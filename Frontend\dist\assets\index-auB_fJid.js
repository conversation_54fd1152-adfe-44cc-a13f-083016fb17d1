import{cb as f}from"./index-oGr_1a_A.js";var p={exports:{}},n,u;function m(){if(u)return n;u=1;var t="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return n=t,n}var a,y;function P(){if(y)return a;y=1;var t=m();function i(){}function s(){}return s.resetWarningCache=i,a=function(){function e(v,S,g,_,b,T){if(T!==t){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}e.isRequired=e;function r(){return e}var o={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:r,element:e,elementType:e,instanceOf:r,node:e,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r,checkPropTypes:s,resetWarningCache:i};return o.PropTypes=o,o},a}var h;function l(){return h||(h=1,p.exports=P()()),p.exports}var R=l();const O=f(R);export{O as P};
