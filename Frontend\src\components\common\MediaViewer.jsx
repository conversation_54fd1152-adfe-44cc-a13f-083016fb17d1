import React, { useState } from "react";
import { FaTimes, FaDownload, FaExpand, FaCompress } from "react-icons/fa";
import { MdPlayArrow, MdPause } from "react-icons/md";
import SimplePDFViewer from "./SimplePDFViewer";
import DocumentViewer from "./DocumentViewer";
import FullscreenViewer from "./FullscreenViewer";
import "../../styles/MediaViewer.css";

const MediaViewer = ({
  isOpen,
  onClose,
  fileUrl,
  fileName,
  fileType,
  title
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  if (!isOpen) return null;

  // Determine file type from URL or provided type
  const getFileType = () => {
    if (fileType) return fileType.toLowerCase();
    if (!fileUrl) return 'unknown';

    const extension = fileUrl.split('.').pop().toLowerCase();
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac'];
    const documentFormats = [
      'pdf', 'doc', 'docx', 'docm', 'dot', 'dotx', 'dotm',
      'xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'csv',
      'txt', 'rtf', 'odt', 'ods', 'odp', 'odg', 'odf',
      'pages', 'numbers', 'key', 'epub', 'mobi', 'azw', 'azw3'
    ];

    if (videoExtensions.includes(extension)) return 'video';
    if (imageExtensions.includes(extension)) return 'image';
    if (audioExtensions.includes(extension)) return 'audio';
    if (documentFormats.includes(extension)) return 'document';

    return 'unknown';
  };

  const mediaType = getFileType();

  const handleDownload = () => {
    // Download functionality disabled for security purposes
    console.warn('Download functionality has been disabled for security purposes');
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const renderMedia = () => {
    switch (mediaType) {
      case 'video':
        return (
          <div className="media-viewer__video-container">
            <div className="media-viewer__toolbar">
              <button
                className="media-viewer__toolbar-btn"
                onClick={() => setIsFullscreen(true)}
                title="Open in Fullscreen"
              >
                <FaExpand /> Fullscreen
              </button>
            </div>
            <video
              className="media-viewer__video"
              controls
              autoPlay={false}
              controlsList="nodownload"
              disablePictureInPicture
            >
              <source src={fileUrl} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
          </div>
        );

      case 'audio':
        return (
          <div className="media-viewer__audio-container">
            <div className="media-viewer__audio-info">
              <h3>{fileName || title || 'Audio File'}</h3>
              <p>Audio Content</p>
            </div>
            <audio
              className="media-viewer__audio"
              controls
            >
              <source src={fileUrl} type="audio/mpeg" />
              Your browser does not support the audio tag.
            </audio>
          </div>
        );

      case 'image':
        return (
          <img
            className="media-viewer__image"
            src={fileUrl}
            alt={fileName || title || 'Image'}
          />
        );

      case 'document':
        return (
          <DocumentViewer
            fileUrl={fileUrl}
            fileName={fileName || title || 'Document'}
            title={title || 'Document'}
            className="media-viewer__document"
            height="100%"
            showDownload={false}
            onDownload={handleDownload}
          />
        );

      default:
        return (
          <div className="media-viewer__unsupported">
            <p>Unsupported media type</p>
          </div>
        );
    }
  };

  return (
    <>
      <div className="media-viewer__overlay" onClick={handleOverlayClick}>
        <div className="media-viewer__container">
          <div className="media-viewer__header">
            <h3 className="media-viewer__title">{title || fileName || 'Media Preview'}</h3>
            <button
              className="media-viewer__close-btn"
              onClick={onClose}
              title="Close"
            >
              <FaTimes />
            </button>
          </div>
          <div className="media-viewer__content">
            {renderMedia()}
          </div>
        </div>
      </div>

      {/* Fullscreen Viewer for Videos */}
      {mediaType === 'video' && (
        <FullscreenViewer
          isOpen={isFullscreen}
          onClose={() => setIsFullscreen(false)}
          contentType="video"
          content={fileUrl}
          videoControls={true}
        />
      )}
    </>
  );
};

export default MediaViewer;
