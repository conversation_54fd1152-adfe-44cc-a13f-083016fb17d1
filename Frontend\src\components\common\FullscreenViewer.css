.fullscreen-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.fullscreen-viewer-container {
  position: relative;
  width: 90vw;
  height: 90vh;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.fullscreen-close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 40px;
  height: 40px;
  border: none;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  transition: background-color 0.2s;
}

.fullscreen-close-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

/* PDF Viewer Styles */
.pdf-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  overflow: auto;
  background-color: #f5f5f5;
  touch-action: pan-y pinch-zoom;
}

.pdf-controls {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 20px;
  z-index: 1;
}

.pdf-controls button {
  background-color: transparent;
  border: 1px solid white;
  color: white;
  padding: 5px 15px;
  border-radius: 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pdf-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pdf-controls button:not(:disabled):hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.pdf-controls span {
  color: white;
  display: flex;
  align-items: center;
}

.zoom-controls {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 20px;
  z-index: 1;
}

.zoom-controls button {
  background-color: transparent;
  border: 1px solid white;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s;
}

.zoom-controls button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.zoom-controls span {
  color: white;
  text-align: center;
}

/* Video Viewer Styles */
.video-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: black;
}

.fullscreen-video {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

.loading {
  color: #666;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .fullscreen-viewer-container {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }

  .pdf-controls {
    bottom: 10px;
    padding: 8px 15px;
  }

  .pdf-controls button {
    padding: 4px 12px;
  }

  .zoom-controls {
    right: 10px;
    padding: 8px;
  }

  .zoom-controls button {
    width: 25px;
    height: 25px;
  }

  .fullscreen-close-button {
    top: 5px;
    right: 5px;
    width: 35px;
    height: 35px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .pdf-container {
    touch-action: manipulation;
  }

  .pdf-controls button,
  .zoom-controls button {
    min-height: 44px;
    min-width: 44px;
  }
} 