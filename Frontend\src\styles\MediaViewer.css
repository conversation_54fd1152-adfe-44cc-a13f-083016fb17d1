/* Media Viewer Styles */
.media-viewer__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.media-viewer__container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.media-viewer__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color);
}

.media-viewer__title {
  margin: 0;
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-viewer__close-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  color: var(--text-color);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.media-viewer__close-btn:hover {
  background-color: var(--bg-gray);
  color: var(--primary-color);
}

.media-viewer__content {
  flex: 1;
  overflow: auto;
  position: relative;
  background-color: var(--bg-gray);
  -webkit-overflow-scrolling: touch;
}

/* Video Container Styles */
.media-viewer__video-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: black;
}

.media-viewer__toolbar {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
  display: flex;
  gap: 8px;
  padding: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: var(--border-radius);
  backdrop-filter: blur(4px);
}

.media-viewer__toolbar-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: var(--border-radius);
  background-color: transparent;
  color: var(--white);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.2s ease;
}

.media-viewer__toolbar-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.media-viewer__video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Audio Styles */
.media-viewer__audio-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 20px;
}

.media-viewer__audio-info {
  text-align: center;
}

.media-viewer__audio-info h3 {
  margin: 0 0 8px;
  font-size: var(--heading6);
  color: var(--text-color);
}

.media-viewer__audio-info p {
  margin: 0;
  color: var(--dark-gray);
}

.media-viewer__audio {
  width: 100%;
  max-width: 500px;
}

/* Image Styles */
.media-viewer__image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Document Styles */
.media-viewer__document {
  height: 100%;
}

/* Unsupported Media Styles */
.media-viewer__unsupported {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
  color: var(--dark-gray);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .media-viewer__overlay {
    padding: 0;
  }

  .media-viewer__container {
    height: 100%;
    border-radius: 0;
  }

  .media-viewer__header {
    padding: 12px 16px;
  }

  .media-viewer__title {
    font-size: var(--basefont);
  }

  .media-viewer__toolbar {
    top: 8px;
    right: 8px;
  }

  .media-viewer__toolbar-btn {
    padding: 4px 8px;
    font-size: var(--extrasmallfont);
  }

  .media-viewer__audio-container {
    padding: 20px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .media-viewer__content {
    touch-action: pan-y pinch-zoom;
  }

  .media-viewer__close-btn,
  .media-viewer__toolbar-btn {
    min-width: 44px;
    min-height: 44px;
  }
}
