import{a as h,b as x,r as N,q as g,j as e,w as C,x as j,A as p,y as f,z as v,B as b,C as F,D as w}from"./index-oGr_1a_A.js";const S="data:image/svg+xml,%3csvg%20width='496'%20height='438'%20viewBox='0%200%20496%20438'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M298%20285.5L-21.5%20444H516V-20.5L386.5%20-9L298%20285.5Z'%20fill='%23FADEC6'/%3e%3c/svg%3e",M=()=>{const l=h(),{formData:a,errors:t,isSubmitting:i,isSubmitted:E,isSuccess:o,isError:m,error:d,contactInfo:c}=x(s=>s.contact),n=(s,r)=>{l(w({field:s,value:r}))},_=()=>{const s={};return a.firstName.trim()||(s.firstName="First name is required"),a.lastName.trim()||(s.lastName="Last name is required"),a.email.trim()?/\S+@\S+\.\S+/.test(a.email)||(s.email="Email is invalid"):s.email="Email is required",a.mobile.trim()?/^\+?[\d\s\-\(\)]+$/.test(a.mobile)||(s.mobile="Mobile number is invalid"):s.mobile="Mobile number is required",a.message.trim()?a.message.trim().length<10&&(s.message="Message must be at least 10 characters"):s.message="Message is required",s},u=async s=>{s.preventDefault(),l(v());const r=_();if(Object.keys(r).length>0){l(b(r));return}l(F(a))};return N.useEffect(()=>{if(o||m){const s=setTimeout(()=>{l(g())},5e3);return()=>clearTimeout(s)}},[o,m,l]),e.jsx(e.Fragment,{children:e.jsx("div",{className:"Contact",children:e.jsx("div",{className:"Contact__container",children:e.jsxs("div",{className:"Contact__content",children:[e.jsxs("div",{className:"Contact__info-section",children:[e.jsx("div",{className:"Contact__info-section-img",children:e.jsx("img",{src:S,alt:""})}),e.jsxs("div",{className:"relativecss",children:[e.jsx("h2",{className:"Contact__info-title",children:"Contact Us"}),e.jsxs("div",{className:"Contact__mail-section",children:[e.jsx("h3",{className:"Contact__mail-title",children:"Mail Us"}),e.jsxs("div",{className:"Contact__email",children:[e.jsx(C,{className:"Contact__email-icon"}),e.jsx("span",{children:c.email})]})]}),e.jsxs("div",{className:"Contact__follow-section",children:[e.jsx("h3",{className:"Contact__follow-title",children:"Follow Us"}),e.jsxs("div",{className:"Contact__social-icons",children:[e.jsx("a",{href:c.socialLinks.facebook,className:"Contact__social-icon","aria-label":"Facebook",children:e.jsx(j,{})}),e.jsx("a",{href:c.socialLinks.instagram,className:"Contact__social-icon","aria-label":"Instagram",children:e.jsx(p,{})}),e.jsx("a",{href:c.socialLinks.twitter,className:"Contact__social-icon","aria-label":"Twitter",children:e.jsx(f,{})})]})]})]})]}),e.jsxs("div",{className:"Contact__form-section",children:[e.jsx("h2",{className:"Contact__form-title",children:"Send A Message"}),o&&e.jsx("div",{className:"Contact__success-message",children:"Thank you for your message! We'll get back to you soon."}),m&&e.jsx("div",{className:"Contact__error-message",children:d||"Failed to send message. Please try again."}),e.jsxs("form",{onSubmit:u,className:"Contact__form",children:[e.jsxs("div",{className:"Contact__form-row",children:[e.jsxs("div",{className:"Contact__input-group",children:[e.jsx("input",{type:"text",placeholder:"First Name",value:a.firstName,onChange:s=>n("firstName",s.target.value),className:"Contact__input",disabled:i}),t.firstName&&e.jsx("span",{className:"Contact__error",children:t.firstName})]}),e.jsxs("div",{className:"Contact__input-group",children:[e.jsx("input",{type:"text",placeholder:"Last Name",value:a.lastName,onChange:s=>n("lastName",s.target.value),className:"Contact__input",disabled:i}),t.lastName&&e.jsx("span",{className:"Contact__error",children:t.lastName})]})]}),e.jsxs("div",{className:"Contact__form-row",children:[e.jsxs("div",{className:"Contact__input-group",children:[e.jsx("input",{type:"email",placeholder:"Email Address",value:a.email,onChange:s=>n("email",s.target.value),className:"Contact__input",disabled:i}),t.email&&e.jsx("span",{className:"Contact__error",children:t.email})]}),e.jsxs("div",{className:"Contact__input-group",children:[e.jsx("input",{type:"tel",placeholder:"Mobile Number",value:a.mobile,onChange:s=>n("mobile",s.target.value),className:"Contact__input",disabled:i}),t.mobile&&e.jsx("span",{className:"Contact__error",children:t.mobile})]})]}),e.jsxs("div",{className:"Contact__input-group Contact__input-group--full",children:[e.jsx("textarea",{placeholder:"Message",value:a.message,onChange:s=>n("message",s.target.value),className:"Contact__textarea",disabled:i}),t.message&&e.jsx("span",{className:"Contact__error",children:t.message})]}),e.jsx("button",{type:"submit",className:"Contact__submit-btn",disabled:i,children:i?"Sending...":"Send Message"})]})]})]})})})})};export{M as default};
