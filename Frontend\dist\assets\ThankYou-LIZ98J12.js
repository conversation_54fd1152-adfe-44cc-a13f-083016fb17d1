const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.es-JPZNIsat.js","assets/index-oGr_1a_A.js","assets/index-BicLFhOs.css","assets/default-CtJ0YSQs.js","assets/thankyou-hv9It-UO.js","assets/timezoneUtils-Dky5bF8c.js"])))=>i.map(i=>d[i]);
import{_ as Cs,u as ph,i as gh,a as mh,r as gs,b as vh,I as bh,j as Tt,J as yh,K as wh,N as xh,O as Ah,P as Nh}from"./index-oGr_1a_A.js";import{m as xl,d as ms,u as Lh,j as Sh,a as Ph,b as _h,c as kh,v as jh}from"./default-CtJ0YSQs.js";import{T as Ch}from"./thankyou-hv9It-UO.js";import{f as uo}from"./timezoneUtils-Dky5bF8c.js";function de(r){"@babel/helpers - typeof";return de=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},de(r)}var er=Uint8Array,vr=Uint16Array,Vs=Int32Array,No=new er([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),Lo=new er([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Fs=new er([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Kl=function(r,t){for(var e=new vr(31),i=0;i<31;++i)e[i]=t+=1<<r[i-1];for(var o=new Vs(e[30]),i=1;i<30;++i)for(var a=e[i];a<e[i+1];++a)o[a]=a-e[i]<<5|i;return{b:e,r:o}},Xl=Kl(No,2),Ql=Xl.b,Is=Xl.r;Ql[28]=258,Is[258]=28;var Zl=Kl(Lo,0),Fh=Zl.b,Al=Zl.r,Ds=new vr(32768);for(var xe=0;xe<32768;++xe){var Fn=(xe&43690)>>1|(xe&21845)<<1;Fn=(Fn&52428)>>2|(Fn&13107)<<2,Fn=(Fn&61680)>>4|(Fn&3855)<<4,Ds[xe]=((Fn&65280)>>8|(Fn&255)<<8)>>1}var tn=function(r,t,e){for(var i=r.length,o=0,a=new vr(t);o<i;++o)r[o]&&++a[r[o]-1];var l=new vr(t);for(o=1;o<t;++o)l[o]=l[o-1]+a[o-1]<<1;var h;if(e){h=new vr(1<<t);var c=15-t;for(o=0;o<i;++o)if(r[o])for(var d=o<<4|r[o],m=t-r[o],v=l[r[o]-1]++<<m,w=v|(1<<m)-1;v<=w;++v)h[Ds[v]>>c]=d}else for(h=new vr(i),o=0;o<i;++o)r[o]&&(h[o]=Ds[l[r[o]-1]++]>>15-r[o]);return h},Bn=new er(288);for(var xe=0;xe<144;++xe)Bn[xe]=8;for(var xe=144;xe<256;++xe)Bn[xe]=9;for(var xe=256;xe<280;++xe)Bn[xe]=7;for(var xe=280;xe<288;++xe)Bn[xe]=8;var ba=new er(32);for(var xe=0;xe<32;++xe)ba[xe]=5;var Ih=tn(Bn,9,0),Dh=tn(Bn,9,1),Bh=tn(ba,5,0),Oh=tn(ba,5,1),vs=function(r){for(var t=r[0],e=1;e<r.length;++e)r[e]>t&&(t=r[e]);return t},Tr=function(r,t,e){var i=t/8|0;return(r[i]|r[i+1]<<8)>>(t&7)&e},bs=function(r,t){var e=t/8|0;return(r[e]|r[e+1]<<8|r[e+2]<<16)>>(t&7)},Gs=function(r){return(r+7)/8|0},$l=function(r,t,e){return(e==null||e>r.length)&&(e=r.length),new er(r.subarray(t,e))},Mh=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],Rr=function(r,t,e){var i=new Error(t||Mh[r]);if(i.code=r,Error.captureStackTrace&&Error.captureStackTrace(i,Rr),!e)throw i;return i},Th=function(r,t,e,i){var o=r.length,a=0;if(!o||t.f&&!t.l)return e||new er(0);var l=!e,h=l||t.i!=2,c=t.i;l&&(e=new er(o*3));var d=function(At){var jt=e.length;if(At>jt){var Pt=new er(Math.max(jt*2,At));Pt.set(e),e=Pt}},m=t.f||0,v=t.p||0,w=t.b||0,p=t.l,C=t.d,k=t.m,B=t.n,P=o*8;do{if(!p){m=Tr(r,v,1);var O=Tr(r,v+1,3);if(v+=3,O)if(O==1)p=Dh,C=Oh,k=9,B=5;else if(O==2){var yt=Tr(r,v,31)+257,Q=Tr(r,v+10,15)+4,q=yt+Tr(r,v+5,31)+1;v+=14;for(var rt=new er(q),pt=new er(19),_=0;_<Q;++_)pt[Fs[_]]=Tr(r,v+_*3,7);v+=Q*3;for(var j=vs(pt),V=(1<<j)-1,R=tn(pt,j,1),_=0;_<q;){var ot=R[Tr(r,v,V)];v+=ot&15;var E=ot>>4;if(E<16)rt[_++]=E;else{var nt=0,ut=0;for(E==16?(ut=3+Tr(r,v,3),v+=2,nt=rt[_-1]):E==17?(ut=3+Tr(r,v,7),v+=3):E==18&&(ut=11+Tr(r,v,127),v+=7);ut--;)rt[_++]=nt}}var Z=rt.subarray(0,yt),ht=rt.subarray(yt);k=vs(Z),B=vs(ht),p=tn(Z,k,1),C=tn(ht,B,1)}else Rr(1);else{var E=Gs(v)+4,K=r[E-4]|r[E-3]<<8,st=E+K;if(st>o){c&&Rr(0);break}h&&d(w+K),e.set(r.subarray(E,st),w),t.b=w+=K,t.p=v=st*8,t.f=m;continue}if(v>P){c&&Rr(0);break}}h&&d(w+131072);for(var dt=(1<<k)-1,It=(1<<B)-1,N=v;;N=v){var nt=p[bs(r,v)&dt],I=nt>>4;if(v+=nt&15,v>P){c&&Rr(0);break}if(nt||Rr(2),I<256)e[w++]=I;else if(I==256){N=v,p=null;break}else{var M=I-254;if(I>264){var _=I-257,U=No[_];M=Tr(r,v,(1<<U)-1)+Ql[_],v+=U}var Y=C[bs(r,v)&It],et=Y>>4;Y||Rr(3),v+=Y&15;var ht=Fh[et];if(et>3){var U=Lo[et];ht+=bs(r,v)&(1<<U)-1,v+=U}if(v>P){c&&Rr(0);break}h&&d(w+131072);var it=w+M;if(w<ht){var at=a-ht,Nt=Math.min(ht,it);for(at+w<0&&Rr(3);w<Nt;++w)e[w]=i[at+w]}for(;w<it;++w)e[w]=e[w-ht]}}t.l=p,t.p=N,t.b=w,t.f=m,p&&(m=1,t.m=k,t.d=C,t.n=B)}while(!m);return w!=e.length&&l?$l(e,0,w):e.subarray(0,w)},pn=function(r,t,e){e<<=t&7;var i=t/8|0;r[i]|=e,r[i+1]|=e>>8},pa=function(r,t,e){e<<=t&7;var i=t/8|0;r[i]|=e,r[i+1]|=e>>8,r[i+2]|=e>>16},ys=function(r,t){for(var e=[],i=0;i<r.length;++i)r[i]&&e.push({s:i,f:r[i]});var o=e.length,a=e.slice();if(!o)return{t:eu,l:0};if(o==1){var l=new er(e[0].s+1);return l[e[0].s]=1,{t:l,l:1}}e.sort(function(st,yt){return st.f-yt.f}),e.push({s:-1,f:25001});var h=e[0],c=e[1],d=0,m=1,v=2;for(e[0]={s:-1,f:h.f+c.f,l:h,r:c};m!=o-1;)h=e[e[d].f<e[v].f?d++:v++],c=e[d!=m&&e[d].f<e[v].f?d++:v++],e[m++]={s:-1,f:h.f+c.f,l:h,r:c};for(var w=a[0].s,i=1;i<o;++i)a[i].s>w&&(w=a[i].s);var p=new vr(w+1),C=Bs(e[m-1],p,0);if(C>t){var i=0,k=0,B=C-t,P=1<<B;for(a.sort(function(yt,Q){return p[Q.s]-p[yt.s]||yt.f-Q.f});i<o;++i){var O=a[i].s;if(p[O]>t)k+=P-(1<<C-p[O]),p[O]=t;else break}for(k>>=B;k>0;){var E=a[i].s;p[E]<t?k-=1<<t-p[E]++-1:++i}for(;i>=0&&k;--i){var K=a[i].s;p[K]==t&&(--p[K],++k)}C=t}return{t:new er(p),l:C}},Bs=function(r,t,e){return r.s==-1?Math.max(Bs(r.l,t,e+1),Bs(r.r,t,e+1)):t[r.s]=e},Nl=function(r){for(var t=r.length;t&&!r[--t];);for(var e=new vr(++t),i=0,o=r[0],a=1,l=function(c){e[i++]=c},h=1;h<=t;++h)if(r[h]==o&&h!=t)++a;else{if(!o&&a>2){for(;a>138;a-=138)l(32754);a>2&&(l(a>10?a-11<<5|28690:a-3<<5|12305),a=0)}else if(a>3){for(l(o),--a;a>6;a-=6)l(8304);a>2&&(l(a-3<<5|8208),a=0)}for(;a--;)l(o);a=1,o=r[h]}return{c:e.subarray(0,i),n:t}},ga=function(r,t){for(var e=0,i=0;i<t.length;++i)e+=r[i]*t[i];return e},tu=function(r,t,e){var i=e.length,o=Gs(t+2);r[o]=i&255,r[o+1]=i>>8,r[o+2]=r[o]^255,r[o+3]=r[o+1]^255;for(var a=0;a<i;++a)r[o+a+4]=e[a];return(o+4+i)*8},Ll=function(r,t,e,i,o,a,l,h,c,d,m){pn(t,m++,e),++o[256];for(var v=ys(o,15),w=v.t,p=v.l,C=ys(a,15),k=C.t,B=C.l,P=Nl(w),O=P.c,E=P.n,K=Nl(k),st=K.c,yt=K.n,Q=new vr(19),q=0;q<O.length;++q)++Q[O[q]&31];for(var q=0;q<st.length;++q)++Q[st[q]&31];for(var rt=ys(Q,7),pt=rt.t,_=rt.l,j=19;j>4&&!pt[Fs[j-1]];--j);var V=d+5<<3,R=ga(o,Bn)+ga(a,ba)+l,ot=ga(o,w)+ga(a,k)+l+14+3*j+ga(Q,pt)+2*Q[16]+3*Q[17]+7*Q[18];if(c>=0&&V<=R&&V<=ot)return tu(t,m,r.subarray(c,c+d));var nt,ut,Z,ht;if(pn(t,m,1+(ot<R)),m+=2,ot<R){nt=tn(w,p,0),ut=w,Z=tn(k,B,0),ht=k;var dt=tn(pt,_,0);pn(t,m,E-257),pn(t,m+5,yt-1),pn(t,m+10,j-4),m+=14;for(var q=0;q<j;++q)pn(t,m+3*q,pt[Fs[q]]);m+=3*j;for(var It=[O,st],N=0;N<2;++N)for(var I=It[N],q=0;q<I.length;++q){var M=I[q]&31;pn(t,m,dt[M]),m+=pt[M],M>15&&(pn(t,m,I[q]>>5&127),m+=I[q]>>12)}}else nt=Ih,ut=Bn,Z=Bh,ht=ba;for(var q=0;q<h;++q){var U=i[q];if(U>255){var M=U>>18&31;pa(t,m,nt[M+257]),m+=ut[M+257],M>7&&(pn(t,m,U>>23&31),m+=No[M]);var Y=U&31;pa(t,m,Z[Y]),m+=ht[Y],Y>3&&(pa(t,m,U>>5&8191),m+=Lo[Y])}else pa(t,m,nt[U]),m+=ut[U]}return pa(t,m,nt[256]),m+ut[256]},Eh=new Vs([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),eu=new er(0),Rh=function(r,t,e,i,o,a){var l=a.z||r.length,h=new er(i+l+5*(1+Math.ceil(l/7e3))+o),c=h.subarray(i,h.length-o),d=a.l,m=(a.r||0)&7;if(t){m&&(c[0]=a.r>>3);for(var v=Eh[t-1],w=v>>13,p=v&8191,C=(1<<e)-1,k=a.p||new vr(32768),B=a.h||new vr(C+1),P=Math.ceil(e/3),O=2*P,E=function(Ht){return(r[Ht]^r[Ht+1]<<P^r[Ht+2]<<O)&C},K=new Vs(25e3),st=new vr(288),yt=new vr(32),Q=0,q=0,rt=a.i||0,pt=0,_=a.w||0,j=0;rt+2<l;++rt){var V=E(rt),R=rt&32767,ot=B[V];if(k[R]=ot,B[V]=R,_<=rt){var nt=l-rt;if((Q>7e3||pt>24576)&&(nt>423||!d)){m=Ll(r,c,0,K,st,yt,q,pt,j,rt-j,m),pt=Q=q=0,j=rt;for(var ut=0;ut<286;++ut)st[ut]=0;for(var ut=0;ut<30;++ut)yt[ut]=0}var Z=2,ht=0,dt=p,It=R-ot&32767;if(nt>2&&V==E(rt-It))for(var N=Math.min(w,nt)-1,I=Math.min(32767,rt),M=Math.min(258,nt);It<=I&&--dt&&R!=ot;){if(r[rt+Z]==r[rt+Z-It]){for(var U=0;U<M&&r[rt+U]==r[rt+U-It];++U);if(U>Z){if(Z=U,ht=It,U>N)break;for(var Y=Math.min(It,U-2),et=0,ut=0;ut<Y;++ut){var it=rt-It+ut&32767,at=k[it],Nt=it-at&32767;Nt>et&&(et=Nt,ot=it)}}}R=ot,ot=k[R],It+=R-ot&32767}if(ht){K[pt++]=268435456|Is[Z]<<18|Al[ht];var At=Is[Z]&31,jt=Al[ht]&31;q+=No[At]+Lo[jt],++st[257+At],++yt[jt],_=rt+Z,++Q}else K[pt++]=r[rt],++st[r[rt]]}}for(rt=Math.max(rt,_);rt<l;++rt)K[pt++]=r[rt],++st[r[rt]];m=Ll(r,c,d,K,st,yt,q,pt,j,rt-j,m),d||(a.r=m&7|c[m/8|0]<<3,m-=7,a.h=B,a.p=k,a.i=rt,a.w=_)}else{for(var rt=a.w||0;rt<l+d;rt+=65535){var Pt=rt+65535;Pt>=l&&(c[m/8|0]=d,Pt=l),m=tu(c,m+1,r.subarray(rt,Pt))}a.i=l}return $l(h,0,i+Gs(m)+o)},ru=function(){var r=1,t=0;return{p:function(e){for(var i=r,o=t,a=e.length|0,l=0;l!=a;){for(var h=Math.min(l+2655,a);l<h;++l)o+=i+=e[l];i=(i&65535)+15*(i>>16),o=(o&65535)+15*(o>>16)}r=i,t=o},d:function(){return r%=65521,t%=65521,(r&255)<<24|(r&65280)<<8|(t&255)<<8|t>>8}}},qh=function(r,t,e,i,o){if(!o&&(o={l:1},t.dictionary)){var a=t.dictionary.subarray(-32768),l=new er(a.length+r.length);l.set(a),l.set(r,a.length),r=l,o.w=a.length}return Rh(r,t.level==null?6:t.level,t.mem==null?o.l?Math.ceil(Math.max(8,Math.min(13,Math.log(r.length)))*1.5):20:12+t.mem,e,i,o)},nu=function(r,t,e){for(;e;++t)r[t]=e,e>>>=8},zh=function(r,t){var e=t.level,i=e==0?0:e<6?1:e==9?3:2;if(r[0]=120,r[1]=i<<6|(t.dictionary&&32),r[1]|=31-(r[0]<<8|r[1])%31,t.dictionary){var o=ru();o.p(t.dictionary),nu(r,2,o.d())}},Uh=function(r,t){return((r[0]&15)!=8||r[0]>>4>7||(r[0]<<8|r[1])%31)&&Rr(6,"invalid zlib data"),(r[1]>>5&1)==1&&Rr(6,"invalid zlib data: "+(r[1]&32?"need":"unexpected")+" dictionary"),(r[1]>>3&4)+2};function Os(r,t){t||(t={});var e=ru();e.p(r);var i=qh(r,t,t.dictionary?6:2,4);return zh(i,t),nu(i,i.length-4,e.d()),i}function Hh(r,t){return Th(r.subarray(Uh(r),-4),{i:2},t,t)}var Wh=typeof TextDecoder<"u"&&new TextDecoder,Vh=0;try{Wh.decode(eu,{stream:!0}),Vh=1}catch{}var Wt=function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this}();function ws(){Wt.console&&typeof Wt.console.log=="function"&&Wt.console.log.apply(Wt.console,arguments)}var be={log:ws,warn:function(r){Wt.console&&(typeof Wt.console.warn=="function"?Wt.console.warn.apply(Wt.console,arguments):ws.call(null,arguments))},error:function(r){Wt.console&&(typeof Wt.console.error=="function"?Wt.console.error.apply(Wt.console,arguments):ws(r))}};function xs(r,t,e){var i=new XMLHttpRequest;i.open("GET",r),i.responseType="blob",i.onload=function(){Kn(i.response,t,e)},i.onerror=function(){be.error("could not download file")},i.send()}function Sl(r){var t=new XMLHttpRequest;t.open("HEAD",r,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function ho(r){try{r.dispatchEvent(new MouseEvent("click"))}catch{var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),r.dispatchEvent(t)}}var ma,Ms,Kn=Wt.saveAs||((typeof window>"u"?"undefined":de(window))!=="object"||window!==Wt?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(r,t,e){var i=Wt.URL||Wt.webkitURL,o=document.createElement("a");t=t||r.name||"download",o.download=t,o.rel="noopener",typeof r=="string"?(o.href=r,o.origin!==location.origin?Sl(o.href)?xs(r,t,e):ho(o,o.target="_blank"):ho(o)):(o.href=i.createObjectURL(r),setTimeout(function(){i.revokeObjectURL(o.href)},4e4),setTimeout(function(){ho(o)},0))}:"msSaveOrOpenBlob"in navigator?function(r,t,e){if(t=t||r.name||"download",typeof r=="string")if(Sl(r))xs(r,t,e);else{var i=document.createElement("a");i.href=r,i.target="_blank",setTimeout(function(){ho(i)})}else navigator.msSaveOrOpenBlob(function(o,a){return a===void 0?a={autoBom:!1}:de(a)!=="object"&&(be.warn("Deprecated: Expected third argument to be a object"),a={autoBom:!a}),a.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(o.type)?new Blob(["\uFEFF",o],{type:o.type}):o}(r,e),t)}:function(r,t,e,i){if((i=i||open("","_blank"))&&(i.document.title=i.document.body.innerText="downloading..."),typeof r=="string")return xs(r,t,e);var o=r.type==="application/octet-stream",a=/constructor/i.test(Wt.HTMLElement)||Wt.safari,l=/CriOS\/[\d]+/.test(navigator.userAgent);if((l||o&&a)&&(typeof FileReader>"u"?"undefined":de(FileReader))==="object"){var h=new FileReader;h.onloadend=function(){var m=h.result;m=l?m:m.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=m:location=m,i=null},h.readAsDataURL(r)}else{var c=Wt.URL||Wt.webkitURL,d=c.createObjectURL(r);i?i.location=d:location.href=d,i=null,setTimeout(function(){c.revokeObjectURL(d)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function iu(r){var t;r=r||"",this.ok=!1,r.charAt(0)=="#"&&(r=r.substr(1,6)),r={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[r=(r=r.replace(/ /g,"")).toLowerCase()]||r;for(var e=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(h){return[parseInt(h[1]),parseInt(h[2]),parseInt(h[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(h){return[parseInt(h[1],16),parseInt(h[2],16),parseInt(h[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(h){return[parseInt(h[1]+h[1],16),parseInt(h[2]+h[2],16),parseInt(h[3]+h[3],16)]}}],i=0;i<e.length;i++){var o=e[i].re,a=e[i].process,l=o.exec(r);l&&(t=a(l),this.r=t[0],this.g=t[1],this.b=t[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var h=this.r.toString(16),c=this.g.toString(16),d=this.b.toString(16);return h.length==1&&(h="0"+h),c.length==1&&(c="0"+c),d.length==1&&(d="0"+d),"#"+h+c+d}}/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function As(r,t){var e=r[0],i=r[1],o=r[2],a=r[3];e=Qe(e,i,o,a,t[0],7,-680876936),a=Qe(a,e,i,o,t[1],12,-389564586),o=Qe(o,a,e,i,t[2],17,606105819),i=Qe(i,o,a,e,t[3],22,-**********),e=Qe(e,i,o,a,t[4],7,-176418897),a=Qe(a,e,i,o,t[5],12,**********),o=Qe(o,a,e,i,t[6],17,-**********),i=Qe(i,o,a,e,t[7],22,-45705983),e=Qe(e,i,o,a,t[8],7,**********),a=Qe(a,e,i,o,t[9],12,-**********),o=Qe(o,a,e,i,t[10],17,-42063),i=Qe(i,o,a,e,t[11],22,-**********),e=Qe(e,i,o,a,t[12],7,**********),a=Qe(a,e,i,o,t[13],12,-40341101),o=Qe(o,a,e,i,t[14],17,-**********),e=Ze(e,i=Qe(i,o,a,e,t[15],22,**********),o,a,t[1],5,-165796510),a=Ze(a,e,i,o,t[6],9,-**********),o=Ze(o,a,e,i,t[11],14,643717713),i=Ze(i,o,a,e,t[0],20,-373897302),e=Ze(e,i,o,a,t[5],5,-701558691),a=Ze(a,e,i,o,t[10],9,38016083),o=Ze(o,a,e,i,t[15],14,-660478335),i=Ze(i,o,a,e,t[4],20,-405537848),e=Ze(e,i,o,a,t[9],5,568446438),a=Ze(a,e,i,o,t[14],9,-1019803690),o=Ze(o,a,e,i,t[3],14,-187363961),i=Ze(i,o,a,e,t[8],20,1163531501),e=Ze(e,i,o,a,t[13],5,-1444681467),a=Ze(a,e,i,o,t[2],9,-51403784),o=Ze(o,a,e,i,t[7],14,1735328473),e=$e(e,i=Ze(i,o,a,e,t[12],20,-1926607734),o,a,t[5],4,-378558),a=$e(a,e,i,o,t[8],11,-2022574463),o=$e(o,a,e,i,t[11],16,1839030562),i=$e(i,o,a,e,t[14],23,-35309556),e=$e(e,i,o,a,t[1],4,-1530992060),a=$e(a,e,i,o,t[4],11,1272893353),o=$e(o,a,e,i,t[7],16,-155497632),i=$e(i,o,a,e,t[10],23,-1094730640),e=$e(e,i,o,a,t[13],4,681279174),a=$e(a,e,i,o,t[0],11,-358537222),o=$e(o,a,e,i,t[3],16,-722521979),i=$e(i,o,a,e,t[6],23,76029189),e=$e(e,i,o,a,t[9],4,-640364487),a=$e(a,e,i,o,t[12],11,-421815835),o=$e(o,a,e,i,t[15],16,530742520),e=tr(e,i=$e(i,o,a,e,t[2],23,-995338651),o,a,t[0],6,-198630844),a=tr(a,e,i,o,t[7],10,1126891415),o=tr(o,a,e,i,t[14],15,-1416354905),i=tr(i,o,a,e,t[5],21,-57434055),e=tr(e,i,o,a,t[12],6,1700485571),a=tr(a,e,i,o,t[3],10,-1894986606),o=tr(o,a,e,i,t[10],15,-1051523),i=tr(i,o,a,e,t[1],21,-2054922799),e=tr(e,i,o,a,t[8],6,1873313359),a=tr(a,e,i,o,t[15],10,-30611744),o=tr(o,a,e,i,t[6],15,-1560198380),i=tr(i,o,a,e,t[13],21,1309151649),e=tr(e,i,o,a,t[4],6,-145523070),a=tr(a,e,i,o,t[11],10,-1120210379),o=tr(o,a,e,i,t[2],15,718787259),i=tr(i,o,a,e,t[9],21,-343485551),r[0]=Dn(e,r[0]),r[1]=Dn(i,r[1]),r[2]=Dn(o,r[2]),r[3]=Dn(a,r[3])}function So(r,t,e,i,o,a){return t=Dn(Dn(t,r),Dn(i,a)),Dn(t<<o|t>>>32-o,e)}function Qe(r,t,e,i,o,a,l){return So(t&e|~t&i,r,t,o,a,l)}function Ze(r,t,e,i,o,a,l){return So(t&i|e&~i,r,t,o,a,l)}function $e(r,t,e,i,o,a,l){return So(t^e^i,r,t,o,a,l)}function tr(r,t,e,i,o,a,l){return So(e^(t|~i),r,t,o,a,l)}function au(r){var t,e=r.length,i=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=r.length;t+=64)As(i,Gh(r.substring(t-64,t)));r=r.substring(t-64);var o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<r.length;t++)o[t>>2]|=r.charCodeAt(t)<<(t%4<<3);if(o[t>>2]|=128<<(t%4<<3),t>55)for(As(i,o),t=0;t<16;t++)o[t]=0;return o[14]=8*e,As(i,o),i}function Gh(r){var t,e=[];for(t=0;t<64;t+=4)e[t>>2]=r.charCodeAt(t)+(r.charCodeAt(t+1)<<8)+(r.charCodeAt(t+2)<<16)+(r.charCodeAt(t+3)<<24);return e}ma=Wt.atob.bind(Wt),Ms=Wt.btoa.bind(Wt);var Pl="0123456789abcdef".split("");function Jh(r){for(var t="",e=0;e<4;e++)t+=Pl[r>>8*e+4&15]+Pl[r>>8*e&15];return t}function Yh(r){return String.fromCharCode((255&r)>>0,(65280&r)>>8,(16711680&r)>>16,(**********&r)>>24)}function Ts(r){return au(r).map(Yh).join("")}var Kh=function(r){for(var t=0;t<r.length;t++)r[t]=Jh(r[t]);return r.join("")}(au("hello"))!="5d41402abc4b2a76b9719d911017c592";function Dn(r,t){if(Kh){var e=(65535&r)+(65535&t);return(r>>16)+(t>>16)+(e>>16)<<16|65535&e}return r+t&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function Es(r,t){var e,i,o,a;if(r!==e){for(var l=(o=r,a=1+(256/r.length>>0),new Array(a+1).join(o)),h=[],c=0;c<256;c++)h[c]=c;var d=0;for(c=0;c<256;c++){var m=h[c];d=(d+m+l.charCodeAt(c))%256,h[c]=h[d],h[d]=m}e=r,i=h}else h=i;var v=t.length,w=0,p=0,C="";for(c=0;c<v;c++)p=(p+(m=h[w=(w+1)%256]))%256,h[w]=h[p],h[p]=m,l=h[(h[w]+h[p])%256],C+=String.fromCharCode(t.charCodeAt(c)^l);return C}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var _l={print:4,modify:8,copy:16,"annot-forms":32};function Ci(r,t,e,i){this.v=1,this.r=2;var o=192;r.forEach(function(h){if(_l.perm!==void 0)throw new Error("Invalid permission: "+h);o+=_l[h]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var a=(t+this.padding).substr(0,32),l=(e+this.padding).substr(0,32);this.O=this.processOwnerPassword(a,l),this.P=-(1+(255^o)),this.encryptionKey=Ts(a+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(i)).substr(0,5),this.U=Es(this.encryptionKey,this.padding)}function Fi(r){if(/[^\u0000-\u00ff]/.test(r))throw new Error("Invalid PDF Name Object: "+r+", Only accept ASCII characters.");for(var t="",e=r.length,i=0;i<e;i++){var o=r.charCodeAt(i);o<33||o===35||o===37||o===40||o===41||o===47||o===60||o===62||o===91||o===93||o===123||o===125||o>126?t+="#"+("0"+o.toString(16)).slice(-2):t+=r[i]}return t}function kl(r){if(de(r)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var t={};this.subscribe=function(e,i,o){if(o=o||!1,typeof e!="string"||typeof i!="function"||typeof o!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");t.hasOwnProperty(e)||(t[e]={});var a=Math.random().toString(35);return t[e][a]=[i,!!o],a},this.unsubscribe=function(e){for(var i in t)if(t[i][e])return delete t[i][e],Object.keys(t[i]).length===0&&delete t[i],!0;return!1},this.publish=function(e){if(t.hasOwnProperty(e)){var i=Array.prototype.slice.call(arguments,1),o=[];for(var a in t[e]){var l=t[e][a];try{l[0].apply(r,i)}catch(h){Wt.console&&be.error("jsPDF PubSub Error",h.message,h)}l[1]&&o.push(a)}o.length&&o.forEach(this.unsubscribe)}},this.getTopics=function(){return t}}function yo(r){if(!(this instanceof yo))return new yo(r);var t="opacity,stroke-opacity".split(",");for(var e in r)r.hasOwnProperty(e)&&t.indexOf(e)>=0&&(this[e]=r[e]);this.id="",this.objectNumber=-1}function ou(r,t){this.gState=r,this.matrix=t,this.id="",this.objectNumber=-1}function Xn(r,t,e,i,o){if(!(this instanceof Xn))return new Xn(r,t,e,i,o);this.type=r==="axial"?2:3,this.coords=t,this.colors=e,ou.call(this,i,o)}function Ii(r,t,e,i,o){if(!(this instanceof Ii))return new Ii(r,t,e,i,o);this.boundingBox=r,this.xStep=t,this.yStep=e,this.stream="",this.cloneIndex=0,ou.call(this,i,o)}function Ut(r){var t,e=typeof arguments[0]=="string"?arguments[0]:"p",i=arguments[1],o=arguments[2],a=arguments[3],l=[],h=1,c=16,d="S",m=null;de(r=r||{})==="object"&&(e=r.orientation,i=r.unit||i,o=r.format||o,a=r.compress||r.compressPdf||a,(m=r.encryption||null)!==null&&(m.userPassword=m.userPassword||"",m.ownerPassword=m.ownerPassword||"",m.userPermissions=m.userPermissions||[]),h=typeof r.userUnit=="number"?Math.abs(r.userUnit):1,r.precision!==void 0&&(t=r.precision),r.floatPrecision!==void 0&&(c=r.floatPrecision),d=r.defaultPathOperation||"S"),l=r.filters||(a===!0?["FlateEncode"]:l),i=i||"mm",e=(""+(e||"P")).toLowerCase();var v=r.putOnlyUsedFonts||!1,w={},p={internal:{},__private__:{}};p.__private__.PubSub=kl;var C="1.3",k=p.__private__.getPdfVersion=function(){return C};p.__private__.setPdfVersion=function(u){C=u};var B={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};p.__private__.getPageFormats=function(){return B};var P=p.__private__.getPageFormat=function(u){return B[u]};o=o||"a4";var O={COMPAT:"compat",ADVANCED:"advanced"},E=O.COMPAT;function K(){this.saveGraphicsState(),T(new zt(Ft,0,0,-Ft,0,yn()*Ft).toString()+" cm"),this.setFontSize(this.getFontSize()/Ft),d="n",E=O.ADVANCED}function st(){this.restoreGraphicsState(),d="S",E=O.COMPAT}var yt=p.__private__.combineFontStyleAndFontWeight=function(u,y){if(u=="bold"&&y=="normal"||u=="bold"&&y==400||u=="normal"&&y=="italic"||u=="bold"&&y=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return y&&(u=y==400||y==="normal"?u==="italic"?"italic":"normal":y!=700&&y!=="bold"||u!=="normal"?(y==700?"bold":y)+""+u:"bold"),u};p.advancedAPI=function(u){var y=E===O.COMPAT;return y&&K.call(this),typeof u!="function"||(u(this),y&&st.call(this)),this},p.compatAPI=function(u){var y=E===O.ADVANCED;return y&&st.call(this),typeof u!="function"||(u(this),y&&K.call(this)),this},p.isAdvancedAPI=function(){return E===O.ADVANCED};var Q,q=function(u){if(E!==O.ADVANCED)throw new Error(u+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},rt=p.roundToPrecision=p.__private__.roundToPrecision=function(u,y){var D=t||y;if(isNaN(u)||isNaN(D))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return u.toFixed(D).replace(/0+$/,"")};Q=p.hpf=p.__private__.hpf=typeof c=="number"?function(u){if(isNaN(u))throw new Error("Invalid argument passed to jsPDF.hpf");return rt(u,c)}:c==="smart"?function(u){if(isNaN(u))throw new Error("Invalid argument passed to jsPDF.hpf");return rt(u,u>-1&&u<1?16:5)}:function(u){if(isNaN(u))throw new Error("Invalid argument passed to jsPDF.hpf");return rt(u,16)};var pt=p.f2=p.__private__.f2=function(u){if(isNaN(u))throw new Error("Invalid argument passed to jsPDF.f2");return rt(u,2)},_=p.__private__.f3=function(u){if(isNaN(u))throw new Error("Invalid argument passed to jsPDF.f3");return rt(u,3)},j=p.scale=p.__private__.scale=function(u){if(isNaN(u))throw new Error("Invalid argument passed to jsPDF.scale");return E===O.COMPAT?u*Ft:E===O.ADVANCED?u:void 0},V=function(u){return E===O.COMPAT?yn()-u:E===O.ADVANCED?u:void 0},R=function(u){return j(V(u))};p.__private__.setPrecision=p.setPrecision=function(u){typeof parseInt(u,10)=="number"&&(t=parseInt(u,10))};var ot,nt="00000000000000000000000000000000",ut=p.__private__.getFileId=function(){return nt},Z=p.__private__.setFileId=function(u){return nt=u!==void 0&&/^[a-fA-F0-9]{32}$/.test(u)?u.toUpperCase():nt.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),m!==null&&(Ke=new Ci(m.userPermissions,m.userPassword,m.ownerPassword,nt)),nt};p.setFileId=function(u){return Z(u),this},p.getFileId=function(){return ut()};var ht=p.__private__.convertDateToPDFDate=function(u){var y=u.getTimezoneOffset(),D=y<0?"+":"-",z=Math.floor(Math.abs(y/60)),X=Math.abs(y%60),ct=[D,M(z),"'",M(X),"'"].join("");return["D:",u.getFullYear(),M(u.getMonth()+1),M(u.getDate()),M(u.getHours()),M(u.getMinutes()),M(u.getSeconds()),ct].join("")},dt=p.__private__.convertPDFDateToDate=function(u){var y=parseInt(u.substr(2,4),10),D=parseInt(u.substr(6,2),10)-1,z=parseInt(u.substr(8,2),10),X=parseInt(u.substr(10,2),10),ct=parseInt(u.substr(12,2),10),wt=parseInt(u.substr(14,2),10);return new Date(y,D,z,X,ct,wt,0)},It=p.__private__.setCreationDate=function(u){var y;if(u===void 0&&(u=new Date),u instanceof Date)y=ht(u);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(u))throw new Error("Invalid argument passed to jsPDF.setCreationDate");y=u}return ot=y},N=p.__private__.getCreationDate=function(u){var y=ot;return u==="jsDate"&&(y=dt(ot)),y};p.setCreationDate=function(u){return It(u),this},p.getCreationDate=function(u){return N(u)};var I,M=p.__private__.padd2=function(u){return("0"+parseInt(u)).slice(-2)},U=p.__private__.padd2Hex=function(u){return("00"+(u=u.toString())).substr(u.length)},Y=0,et=[],it=[],at=0,Nt=[],At=[],jt=!1,Pt=it,Ht=function(){Y=0,at=0,it=[],et=[],Nt=[],nn=Oe(),jr=Oe()};p.__private__.setCustomOutputDestination=function(u){jt=!0,Pt=u};var ft=function(u){jt||(Pt=u)};p.__private__.resetCustomOutputDestination=function(){jt=!1,Pt=it};var T=p.__private__.out=function(u){return u=u.toString(),at+=u.length+1,Pt.push(u),Pt},Qt=p.__private__.write=function(u){return T(arguments.length===1?u.toString():Array.prototype.join.call(arguments," "))},Mt=p.__private__.getArrayBuffer=function(u){for(var y=u.length,D=new ArrayBuffer(y),z=new Uint8Array(D);y--;)z[y]=u.charCodeAt(y);return D},xt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];p.__private__.getStandardFonts=function(){return xt};var Lt=r.fontSize||16;p.__private__.setFontSize=p.setFontSize=function(u){return Lt=E===O.ADVANCED?u/Ft:u,this};var Ct,kt=p.__private__.getFontSize=p.getFontSize=function(){return E===O.COMPAT?Lt:Lt*Ft},Et=r.R2L||!1;p.__private__.setR2L=p.setR2L=function(u){return Et=u,this},p.__private__.getR2L=p.getR2L=function(){return Et};var Jt,te=p.__private__.setZoomMode=function(u){var y=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(u))Ct=u;else if(isNaN(u)){if(y.indexOf(u)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+u+'" is not recognized.');Ct=u}else Ct=parseInt(u,10)};p.__private__.getZoomMode=function(){return Ct};var ee,ae=p.__private__.setPageMode=function(u){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(u)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+u+'" is not recognized.');Jt=u};p.__private__.getPageMode=function(){return Jt};var pe=p.__private__.setLayoutMode=function(u){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(u)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+u+'" is not recognized.');ee=u};p.__private__.getLayoutMode=function(){return ee},p.__private__.setDisplayMode=p.setDisplayMode=function(u,y,D){return te(u),pe(y),ae(D),this};var Vt={title:"",subject:"",author:"",keywords:"",creator:""};p.__private__.getDocumentProperty=function(u){if(Object.keys(Vt).indexOf(u)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return Vt[u]},p.__private__.getDocumentProperties=function(){return Vt},p.__private__.setDocumentProperties=p.setProperties=p.setDocumentProperties=function(u){for(var y in Vt)Vt.hasOwnProperty(y)&&u[y]&&(Vt[y]=u[y]);return this},p.__private__.setDocumentProperty=function(u,y){if(Object.keys(Vt).indexOf(u)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return Vt[u]=y};var re,Ft,Ye,se,Pr,me={},Ae={},Ur=[],ue={},Mn={},Le={},_r={},rn=null,Se=0,Yt=[],he=new kl(p),Tn=r.hotfixes||[],Ve={},Hr={},Wr=[],zt=function u(y,D,z,X,ct,wt){if(!(this instanceof u))return new u(y,D,z,X,ct,wt);isNaN(y)&&(y=1),isNaN(D)&&(D=0),isNaN(z)&&(z=0),isNaN(X)&&(X=1),isNaN(ct)&&(ct=0),isNaN(wt)&&(wt=0),this._matrix=[y,D,z,X,ct,wt]};Object.defineProperty(zt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(u){this._matrix[0]=u}}),Object.defineProperty(zt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(u){this._matrix[1]=u}}),Object.defineProperty(zt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(u){this._matrix[2]=u}}),Object.defineProperty(zt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(u){this._matrix[3]=u}}),Object.defineProperty(zt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(u){this._matrix[4]=u}}),Object.defineProperty(zt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(u){this._matrix[5]=u}}),Object.defineProperty(zt.prototype,"a",{get:function(){return this._matrix[0]},set:function(u){this._matrix[0]=u}}),Object.defineProperty(zt.prototype,"b",{get:function(){return this._matrix[1]},set:function(u){this._matrix[1]=u}}),Object.defineProperty(zt.prototype,"c",{get:function(){return this._matrix[2]},set:function(u){this._matrix[2]=u}}),Object.defineProperty(zt.prototype,"d",{get:function(){return this._matrix[3]},set:function(u){this._matrix[3]=u}}),Object.defineProperty(zt.prototype,"e",{get:function(){return this._matrix[4]},set:function(u){this._matrix[4]=u}}),Object.defineProperty(zt.prototype,"f",{get:function(){return this._matrix[5]},set:function(u){this._matrix[5]=u}}),Object.defineProperty(zt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(zt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(zt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(zt.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),zt.prototype.join=function(u){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(Q).join(u)},zt.prototype.multiply=function(u){var y=u.sx*this.sx+u.shy*this.shx,D=u.sx*this.shy+u.shy*this.sy,z=u.shx*this.sx+u.sy*this.shx,X=u.shx*this.shy+u.sy*this.sy,ct=u.tx*this.sx+u.ty*this.shx+this.tx,wt=u.tx*this.shy+u.ty*this.sy+this.ty;return new zt(y,D,z,X,ct,wt)},zt.prototype.decompose=function(){var u=this.sx,y=this.shy,D=this.shx,z=this.sy,X=this.tx,ct=this.ty,wt=Math.sqrt(u*u+y*y),Dt=(u/=wt)*D+(y/=wt)*z;D-=u*Dt,z-=y*Dt;var Rt=Math.sqrt(D*D+z*z);return Dt/=Rt,u*(z/=Rt)<y*(D/=Rt)&&(u=-u,y=-y,Dt=-Dt,wt=-wt),{scale:new zt(wt,0,0,Rt,0,0),translate:new zt(1,0,0,1,X,ct),rotate:new zt(u,y,-y,u,0,0),skew:new zt(1,0,Dt,1,0,0)}},zt.prototype.toString=function(u){return this.join(" ")},zt.prototype.inversed=function(){var u=this.sx,y=this.shy,D=this.shx,z=this.sy,X=this.tx,ct=this.ty,wt=1/(u*z-y*D),Dt=z*wt,Rt=-y*wt,Zt=-D*wt,Kt=u*wt;return new zt(Dt,Rt,Zt,Kt,-Dt*X-Zt*ct,-Rt*X-Kt*ct)},zt.prototype.applyToPoint=function(u){var y=u.x*this.sx+u.y*this.shx+this.tx,D=u.x*this.shy+u.y*this.sy+this.ty;return new fi(y,D)},zt.prototype.applyToRectangle=function(u){var y=this.applyToPoint(u),D=this.applyToPoint(new fi(u.x+u.w,u.y+u.h));return new Yi(y.x,y.y,D.x-y.x,D.y-y.y)},zt.prototype.clone=function(){var u=this.sx,y=this.shy,D=this.shx,z=this.sy,X=this.tx,ct=this.ty;return new zt(u,y,D,z,X,ct)},p.Matrix=zt;var kr=p.matrixMult=function(u,y){return y.multiply(u)},Vr=new zt(1,0,0,1,0,0);p.unitMatrix=p.identityMatrix=Vr;var ar=function(u,y){if(!Mn[u]){var D=(y instanceof Xn?"Sh":"P")+(Object.keys(ue).length+1).toString(10);y.id=D,Mn[u]=D,ue[D]=y,he.publish("addPattern",y)}};p.ShadingPattern=Xn,p.TilingPattern=Ii,p.addShadingPattern=function(u,y){return q("addShadingPattern()"),ar(u,y),this},p.beginTilingPattern=function(u){q("beginTilingPattern()"),Ta(u.boundingBox[0],u.boundingBox[1],u.boundingBox[2]-u.boundingBox[0],u.boundingBox[3]-u.boundingBox[1],u.matrix)},p.endTilingPattern=function(u,y){q("endTilingPattern()"),y.stream=At[I].join(`
`),ar(u,y),he.publish("endTilingPattern",y),Wr.pop().restore()};var Re=p.__private__.newObject=function(){var u=Oe();return fr(u,!0),u},Oe=p.__private__.newObjectDeferred=function(){return Y++,et[Y]=function(){return at},Y},fr=function(u,y){return y=typeof y=="boolean"&&y,et[u]=at,y&&T(u+" 0 obj"),u},$n=p.__private__.newAdditionalObject=function(){var u={objId:Oe(),content:""};return Nt.push(u),u},nn=Oe(),jr=Oe(),Cr=p.__private__.decodeColorString=function(u){var y=u.split(" ");if(y.length!==2||y[1]!=="g"&&y[1]!=="G")y.length===5&&(y[4]==="k"||y[4]==="K")&&(y=[(1-y[0])*(1-y[3]),(1-y[1])*(1-y[3]),(1-y[2])*(1-y[3]),"r"]);else{var D=parseFloat(y[0]);y=[D,D,D,"r"]}for(var z="#",X=0;X<3;X++)z+=("0"+Math.floor(255*parseFloat(y[X])).toString(16)).slice(-2);return z},Fr=p.__private__.encodeColorString=function(u){var y;typeof u=="string"&&(u={ch1:u});var D=u.ch1,z=u.ch2,X=u.ch3,ct=u.ch4,wt=u.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof D=="string"&&D.charAt(0)!=="#"){var Dt=new iu(D);if(Dt.ok)D=Dt.toHex();else if(!/^\d*\.?\d*$/.test(D))throw new Error('Invalid color "'+D+'" passed to jsPDF.encodeColorString.')}if(typeof D=="string"&&/^#[0-9A-Fa-f]{3}$/.test(D)&&(D="#"+D[1]+D[1]+D[2]+D[2]+D[3]+D[3]),typeof D=="string"&&/^#[0-9A-Fa-f]{6}$/.test(D)){var Rt=parseInt(D.substr(1),16);D=Rt>>16&255,z=Rt>>8&255,X=255&Rt}if(z===void 0||ct===void 0&&D===z&&z===X)if(typeof D=="string")y=D+" "+wt[0];else switch(u.precision){case 2:y=pt(D/255)+" "+wt[0];break;case 3:default:y=_(D/255)+" "+wt[0]}else if(ct===void 0||de(ct)==="object"){if(ct&&!isNaN(ct.a)&&ct.a===0)return y=["1.","1.","1.",wt[1]].join(" ");if(typeof D=="string")y=[D,z,X,wt[1]].join(" ");else switch(u.precision){case 2:y=[pt(D/255),pt(z/255),pt(X/255),wt[1]].join(" ");break;default:case 3:y=[_(D/255),_(z/255),_(X/255),wt[1]].join(" ")}}else if(typeof D=="string")y=[D,z,X,ct,wt[2]].join(" ");else switch(u.precision){case 2:y=[pt(D),pt(z),pt(X),pt(ct),wt[2]].join(" ");break;case 3:default:y=[_(D),_(z),_(X),_(ct),wt[2]].join(" ")}return y},Gr=p.__private__.getFilters=function(){return l},br=p.__private__.putStream=function(u){var y=(u=u||{}).data||"",D=u.filters||Gr(),z=u.alreadyAppliedFilters||[],X=u.addLength1||!1,ct=y.length,wt=u.objectId,Dt=function(Xe){return Xe};if(m!==null&&wt===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");m!==null&&(Dt=Ke.encryptor(wt,0));var Rt={};D===!0&&(D=["FlateEncode"]);var Zt=u.additionalKeyValues||[],Kt=(Rt=Ut.API.processDataByFilters!==void 0?Ut.API.processDataByFilters(y,D):{data:y,reverseChain:[]}).reverseChain+(Array.isArray(z)?z.join(" "):z.toString());if(Rt.data.length!==0&&(Zt.push({key:"Length",value:Rt.data.length}),X===!0&&Zt.push({key:"Length1",value:ct})),Kt.length!=0)if(Kt.split("/").length-1==1)Zt.push({key:"Filter",value:Kt});else{Zt.push({key:"Filter",value:"["+Kt+"]"});for(var ie=0;ie<Zt.length;ie+=1)if(Zt[ie].key==="DecodeParms"){for(var Ne=[],Pe=0;Pe<Rt.reverseChain.split("/").length-1;Pe+=1)Ne.push("null");Ne.push(Zt[ie].value),Zt[ie].value="["+Ne.join(" ")+"]"}}T("<<");for(var Me=0;Me<Zt.length;Me++)T("/"+Zt[Me].key+" "+Zt[Me].value);T(">>"),Rt.data.length!==0&&(T("stream"),T(Dt(Rt.data)),T("endstream"))},Jr=p.__private__.putPage=function(u){var y=u.number,D=u.data,z=u.objId,X=u.contentsObjId;fr(z,!0),T("<</Type /Page"),T("/Parent "+u.rootDictionaryObjId+" 0 R"),T("/Resources "+u.resourceDictionaryObjId+" 0 R"),T("/MediaBox ["+parseFloat(Q(u.mediaBox.bottomLeftX))+" "+parseFloat(Q(u.mediaBox.bottomLeftY))+" "+Q(u.mediaBox.topRightX)+" "+Q(u.mediaBox.topRightY)+"]"),u.cropBox!==null&&T("/CropBox ["+Q(u.cropBox.bottomLeftX)+" "+Q(u.cropBox.bottomLeftY)+" "+Q(u.cropBox.topRightX)+" "+Q(u.cropBox.topRightY)+"]"),u.bleedBox!==null&&T("/BleedBox ["+Q(u.bleedBox.bottomLeftX)+" "+Q(u.bleedBox.bottomLeftY)+" "+Q(u.bleedBox.topRightX)+" "+Q(u.bleedBox.topRightY)+"]"),u.trimBox!==null&&T("/TrimBox ["+Q(u.trimBox.bottomLeftX)+" "+Q(u.trimBox.bottomLeftY)+" "+Q(u.trimBox.topRightX)+" "+Q(u.trimBox.topRightY)+"]"),u.artBox!==null&&T("/ArtBox ["+Q(u.artBox.bottomLeftX)+" "+Q(u.artBox.bottomLeftY)+" "+Q(u.artBox.topRightX)+" "+Q(u.artBox.topRightY)+"]"),typeof u.userUnit=="number"&&u.userUnit!==1&&T("/UserUnit "+u.userUnit),he.publish("putPage",{objId:z,pageContext:Yt[y],pageNumber:y,page:D}),T("/Contents "+X+" 0 R"),T(">>"),T("endobj");var ct=D.join(`
`);return E===O.ADVANCED&&(ct+=`
Q`),fr(X,!0),br({data:ct,filters:Gr(),objectId:X}),T("endobj"),z},En=p.__private__.putPages=function(){var u,y,D=[];for(u=1;u<=Se;u++)Yt[u].objId=Oe(),Yt[u].contentsObjId=Oe();for(u=1;u<=Se;u++)D.push(Jr({number:u,data:At[u],objId:Yt[u].objId,contentsObjId:Yt[u].contentsObjId,mediaBox:Yt[u].mediaBox,cropBox:Yt[u].cropBox,bleedBox:Yt[u].bleedBox,trimBox:Yt[u].trimBox,artBox:Yt[u].artBox,userUnit:Yt[u].userUnit,rootDictionaryObjId:nn,resourceDictionaryObjId:jr}));fr(nn,!0),T("<</Type /Pages");var z="/Kids [";for(y=0;y<Se;y++)z+=D[y]+" 0 R ";T(z+"]"),T("/Count "+Se),T(">>"),T("endobj"),he.publish("postPutPages")},ti=function(u){he.publish("putFont",{font:u,out:T,newObject:Re,putStream:br}),u.isAlreadyPutted!==!0&&(u.objectNumber=Re(),T("<<"),T("/Type /Font"),T("/BaseFont /"+Fi(u.postScriptName)),T("/Subtype /Type1"),typeof u.encoding=="string"&&T("/Encoding /"+u.encoding),T("/FirstChar 32"),T("/LastChar 255"),T(">>"),T("endobj"))},ei=function(){for(var u in me)me.hasOwnProperty(u)&&(v===!1||v===!0&&w.hasOwnProperty(u))&&ti(me[u])},ri=function(u){u.objectNumber=Re();var y=[];y.push({key:"Type",value:"/XObject"}),y.push({key:"Subtype",value:"/Form"}),y.push({key:"BBox",value:"["+[Q(u.x),Q(u.y),Q(u.x+u.width),Q(u.y+u.height)].join(" ")+"]"}),y.push({key:"Matrix",value:"["+u.matrix.toString()+"]"});var D=u.pages[1].join(`
`);br({data:D,additionalKeyValues:y,objectId:u.objectNumber}),T("endobj")},ni=function(){for(var u in Ve)Ve.hasOwnProperty(u)&&ri(Ve[u])},ya=function(u,y){var D,z=[],X=1/(y-1);for(D=0;D<1;D+=X)z.push(D);if(z.push(1),u[0].offset!=0){var ct={offset:0,color:u[0].color};u.unshift(ct)}if(u[u.length-1].offset!=1){var wt={offset:1,color:u[u.length-1].color};u.push(wt)}for(var Dt="",Rt=0,Zt=0;Zt<z.length;Zt++){for(D=z[Zt];D>u[Rt+1].offset;)Rt++;var Kt=u[Rt].offset,ie=(D-Kt)/(u[Rt+1].offset-Kt),Ne=u[Rt].color,Pe=u[Rt+1].color;Dt+=U(Math.round((1-ie)*Ne[0]+ie*Pe[0]).toString(16))+U(Math.round((1-ie)*Ne[1]+ie*Pe[1]).toString(16))+U(Math.round((1-ie)*Ne[2]+ie*Pe[2]).toString(16))}return Dt.trim()},_o=function(u,y){y||(y=21);var D=Re(),z=ya(u.colors,y),X=[];X.push({key:"FunctionType",value:"0"}),X.push({key:"Domain",value:"[0.0 1.0]"}),X.push({key:"Size",value:"["+y+"]"}),X.push({key:"BitsPerSample",value:"8"}),X.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),X.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),br({data:z,additionalKeyValues:X,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:D}),T("endobj"),u.objectNumber=Re(),T("<< /ShadingType "+u.type),T("/ColorSpace /DeviceRGB");var ct="/Coords ["+Q(parseFloat(u.coords[0]))+" "+Q(parseFloat(u.coords[1]))+" ";u.type===2?ct+=Q(parseFloat(u.coords[2]))+" "+Q(parseFloat(u.coords[3])):ct+=Q(parseFloat(u.coords[2]))+" "+Q(parseFloat(u.coords[3]))+" "+Q(parseFloat(u.coords[4]))+" "+Q(parseFloat(u.coords[5])),T(ct+="]"),u.matrix&&T("/Matrix ["+u.matrix.toString()+"]"),T("/Function "+D+" 0 R"),T("/Extend [true true]"),T(">>"),T("endobj")},ko=function(u,y){var D=Oe(),z=Re();y.push({resourcesOid:D,objectOid:z}),u.objectNumber=z;var X=[];X.push({key:"Type",value:"/Pattern"}),X.push({key:"PatternType",value:"1"}),X.push({key:"PaintType",value:"1"}),X.push({key:"TilingType",value:"1"}),X.push({key:"BBox",value:"["+u.boundingBox.map(Q).join(" ")+"]"}),X.push({key:"XStep",value:Q(u.xStep)}),X.push({key:"YStep",value:Q(u.yStep)}),X.push({key:"Resources",value:D+" 0 R"}),u.matrix&&X.push({key:"Matrix",value:"["+u.matrix.toString()+"]"}),br({data:u.stream,additionalKeyValues:X,objectId:u.objectNumber}),T("endobj")},ii=function(u){var y;for(y in ue)ue.hasOwnProperty(y)&&(ue[y]instanceof Xn?_o(ue[y]):ue[y]instanceof Ii&&ko(ue[y],u))},wa=function(u){for(var y in u.objectNumber=Re(),T("<<"),u)switch(y){case"opacity":T("/ca "+pt(u[y]));break;case"stroke-opacity":T("/CA "+pt(u[y]))}T(">>"),T("endobj")},jo=function(){var u;for(u in Le)Le.hasOwnProperty(u)&&wa(Le[u])},Ei=function(){for(var u in T("/XObject <<"),Ve)Ve.hasOwnProperty(u)&&Ve[u].objectNumber>=0&&T("/"+u+" "+Ve[u].objectNumber+" 0 R");he.publish("putXobjectDict"),T(">>")},Co=function(){Ke.oid=Re(),T("<<"),T("/Filter /Standard"),T("/V "+Ke.v),T("/R "+Ke.r),T("/U <"+Ke.toHexString(Ke.U)+">"),T("/O <"+Ke.toHexString(Ke.O)+">"),T("/P "+Ke.P),T(">>"),T("endobj")},xa=function(){for(var u in T("/Font <<"),me)me.hasOwnProperty(u)&&(v===!1||v===!0&&w.hasOwnProperty(u))&&T("/"+u+" "+me[u].objectNumber+" 0 R");T(">>")},Fo=function(){if(Object.keys(ue).length>0){for(var u in T("/Shading <<"),ue)ue.hasOwnProperty(u)&&ue[u]instanceof Xn&&ue[u].objectNumber>=0&&T("/"+u+" "+ue[u].objectNumber+" 0 R");he.publish("putShadingPatternDict"),T(">>")}},ai=function(u){if(Object.keys(ue).length>0){for(var y in T("/Pattern <<"),ue)ue.hasOwnProperty(y)&&ue[y]instanceof p.TilingPattern&&ue[y].objectNumber>=0&&ue[y].objectNumber<u&&T("/"+y+" "+ue[y].objectNumber+" 0 R");he.publish("putTilingPatternDict"),T(">>")}},Io=function(){if(Object.keys(Le).length>0){var u;for(u in T("/ExtGState <<"),Le)Le.hasOwnProperty(u)&&Le[u].objectNumber>=0&&T("/"+u+" "+Le[u].objectNumber+" 0 R");he.publish("putGStateDict"),T(">>")}},je=function(u){fr(u.resourcesOid,!0),T("<<"),T("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),xa(),Fo(),ai(u.objectOid),Io(),Ei(),T(">>"),T("endobj")},Aa=function(){var u=[];ei(),jo(),ni(),ii(u),he.publish("putResources"),u.forEach(je),je({resourcesOid:jr,objectOid:Number.MAX_SAFE_INTEGER}),he.publish("postPutResources")},Na=function(){he.publish("putAdditionalObjects");for(var u=0;u<Nt.length;u++){var y=Nt[u];fr(y.objId,!0),T(y.content),T("endobj")}he.publish("postPutAdditionalObjects")},La=function(u){Ae[u.fontName]=Ae[u.fontName]||{},Ae[u.fontName][u.fontStyle]=u.id},Ri=function(u,y,D,z,X){var ct={id:"F"+(Object.keys(me).length+1).toString(10),postScriptName:u,fontName:y,fontStyle:D,encoding:z,isStandardFont:X||!1,metadata:{}};return he.publish("addFont",{font:ct,instance:this}),me[ct.id]=ct,La(ct),ct.id},Do=function(u){for(var y=0,D=xt.length;y<D;y++){var z=Ri.call(this,u[y][0],u[y][1],u[y][2],xt[y][3],!0);v===!1&&(w[z]=!0);var X=u[y][0].split("-");La({id:z,fontName:X[0],fontStyle:X[1]||""})}he.publish("addFonts",{fonts:me,dictionary:Ae})},Ir=function(u){return u.foo=function(){try{return u.apply(this,arguments)}catch(z){var y=z.stack||"";~y.indexOf(" at ")&&(y=y.split(" at ")[1]);var D="Error in function "+y.split(`
`)[0].split("<")[0]+": "+z.message;if(!Wt.console)throw new Error(D);Wt.console.error(D,z),Wt.alert&&alert(D)}},u.foo.bar=u,u.foo},oi=function(u,y){var D,z,X,ct,wt,Dt,Rt,Zt,Kt;if(X=(y=y||{}).sourceEncoding||"Unicode",wt=y.outputEncoding,(y.autoencode||wt)&&me[re].metadata&&me[re].metadata[X]&&me[re].metadata[X].encoding&&(ct=me[re].metadata[X].encoding,!wt&&me[re].encoding&&(wt=me[re].encoding),!wt&&ct.codePages&&(wt=ct.codePages[0]),typeof wt=="string"&&(wt=ct[wt]),wt)){for(Rt=!1,Dt=[],D=0,z=u.length;D<z;D++)(Zt=wt[u.charCodeAt(D)])?Dt.push(String.fromCharCode(Zt)):Dt.push(u[D]),Dt[D].charCodeAt(0)>>8&&(Rt=!0);u=Dt.join("")}for(D=u.length;Rt===void 0&&D!==0;)u.charCodeAt(D-1)>>8&&(Rt=!0),D--;if(!Rt)return u;for(Dt=y.noBOM?[]:[254,255],D=0,z=u.length;D<z;D++){if((Kt=(Zt=u.charCodeAt(D))>>8)>>8)throw new Error("Character at position "+D+" of string '"+u+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Dt.push(Kt),Dt.push(Zt-(Kt<<8))}return String.fromCharCode.apply(void 0,Dt)},or=p.__private__.pdfEscape=p.pdfEscape=function(u,y){return oi(u,y).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},qi=p.__private__.beginPage=function(u){At[++Se]=[],Yt[Se]={objId:0,contentsObjId:0,userUnit:Number(h),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(u[0]),topRightY:Number(u[1])}},Pa(Se),ft(At[I])},Sa=function(u,y){var D,z,X;switch(e=y||e,typeof u=="string"&&(D=P(u.toLowerCase()),Array.isArray(D)&&(z=D[0],X=D[1])),Array.isArray(u)&&(z=u[0]*Ft,X=u[1]*Ft),isNaN(z)&&(z=o[0],X=o[1]),(z>14400||X>14400)&&(be.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),z=Math.min(14400,z),X=Math.min(14400,X)),o=[z,X],e.substr(0,1)){case"l":X>z&&(o=[X,z]);break;case"p":z>X&&(o=[X,z])}qi(o),Ia(Wi),T(Dr),Gi!==0&&T(Gi+" J"),Ji!==0&&T(Ji+" j"),he.publish("addPage",{pageNumber:Se})},Bo=function(u){u>0&&u<=Se&&(At.splice(u,1),Yt.splice(u,1),Se--,I>Se&&(I=Se),this.setPage(I))},Pa=function(u){u>0&&u<=Se&&(I=u)},Oo=p.__private__.getNumberOfPages=p.getNumberOfPages=function(){return At.length-1},_a=function(u,y,D){var z,X=void 0;return D=D||{},u=u!==void 0?u:me[re].fontName,y=y!==void 0?y:me[re].fontStyle,z=u.toLowerCase(),Ae[z]!==void 0&&Ae[z][y]!==void 0?X=Ae[z][y]:Ae[u]!==void 0&&Ae[u][y]!==void 0?X=Ae[u][y]:D.disableWarning===!1&&be.warn("Unable to look up font label for font '"+u+"', '"+y+"'. Refer to getFontList() for available fonts."),X||D.noFallback||(X=Ae.times[y])==null&&(X=Ae.times.normal),X},Mo=p.__private__.putInfo=function(){var u=Re(),y=function(z){return z};for(var D in m!==null&&(y=Ke.encryptor(u,0)),T("<<"),T("/Producer ("+or(y("jsPDF "+Ut.version))+")"),Vt)Vt.hasOwnProperty(D)&&Vt[D]&&T("/"+D.substr(0,1).toUpperCase()+D.substr(1)+" ("+or(y(Vt[D]))+")");T("/CreationDate ("+or(y(ot))+")"),T(">>"),T("endobj")},zi=p.__private__.putCatalog=function(u){var y=(u=u||{}).rootDictionaryObjId||nn;switch(Re(),T("<<"),T("/Type /Catalog"),T("/Pages "+y+" 0 R"),Ct||(Ct="fullwidth"),Ct){case"fullwidth":T("/OpenAction [3 0 R /FitH null]");break;case"fullheight":T("/OpenAction [3 0 R /FitV null]");break;case"fullpage":T("/OpenAction [3 0 R /Fit]");break;case"original":T("/OpenAction [3 0 R /XYZ null null 1]");break;default:var D=""+Ct;D.substr(D.length-1)==="%"&&(Ct=parseInt(Ct)/100),typeof Ct=="number"&&T("/OpenAction [3 0 R /XYZ null null "+pt(Ct)+"]")}switch(ee||(ee="continuous"),ee){case"continuous":T("/PageLayout /OneColumn");break;case"single":T("/PageLayout /SinglePage");break;case"two":case"twoleft":T("/PageLayout /TwoColumnLeft");break;case"tworight":T("/PageLayout /TwoColumnRight")}Jt&&T("/PageMode /"+Jt),he.publish("putCatalog"),T(">>"),T("endobj")},To=p.__private__.putTrailer=function(){T("trailer"),T("<<"),T("/Size "+(Y+1)),T("/Root "+Y+" 0 R"),T("/Info "+(Y-1)+" 0 R"),m!==null&&T("/Encrypt "+Ke.oid+" 0 R"),T("/ID [ <"+nt+"> <"+nt+"> ]"),T(">>")},Eo=p.__private__.putHeader=function(){T("%PDF-"+C),T("%ºß¬à")},Ro=p.__private__.putXRef=function(){var u="0000000000";T("xref"),T("0 "+(Y+1)),T("0000000000 65535 f ");for(var y=1;y<=Y;y++)typeof et[y]=="function"?T((u+et[y]()).slice(-10)+" 00000 n "):et[y]!==void 0?T((u+et[y]).slice(-10)+" 00000 n "):T("0000000000 00000 n ")},an=p.__private__.buildDocument=function(){Ht(),ft(it),he.publish("buildDocument"),Eo(),En(),Na(),Aa(),m!==null&&Co(),Mo(),zi();var u=at;return Ro(),To(),T("startxref"),T(""+u),T("%%EOF"),ft(At[I]),it.join(`
`)},si=p.__private__.getBlob=function(u){return new Blob([Mt(u)],{type:"application/pdf"})},li=p.output=p.__private__.output=Ir(function(u,y){switch(typeof(y=y||{})=="string"?y={filename:y}:y.filename=y.filename||"generated.pdf",u){case void 0:return an();case"save":p.save(y.filename);break;case"arraybuffer":return Mt(an());case"blob":return si(an());case"bloburi":case"bloburl":if(Wt.URL!==void 0&&typeof Wt.URL.createObjectURL=="function")return Wt.URL&&Wt.URL.createObjectURL(si(an()))||void 0;be.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var D="",z=an();try{D=Ms(z)}catch{D=Ms(unescape(encodeURIComponent(z)))}return"data:application/pdf;filename="+y.filename+";base64,"+D;case"pdfobjectnewwindow":if(Object.prototype.toString.call(Wt)==="[object Window]"){var X="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",ct=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';y.pdfObjectUrl&&(X=y.pdfObjectUrl,ct="");var wt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+X+'"'+ct+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(y)+");<\/script></body></html>",Dt=Wt.open();return Dt!==null&&Dt.document.write(wt),Dt}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(Wt)==="[object Window]"){var Rt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(y.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+y.filename+'" width="500px" height="400px" /></body></html>',Zt=Wt.open();if(Zt!==null){Zt.document.write(Rt);var Kt=this;Zt.document.documentElement.querySelector("#pdfViewer").onload=function(){Zt.document.title=y.filename,Zt.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(Kt.output("bloburl"))}}return Zt}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(Wt)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var ie='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",y)+'"></iframe></body></html>',Ne=Wt.open();if(Ne!==null&&(Ne.document.write(ie),Ne.document.title=y.filename),Ne||typeof safari>"u")return Ne;break;case"datauri":case"dataurl":return Wt.document.location.href=this.output("datauristring",y);default:return null}}),ka=function(u){return Array.isArray(Tn)===!0&&Tn.indexOf(u)>-1};switch(i){case"pt":Ft=1;break;case"mm":Ft=72/25.4;break;case"cm":Ft=72/2.54;break;case"in":Ft=72;break;case"px":Ft=ka("px_scaling")==1?.75:96/72;break;case"pc":case"em":Ft=12;break;case"ex":Ft=6;break;default:if(typeof i!="number")throw new Error("Invalid unit: "+i);Ft=i}var Ke=null;It(),Z();var qo=function(u){return m!==null?Ke.encryptor(u,0):function(y){return y}},ja=p.__private__.getPageInfo=p.getPageInfo=function(u){if(isNaN(u)||u%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Yt[u].objId,pageNumber:u,pageContext:Yt[u]}},Gt=p.__private__.getPageInfoByObjId=function(u){if(isNaN(u)||u%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var y in Yt)if(Yt[y].objId===u)break;return ja(y)},zo=p.__private__.getCurrentPageInfo=p.getCurrentPageInfo=function(){return{objId:Yt[I].objId,pageNumber:I,pageContext:Yt[I]}};p.addPage=function(){return Sa.apply(this,arguments),this},p.setPage=function(){return Pa.apply(this,arguments),ft.call(this,At[I]),this},p.insertPage=function(u){return this.addPage(),this.movePage(I,u),this},p.movePage=function(u,y){var D,z;if(u>y){D=At[u],z=Yt[u];for(var X=u;X>y;X--)At[X]=At[X-1],Yt[X]=Yt[X-1];At[y]=D,Yt[y]=z,this.setPage(y)}else if(u<y){D=At[u],z=Yt[u];for(var ct=u;ct<y;ct++)At[ct]=At[ct+1],Yt[ct]=Yt[ct+1];At[y]=D,Yt[y]=z,this.setPage(y)}return this},p.deletePage=function(){return Bo.apply(this,arguments),this},p.__private__.text=p.text=function(u,y,D,z,X){var ct,wt,Dt,Rt,Zt,Kt,ie,Ne,Pe,Me=(z=z||{}).scope||this;if(typeof u=="number"&&typeof y=="number"&&(typeof D=="string"||Array.isArray(D))){var Xe=D;D=y,y=u,u=Xe}if(arguments[3]instanceof zt?(q("The transform parameter of text() with a Matrix value"),Pe=X):(Dt=arguments[4],Rt=arguments[5],de(ie=arguments[3])==="object"&&ie!==null||(typeof Dt=="string"&&(Rt=Dt,Dt=null),typeof ie=="string"&&(Rt=ie,ie=null),typeof ie=="number"&&(Dt=ie,ie=null),z={flags:ie,angle:Dt,align:Rt})),isNaN(y)||isNaN(D)||u==null)throw new Error("Invalid arguments passed to jsPDF.text");if(u.length===0)return Me;var ze="",Br=!1,dr=typeof z.lineHeightFactor=="number"?z.lineHeightFactor:qn,Xr=Me.internal.scaleFactor;function Ea(ye){return ye=ye.split("	").join(Array(z.TabLen||9).join(" ")),or(ye,ie)}function Zi(ye){for(var we,Fe=ye.concat(),qe=[],hn=Fe.length;hn--;)typeof(we=Fe.shift())=="string"?qe.push(we):Array.isArray(ye)&&(we.length===1||we[1]===void 0&&we[2]===void 0)?qe.push(we[0]):qe.push([we[0],we[1],we[2]]);return qe}function $i(ye,we){var Fe;if(typeof ye=="string")Fe=we(ye)[0];else if(Array.isArray(ye)){for(var qe,hn,sa=ye.concat(),Ni=[],Ha=sa.length;Ha--;)typeof(qe=sa.shift())=="string"?Ni.push(we(qe)[0]):Array.isArray(qe)&&typeof qe[0]=="string"&&(hn=we(qe[0],qe[1],qe[2]),Ni.push([hn[0],hn[1],hn[2]]));Fe=Ni}return Fe}var pi=!1,ta=!0;if(typeof u=="string")pi=!0;else if(Array.isArray(u)){var ea=u.concat();wt=[];for(var gi,Ge=ea.length;Ge--;)(typeof(gi=ea.shift())!="string"||Array.isArray(gi)&&typeof gi[0]!="string")&&(ta=!1);pi=ta}if(pi===!1)throw new Error('Type of text must be string or Array. "'+u+'" is not recognized.');typeof u=="string"&&(u=u.match(/[\r?\n]/)?u.split(/\r\n|\r|\n/g):[u]);var mi=Lt/Me.internal.scaleFactor,vi=mi*(dr-1);switch(z.baseline){case"bottom":D-=vi;break;case"top":D+=mi-vi;break;case"hanging":D+=mi-2*vi;break;case"middle":D+=mi/2-vi}if((Kt=z.maxWidth||0)>0&&(typeof u=="string"?u=Me.splitTextToSize(u,Kt):Object.prototype.toString.call(u)==="[object Array]"&&(u=u.reduce(function(ye,we){return ye.concat(Me.splitTextToSize(we,Kt))},[]))),ct={text:u,x:y,y:D,options:z,mutex:{pdfEscape:or,activeFontKey:re,fonts:me,activeFontSize:Lt}},he.publish("preProcessText",ct),u=ct.text,Dt=(z=ct.options).angle,!(Pe instanceof zt)&&Dt&&typeof Dt=="number"){Dt*=Math.PI/180,z.rotationDirection===0&&(Dt=-Dt),E===O.ADVANCED&&(Dt=-Dt);var bi=Math.cos(Dt),ra=Math.sin(Dt);Pe=new zt(bi,ra,-ra,bi,0,0)}else Dt&&Dt instanceof zt&&(Pe=Dt);E!==O.ADVANCED||Pe||(Pe=Vr),(Zt=z.charSpace||ci)!==void 0&&(ze+=Q(j(Zt))+` Tc
`,this.setCharSpace(this.getCharSpace()||0)),(Ne=z.horizontalScale)!==void 0&&(ze+=Q(100*Ne)+` Tz
`),z.lang;var sr=-1,Qo=z.renderingMode!==void 0?z.renderingMode:z.stroke,na=Me.internal.getCurrentPageInfo().pageContext;switch(Qo){case 0:case!1:case"fill":sr=0;break;case 1:case!0:case"stroke":sr=1;break;case 2:case"fillThenStroke":sr=2;break;case 3:case"invisible":sr=3;break;case 4:case"fillAndAddForClipping":sr=4;break;case 5:case"strokeAndAddPathForClipping":sr=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":sr=6;break;case 7:case"addToPathForClipping":sr=7}var Ra=na.usedRenderingMode!==void 0?na.usedRenderingMode:-1;sr!==-1?ze+=sr+` Tr
`:Ra!==-1&&(ze+=`0 Tr
`),sr!==-1&&(na.usedRenderingMode=sr),Rt=z.align||"left";var yr,yi=Lt*dr,qa=Me.internal.pageSize.getWidth(),za=me[re];Zt=z.charSpace||ci,Kt=z.maxWidth||0,ie=Object.assign({autoencode:!0,noBOM:!0},z.flags);var wn=[],Hn=function(ye){return Me.getStringUnitWidth(ye,{font:za,charSpace:Zt,fontSize:Lt,doKerning:!1})*Lt/Xr};if(Object.prototype.toString.call(u)==="[object Array]"){var lr;wt=Zi(u),Rt!=="left"&&(yr=wt.map(Hn));var rr,xn=0;if(Rt==="right"){y-=yr[0],u=[],Ge=wt.length;for(var sn=0;sn<Ge;sn++)sn===0?(rr=Kr(y),lr=on(D)):(rr=j(xn-yr[sn]),lr=-yi),u.push([wt[sn],rr,lr]),xn=yr[sn]}else if(Rt==="center"){y-=yr[0]/2,u=[],Ge=wt.length;for(var ln=0;ln<Ge;ln++)ln===0?(rr=Kr(y),lr=on(D)):(rr=j((xn-yr[ln])/2),lr=-yi),u.push([wt[ln],rr,lr]),xn=yr[ln]}else if(Rt==="left"){u=[],Ge=wt.length;for(var wi=0;wi<Ge;wi++)u.push(wt[wi])}else if(Rt==="justify"&&za.encoding==="Identity-H"){u=[],Ge=wt.length,Kt=Kt!==0?Kt:qa;for(var un=0,Ce=0;Ce<Ge;Ce++)if(lr=Ce===0?on(D):-yi,rr=Ce===0?Kr(y):un,Ce<Ge-1){var ia=j((Kt-yr[Ce])/(wt[Ce].split(" ").length-1)),nr=wt[Ce].split(" ");u.push([nr[0]+" ",rr,lr]),un=0;for(var wr=1;wr<nr.length;wr++){var xi=(Hn(nr[wr-1]+" "+nr[wr])-Hn(nr[wr]))*Xr+ia;wr==nr.length-1?u.push([nr[wr],xi,0]):u.push([nr[wr]+" ",xi,0]),un-=xi}}else u.push([wt[Ce],rr,lr]);u.push(["",un,0])}else{if(Rt!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(u=[],Ge=wt.length,Kt=Kt!==0?Kt:qa,Ce=0;Ce<Ge;Ce++)lr=Ce===0?on(D):-yi,rr=Ce===0?Kr(y):0,Ce<Ge-1?wn.push(Q(j((Kt-yr[Ce])/(wt[Ce].split(" ").length-1)))):wn.push(0),u.push([wt[Ce],rr,lr])}}var Ua=typeof z.R2L=="boolean"?z.R2L:Et;Ua===!0&&(u=$i(u,function(ye,we,Fe){return[ye.split("").reverse().join(""),we,Fe]})),ct={text:u,x:y,y:D,options:z,mutex:{pdfEscape:or,activeFontKey:re,fonts:me,activeFontSize:Lt}},he.publish("postProcessText",ct),u=ct.text,Br=ct.mutex.isHex||!1;var aa=me[re].encoding;aa!=="WinAnsiEncoding"&&aa!=="StandardEncoding"||(u=$i(u,function(ye,we,Fe){return[Ea(ye),we,Fe]})),wt=Zi(u),u=[];for(var Wn,Vn,An,Gn=0,Ai=1,Jn=Array.isArray(wt[0])?Ai:Gn,Nn="",oa=function(ye,we,Fe){var qe="";return Fe instanceof zt?(Fe=typeof z.angle=="number"?kr(Fe,new zt(1,0,0,1,ye,we)):kr(new zt(1,0,0,1,ye,we),Fe),E===O.ADVANCED&&(Fe=kr(new zt(1,0,0,-1,0,0),Fe)),qe=Fe.join(" ")+` Tm
`):qe=Q(ye)+" "+Q(we)+` Td
`,qe},xr=0;xr<wt.length;xr++){switch(Nn="",Jn){case Ai:An=(Br?"<":"(")+wt[xr][0]+(Br?">":")"),Wn=parseFloat(wt[xr][1]),Vn=parseFloat(wt[xr][2]);break;case Gn:An=(Br?"<":"(")+wt[xr]+(Br?">":")"),Wn=Kr(y),Vn=on(D)}wn!==void 0&&wn[xr]!==void 0&&(Nn=wn[xr]+` Tw
`),xr===0?u.push(Nn+oa(Wn,Vn,Pe)+An):Jn===Gn?u.push(Nn+An):Jn===Ai&&u.push(Nn+oa(Wn,Vn,Pe)+An)}u=Jn===Gn?u.join(` Tj
T* `):u.join(` Tj
`),u+=` Tj
`;var Ar=`BT
/`;return Ar+=re+" "+Lt+` Tf
`,Ar+=Q(Lt*dr)+` TL
`,Ar+=zn+`
`,Ar+=ze,Ar+=u,T(Ar+="ET"),w[re]=!0,Me};var Uo=p.__private__.clip=p.clip=function(u){return T(u==="evenodd"?"W*":"W"),this};p.clipEvenOdd=function(){return Uo("evenodd")},p.__private__.discardPath=p.discardPath=function(){return T("n"),this};var Yr=p.__private__.isValidStyle=function(u){var y=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(u)!==-1&&(y=!0),y};p.__private__.setDefaultPathOperation=p.setDefaultPathOperation=function(u){return Yr(u)&&(d=u),this};var Ca=p.__private__.getStyle=p.getStyle=function(u){var y=d;switch(u){case"D":case"S":y="S";break;case"F":y="f";break;case"FD":case"DF":y="B";break;case"f":case"f*":case"B":case"B*":y=u}return y},Fa=p.close=function(){return T("h"),this};p.stroke=function(){return T("S"),this},p.fill=function(u){return ui("f",u),this},p.fillEvenOdd=function(u){return ui("f*",u),this},p.fillStroke=function(u){return ui("B",u),this},p.fillStrokeEvenOdd=function(u){return ui("B*",u),this};var ui=function(u,y){de(y)==="object"?Wo(y,u):T(u)},Ui=function(u){u===null||E===O.ADVANCED&&u===void 0||(u=Ca(u),T(u))};function Ho(u,y,D,z,X){var ct=new Ii(y||this.boundingBox,D||this.xStep,z||this.yStep,this.gState,X||this.matrix);ct.stream=this.stream;var wt=u+"$$"+this.cloneIndex+++"$$";return ar(wt,ct),ct}var Wo=function(u,y){var D=Mn[u.key],z=ue[D];if(z instanceof Xn)T("q"),T(Vo(y)),z.gState&&p.setGState(z.gState),T(u.matrix.toString()+" cm"),T("/"+D+" sh"),T("Q");else if(z instanceof Ii){var X=new zt(1,0,0,-1,0,yn());u.matrix&&(X=X.multiply(u.matrix||Vr),D=Ho.call(z,u.key,u.boundingBox,u.xStep,u.yStep,X).id),T("q"),T("/Pattern cs"),T("/"+D+" scn"),z.gState&&p.setGState(z.gState),T(y),T("Q")}},Vo=function(u){switch(u){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Hi=p.moveTo=function(u,y){return T(Q(j(u))+" "+Q(R(y))+" m"),this},Rn=p.lineTo=function(u,y){return T(Q(j(u))+" "+Q(R(y))+" l"),this},vn=p.curveTo=function(u,y,D,z,X,ct){return T([Q(j(u)),Q(R(y)),Q(j(D)),Q(R(z)),Q(j(X)),Q(R(ct)),"c"].join(" ")),this};p.__private__.line=p.line=function(u,y,D,z,X){if(isNaN(u)||isNaN(y)||isNaN(D)||isNaN(z)||!Yr(X))throw new Error("Invalid arguments passed to jsPDF.line");return E===O.COMPAT?this.lines([[D-u,z-y]],u,y,[1,1],X||"S"):this.lines([[D-u,z-y]],u,y,[1,1]).stroke()},p.__private__.lines=p.lines=function(u,y,D,z,X,ct){var wt,Dt,Rt,Zt,Kt,ie,Ne,Pe,Me,Xe,ze,Br;if(typeof u=="number"&&(Br=D,D=y,y=u,u=Br),z=z||[1,1],ct=ct||!1,isNaN(y)||isNaN(D)||!Array.isArray(u)||!Array.isArray(z)||!Yr(X)||typeof ct!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(Hi(y,D),wt=z[0],Dt=z[1],Zt=u.length,Xe=y,ze=D,Rt=0;Rt<Zt;Rt++)(Kt=u[Rt]).length===2?(Xe=Kt[0]*wt+Xe,ze=Kt[1]*Dt+ze,Rn(Xe,ze)):(ie=Kt[0]*wt+Xe,Ne=Kt[1]*Dt+ze,Pe=Kt[2]*wt+Xe,Me=Kt[3]*Dt+ze,Xe=Kt[4]*wt+Xe,ze=Kt[5]*Dt+ze,vn(ie,Ne,Pe,Me,Xe,ze));return ct&&Fa(),Ui(X),this},p.path=function(u){for(var y=0;y<u.length;y++){var D=u[y],z=D.c;switch(D.op){case"m":Hi(z[0],z[1]);break;case"l":Rn(z[0],z[1]);break;case"c":vn.apply(this,z);break;case"h":Fa()}}return this},p.__private__.rect=p.rect=function(u,y,D,z,X){if(isNaN(u)||isNaN(y)||isNaN(D)||isNaN(z)||!Yr(X))throw new Error("Invalid arguments passed to jsPDF.rect");return E===O.COMPAT&&(z=-z),T([Q(j(u)),Q(R(y)),Q(j(D)),Q(j(z)),"re"].join(" ")),Ui(X),this},p.__private__.triangle=p.triangle=function(u,y,D,z,X,ct,wt){if(isNaN(u)||isNaN(y)||isNaN(D)||isNaN(z)||isNaN(X)||isNaN(ct)||!Yr(wt))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[D-u,z-y],[X-D,ct-z],[u-X,y-ct]],u,y,[1,1],wt,!0),this},p.__private__.roundedRect=p.roundedRect=function(u,y,D,z,X,ct,wt){if(isNaN(u)||isNaN(y)||isNaN(D)||isNaN(z)||isNaN(X)||isNaN(ct)||!Yr(wt))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var Dt=4/3*(Math.SQRT2-1);return X=Math.min(X,.5*D),ct=Math.min(ct,.5*z),this.lines([[D-2*X,0],[X*Dt,0,X,ct-ct*Dt,X,ct],[0,z-2*ct],[0,ct*Dt,-X*Dt,ct,-X,ct],[2*X-D,0],[-X*Dt,0,-X,-ct*Dt,-X,-ct],[0,2*ct-z],[0,-ct*Dt,X*Dt,-ct,X,-ct]],u+X,y,[1,1],wt,!0),this},p.__private__.ellipse=p.ellipse=function(u,y,D,z,X){if(isNaN(u)||isNaN(y)||isNaN(D)||isNaN(z)||!Yr(X))throw new Error("Invalid arguments passed to jsPDF.ellipse");var ct=4/3*(Math.SQRT2-1)*D,wt=4/3*(Math.SQRT2-1)*z;return Hi(u+D,y),vn(u+D,y-wt,u+ct,y-z,u,y-z),vn(u-ct,y-z,u-D,y-wt,u-D,y),vn(u-D,y+wt,u-ct,y+z,u,y+z),vn(u+ct,y+z,u+D,y+wt,u+D,y),Ui(X),this},p.__private__.circle=p.circle=function(u,y,D,z){if(isNaN(u)||isNaN(y)||isNaN(D)||!Yr(z))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(u,y,D,D,z)},p.setFont=function(u,y,D){return D&&(y=yt(y,D)),re=_a(u,y,{disableWarning:!1}),this};var Go=p.__private__.getFont=p.getFont=function(){return me[_a.apply(p,arguments)]};p.__private__.getFontList=p.getFontList=function(){var u,y,D={};for(u in Ae)if(Ae.hasOwnProperty(u))for(y in D[u]=[],Ae[u])Ae[u].hasOwnProperty(y)&&D[u].push(y);return D},p.addFont=function(u,y,D,z,X){var ct=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&ct.indexOf(arguments[3])!==-1?X=arguments[3]:arguments[3]&&ct.indexOf(arguments[3])==-1&&(D=yt(D,z)),X=X||"Identity-H",Ri.call(this,u,y,D,X)};var qn,Wi=r.lineWidth||.200025,hi=p.__private__.getLineWidth=p.getLineWidth=function(){return Wi},Ia=p.__private__.setLineWidth=p.setLineWidth=function(u){return Wi=u,T(Q(j(u))+" w"),this};p.__private__.setLineDash=Ut.API.setLineDash=Ut.API.setLineDashPattern=function(u,y){if(u=u||[],y=y||0,isNaN(y)||!Array.isArray(u))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return u=u.map(function(D){return Q(j(D))}).join(" "),y=Q(j(y)),T("["+u+"] "+y+" d"),this};var Da=p.__private__.getLineHeight=p.getLineHeight=function(){return Lt*qn};p.__private__.getLineHeight=p.getLineHeight=function(){return Lt*qn};var Ba=p.__private__.setLineHeightFactor=p.setLineHeightFactor=function(u){return typeof(u=u||1.15)=="number"&&(qn=u),this},Oa=p.__private__.getLineHeightFactor=p.getLineHeightFactor=function(){return qn};Ba(r.lineHeight);var Kr=p.__private__.getHorizontalCoordinate=function(u){return j(u)},on=p.__private__.getVerticalCoordinate=function(u){return E===O.ADVANCED?u:Yt[I].mediaBox.topRightY-Yt[I].mediaBox.bottomLeftY-j(u)},Jo=p.__private__.getHorizontalCoordinateString=p.getHorizontalCoordinateString=function(u){return Q(Kr(u))},bn=p.__private__.getVerticalCoordinateString=p.getVerticalCoordinateString=function(u){return Q(on(u))},Dr=r.strokeColor||"0 G";p.__private__.getStrokeColor=p.getDrawColor=function(){return Cr(Dr)},p.__private__.setStrokeColor=p.setDrawColor=function(u,y,D,z){return Dr=Fr({ch1:u,ch2:y,ch3:D,ch4:z,pdfColorType:"draw",precision:2}),T(Dr),this};var Vi=r.fillColor||"0 g";p.__private__.getFillColor=p.getFillColor=function(){return Cr(Vi)},p.__private__.setFillColor=p.setFillColor=function(u,y,D,z){return Vi=Fr({ch1:u,ch2:y,ch3:D,ch4:z,pdfColorType:"fill",precision:2}),T(Vi),this};var zn=r.textColor||"0 g",Yo=p.__private__.getTextColor=p.getTextColor=function(){return Cr(zn)};p.__private__.setTextColor=p.setTextColor=function(u,y,D,z){return zn=Fr({ch1:u,ch2:y,ch3:D,ch4:z,pdfColorType:"text",precision:3}),this};var ci=r.charSpace,Ko=p.__private__.getCharSpace=p.getCharSpace=function(){return parseFloat(ci||0)};p.__private__.setCharSpace=p.setCharSpace=function(u){if(isNaN(u))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return ci=u,this};var Gi=0;p.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},p.__private__.setLineCap=p.setLineCap=function(u){var y=p.CapJoinStyles[u];if(y===void 0)throw new Error("Line cap style of '"+u+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Gi=y,T(y+" J"),this};var Ji=0;p.__private__.setLineJoin=p.setLineJoin=function(u){var y=p.CapJoinStyles[u];if(y===void 0)throw new Error("Line join style of '"+u+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Ji=y,T(y+" j"),this},p.__private__.setLineMiterLimit=p.__private__.setMiterLimit=p.setLineMiterLimit=p.setMiterLimit=function(u){if(u=u||0,isNaN(u))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return T(Q(j(u))+" M"),this},p.GState=yo,p.setGState=function(u){(u=typeof u=="string"?Le[_r[u]]:Ma(null,u)).equals(rn)||(T("/"+u.id+" gs"),rn=u)};var Ma=function(u,y){if(!u||!_r[u]){var D=!1;for(var z in Le)if(Le.hasOwnProperty(z)&&Le[z].equals(y)){D=!0;break}if(D)y=Le[z];else{var X="GS"+(Object.keys(Le).length+1).toString(10);Le[X]=y,y.id=X}return u&&(_r[u]=y.id),he.publish("addGState",y),y}};p.addGState=function(u,y){return Ma(u,y),this},p.saveGraphicsState=function(){return T("q"),Ur.push({key:re,size:Lt,color:zn}),this},p.restoreGraphicsState=function(){T("Q");var u=Ur.pop();return re=u.key,Lt=u.size,zn=u.color,rn=null,this},p.setCurrentTransformationMatrix=function(u){return T(u.toString()+" cm"),this},p.comment=function(u){return T("#"+u),this};var fi=function(u,y){var D=u||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return D},set:function(ct){isNaN(ct)||(D=parseFloat(ct))}});var z=y||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return z},set:function(ct){isNaN(ct)||(z=parseFloat(ct))}});var X="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return X},set:function(ct){X=ct.toString()}}),this},Yi=function(u,y,D,z){fi.call(this,u,y),this.type="rect";var X=D||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return X},set:function(wt){isNaN(wt)||(X=parseFloat(wt))}});var ct=z||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return ct},set:function(wt){isNaN(wt)||(ct=parseFloat(wt))}}),this},Ki=function(){this.page=Se,this.currentPage=I,this.pages=At.slice(0),this.pagesContext=Yt.slice(0),this.x=Ye,this.y=se,this.matrix=Pr,this.width=Un(I),this.height=yn(I),this.outputDestination=Pt,this.id="",this.objectNumber=-1};Ki.prototype.restore=function(){Se=this.page,I=this.currentPage,Yt=this.pagesContext,At=this.pages,Ye=this.x,se=this.y,Pr=this.matrix,Xi(I,this.width),Qi(I,this.height),Pt=this.outputDestination};var Ta=function(u,y,D,z,X){Wr.push(new Ki),Se=I=0,At=[],Ye=u,se=y,Pr=X,qi([D,z])},Xo=function(u){if(Hr[u])Wr.pop().restore();else{var y=new Ki,D="Xo"+(Object.keys(Ve).length+1).toString(10);y.id=D,Hr[u]=D,Ve[D]=y,he.publish("addFormObject",y),Wr.pop().restore()}};for(var di in p.beginFormObject=function(u,y,D,z,X){return Ta(u,y,D,z,X),this},p.endFormObject=function(u){return Xo(u),this},p.doFormObject=function(u,y){var D=Ve[Hr[u]];return T("q"),T(y.toString()+" cm"),T("/"+D.id+" Do"),T("Q"),this},p.getFormObject=function(u){var y=Ve[Hr[u]];return{x:y.x,y:y.y,width:y.width,height:y.height,matrix:y.matrix}},p.save=function(u,y){return u=u||"generated.pdf",(y=y||{}).returnPromise=y.returnPromise||!1,y.returnPromise===!1?(Kn(si(an()),u),typeof Kn.unload=="function"&&Wt.setTimeout&&setTimeout(Kn.unload,911),this):new Promise(function(D,z){try{var X=Kn(si(an()),u);typeof Kn.unload=="function"&&Wt.setTimeout&&setTimeout(Kn.unload,911),D(X)}catch(ct){z(ct.message)}})},Ut.API)Ut.API.hasOwnProperty(di)&&(di==="events"&&Ut.API.events.length?function(u,y){var D,z,X;for(X=y.length-1;X!==-1;X--)D=y[X][0],z=y[X][1],u.subscribe.apply(u,[D].concat(typeof z=="function"?[z]:z))}(he,Ut.API.events):p[di]=Ut.API[di]);var Un=p.getPageWidth=function(u){return(Yt[u=u||I].mediaBox.topRightX-Yt[u].mediaBox.bottomLeftX)/Ft},Xi=p.setPageWidth=function(u,y){Yt[u].mediaBox.topRightX=y*Ft+Yt[u].mediaBox.bottomLeftX},yn=p.getPageHeight=function(u){return(Yt[u=u||I].mediaBox.topRightY-Yt[u].mediaBox.bottomLeftY)/Ft},Qi=p.setPageHeight=function(u,y){Yt[u].mediaBox.topRightY=y*Ft+Yt[u].mediaBox.bottomLeftY};return p.internal={pdfEscape:or,getStyle:Ca,getFont:Go,getFontSize:kt,getCharSpace:Ko,getTextColor:Yo,getLineHeight:Da,getLineHeightFactor:Oa,getLineWidth:hi,write:Qt,getHorizontalCoordinate:Kr,getVerticalCoordinate:on,getCoordinateString:Jo,getVerticalCoordinateString:bn,collections:{},newObject:Re,newAdditionalObject:$n,newObjectDeferred:Oe,newObjectDeferredBegin:fr,getFilters:Gr,putStream:br,events:he,scaleFactor:Ft,pageSize:{getWidth:function(){return Un(I)},setWidth:function(u){Xi(I,u)},getHeight:function(){return yn(I)},setHeight:function(u){Qi(I,u)}},encryptionOptions:m,encryption:Ke,getEncryptor:qo,output:li,getNumberOfPages:Oo,pages:At,out:T,f2:pt,f3:_,getPageInfo:ja,getPageInfoByObjId:Gt,getCurrentPageInfo:zo,getPDFVersion:k,Point:fi,Rectangle:Yi,Matrix:zt,hasHotfix:ka},Object.defineProperty(p.internal.pageSize,"width",{get:function(){return Un(I)},set:function(u){Xi(I,u)},enumerable:!0,configurable:!0}),Object.defineProperty(p.internal.pageSize,"height",{get:function(){return yn(I)},set:function(u){Qi(I,u)},enumerable:!0,configurable:!0}),Do.call(p,xt),re="F1",Sa(o,e),he.publish("initialized"),p}Ci.prototype.lsbFirstWord=function(r){return String.fromCharCode(r>>0&255,r>>8&255,r>>16&255,r>>24&255)},Ci.prototype.toHexString=function(r){return r.split("").map(function(t){return("0"+(255&t.charCodeAt(0)).toString(16)).slice(-2)}).join("")},Ci.prototype.hexToBytes=function(r){for(var t=[],e=0;e<r.length;e+=2)t.push(String.fromCharCode(parseInt(r.substr(e,2),16)));return t.join("")},Ci.prototype.processOwnerPassword=function(r,t){return Es(Ts(t).substr(0,5),r)},Ci.prototype.encryptor=function(r,t){var e=Ts(this.encryptionKey+String.fromCharCode(255&r,r>>8&255,r>>16&255,255&t,t>>8&255)).substr(0,10);return function(i){return Es(e,i)}},yo.prototype.equals=function(r){var t,e="id,objectNumber,equals";if(!r||de(r)!==de(this))return!1;var i=0;for(t in this)if(!(e.indexOf(t)>=0)){if(this.hasOwnProperty(t)&&!r.hasOwnProperty(t)||this[t]!==r[t])return!1;i++}for(t in r)r.hasOwnProperty(t)&&e.indexOf(t)<0&&i--;return i===0},Ut.API={events:[]},Ut.version="3.0.1";var ke=Ut.API,Js=1,Zn=function(r){return r.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},ki=function(r){return r.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},Xt=function(r){return r.toFixed(2)},In=function(r){return r.toFixed(5)};ke.__acroform__={};var cr=function(r,t){r.prototype=Object.create(t.prototype),r.prototype.constructor=r},jl=function(r){return r*Js},Zr=function(r){var t=new lu,e=Ot.internal.getHeight(r)||0,i=Ot.internal.getWidth(r)||0;return t.BBox=[0,0,Number(Xt(i)),Number(Xt(e))],t},Xh=ke.__acroform__.setBit=function(r,t){if(r=r||0,t=t||0,isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return r|=1<<t},Qh=ke.__acroform__.clearBit=function(r,t){if(r=r||0,t=t||0,isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return r&=~(1<<t)},Zh=ke.__acroform__.getBit=function(r,t){if(isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return(r&1<<t)==0?0:1},Ie=ke.__acroform__.getBitForPdf=function(r,t){if(isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return Zh(r,t-1)},De=ke.__acroform__.setBitForPdf=function(r,t){if(isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return Xh(r,t-1)},Be=ke.__acroform__.clearBitForPdf=function(r,t){if(isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return Qh(r,t-1)},$h=ke.__acroform__.calculateCoordinates=function(r,t){var e=t.internal.getHorizontalCoordinate,i=t.internal.getVerticalCoordinate,o=r[0],a=r[1],l=r[2],h=r[3],c={};return c.lowerLeft_X=e(o)||0,c.lowerLeft_Y=i(a+h)||0,c.upperRight_X=e(o+l)||0,c.upperRight_Y=i(a)||0,[Number(Xt(c.lowerLeft_X)),Number(Xt(c.lowerLeft_Y)),Number(Xt(c.upperRight_X)),Number(Xt(c.upperRight_Y))]},tc=function(r){if(r.appearanceStreamContent)return r.appearanceStreamContent;if(r.V||r.DV){var t=[],e=r._V||r.DV,i=Rs(r,e),o=r.scope.internal.getFont(r.fontName,r.fontStyle).id;t.push("/Tx BMC"),t.push("q"),t.push("BT"),t.push(r.scope.__private__.encodeColorString(r.color)),t.push("/"+o+" "+Xt(i.fontSize)+" Tf"),t.push("1 0 0 1 0 0 Tm"),t.push(i.text),t.push("ET"),t.push("Q"),t.push("EMC");var a=Zr(r);return a.scope=r.scope,a.stream=t.join(`
`),a}},Rs=function(r,t){var e=r.fontSize===0?r.maxFontSize:r.fontSize,i={text:"",fontSize:""},o=(t=(t=t.substr(0,1)=="("?t.substr(1):t).substr(t.length-1)==")"?t.substr(0,t.length-1):t).split(" ");o=r.multiline?o.map(function(_){return _.split(`
`)}):o.map(function(_){return[_]});var a=e,l=Ot.internal.getHeight(r)||0;l=l<0?-l:l;var h=Ot.internal.getWidth(r)||0;h=h<0?-h:h;var c=function(_,j,V){if(_+1<o.length){var R=j+" "+o[_+1][0];return co(R,r,V).width<=h-4}return!1};a++;t:for(;a>0;){t="",a--;var d,m,v=co("3",r,a).height,w=r.multiline?l-a:(l-v)/2,p=w+=2,C=0,k=0,B=0;if(a<=0){t=`(...) Tj
`,t+="% Width of Text: "+co(t,r,a=12).width+", FieldWidth:"+h+`
`;break}for(var P="",O=0,E=0;E<o.length;E++)if(o.hasOwnProperty(E)){var K=!1;if(o[E].length!==1&&B!==o[E].length-1){if((v+2)*(O+2)+2>l)continue t;P+=o[E][B],K=!0,k=E,E--}else{P=(P+=o[E][B]+" ").substr(P.length-1)==" "?P.substr(0,P.length-1):P;var st=parseInt(E),yt=c(st,P,a),Q=E>=o.length-1;if(yt&&!Q){P+=" ",B=0;continue}if(yt||Q){if(Q)k=st;else if(r.multiline&&(v+2)*(O+2)+2>l)continue t}else{if(!r.multiline||(v+2)*(O+2)+2>l)continue t;k=st}}for(var q="",rt=C;rt<=k;rt++){var pt=o[rt];if(r.multiline){if(rt===k){q+=pt[B]+" ",B=(B+1)%pt.length;continue}if(rt===C){q+=pt[pt.length-1]+" ";continue}}q+=pt[0]+" "}switch(q=q.substr(q.length-1)==" "?q.substr(0,q.length-1):q,m=co(q,r,a).width,r.textAlign){case"right":d=h-m-2;break;case"center":d=(h-m)/2;break;case"left":default:d=2}t+=Xt(d)+" "+Xt(p)+` Td
`,t+="("+Zn(q)+`) Tj
`,t+=-Xt(d)+` 0 Td
`,p=-(a+2),m=0,C=K?k:k+1,O++,P=""}break}return i.text=t,i.fontSize=a,i},co=function(r,t,e){var i=t.scope.internal.getFont(t.fontName,t.fontStyle),o=t.scope.getStringUnitWidth(r,{font:i,fontSize:parseFloat(e),charSpace:0})*parseFloat(e);return{height:t.scope.getStringUnitWidth("3",{font:i,fontSize:parseFloat(e),charSpace:0})*parseFloat(e)*1.5,width:o}},ec={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},rc=function(r,t){var e={type:"reference",object:r};t.internal.getPageInfo(r.page).pageContext.annotations.find(function(i){return i.type===e.type&&i.object===e.object})===void 0&&t.internal.getPageInfo(r.page).pageContext.annotations.push(e)},nc=function(r,t){for(var e in r)if(r.hasOwnProperty(e)){var i=e,o=r[e];t.internal.newObjectDeferredBegin(o.objId,!0),de(o)==="object"&&typeof o.putStream=="function"&&o.putStream(),delete r[i]}},ic=function(r,t){if(t.scope=r,r.internal!==void 0&&(r.internal.acroformPlugin===void 0||r.internal.acroformPlugin.isInitialized===!1)){if(qr.FieldNum=0,r.internal.acroformPlugin=JSON.parse(JSON.stringify(ec)),r.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");Js=r.internal.scaleFactor,r.internal.acroformPlugin.acroFormDictionaryRoot=new uu,r.internal.acroformPlugin.acroFormDictionaryRoot.scope=r,r.internal.acroformPlugin.acroFormDictionaryRoot._eventID=r.internal.events.subscribe("postPutResources",function(){(function(e){e.internal.events.unsubscribe(e.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete e.internal.acroformPlugin.acroFormDictionaryRoot._eventID,e.internal.acroformPlugin.printedOut=!0})(r)}),r.internal.events.subscribe("buildDocument",function(){(function(e){e.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var i=e.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];a.objId=void 0,a.hasAnnotation&&rc(a,e)}})(r)}),r.internal.events.subscribe("putCatalog",function(){(function(e){if(e.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");e.internal.write("/AcroForm "+e.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(r)}),r.internal.events.subscribe("postPutPages",function(e){(function(i,o){var a=!i;for(var l in i||(o.internal.newObjectDeferredBegin(o.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),o.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),i=i||o.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(i.hasOwnProperty(l)){var h=i[l],c=[],d=h.Rect;if(h.Rect&&(h.Rect=$h(h.Rect,o)),o.internal.newObjectDeferredBegin(h.objId,!0),h.DA=Ot.createDefaultAppearanceStream(h),de(h)==="object"&&typeof h.getKeyValueListForStream=="function"&&(c=h.getKeyValueListForStream()),h.Rect=d,h.hasAppearanceStream&&!h.appearanceStreamContent){var m=tc(h);c.push({key:"AP",value:"<</N "+m+">>"}),o.internal.acroformPlugin.xForms.push(m)}if(h.appearanceStreamContent){var v="";for(var w in h.appearanceStreamContent)if(h.appearanceStreamContent.hasOwnProperty(w)){var p=h.appearanceStreamContent[w];if(v+="/"+w+" ",v+="<<",Object.keys(p).length>=1||Array.isArray(p)){for(var l in p)if(p.hasOwnProperty(l)){var C=p[l];typeof C=="function"&&(C=C.call(o,h)),v+="/"+l+" "+C+" ",o.internal.acroformPlugin.xForms.indexOf(C)>=0||o.internal.acroformPlugin.xForms.push(C)}}else typeof(C=p)=="function"&&(C=C.call(o,h)),v+="/"+l+" "+C,o.internal.acroformPlugin.xForms.indexOf(C)>=0||o.internal.acroformPlugin.xForms.push(C);v+=">>"}c.push({key:"AP",value:`<<
`+v+">>"})}o.internal.putStream({additionalKeyValues:c,objectId:h.objId}),o.internal.out("endobj")}a&&nc(o.internal.acroformPlugin.xForms,o)})(e,r)}),r.internal.acroformPlugin.isInitialized=!0}},su=ke.__acroform__.arrayToPdfArray=function(r,t,e){var i=function(l){return l};if(Array.isArray(r)){for(var o="[",a=0;a<r.length;a++)switch(a!==0&&(o+=" "),de(r[a])){case"boolean":case"number":case"object":o+=r[a].toString();break;case"string":r[a].substr(0,1)!=="/"?(t!==void 0&&e&&(i=e.internal.getEncryptor(t)),o+="("+Zn(i(r[a].toString()))+")"):o+=r[a].toString()}return o+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},Ns=function(r,t,e){var i=function(o){return o};return t!==void 0&&e&&(i=e.internal.getEncryptor(t)),(r=r||"").toString(),r="("+Zn(i(r))+")"},$r=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(r){this._objId=r}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};$r.prototype.toString=function(){return this.objId+" 0 R"},$r.prototype.putStream=function(){var r=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:r,objectId:this.objId}),this.scope.internal.out("endobj")},$r.prototype.getKeyValueListForStream=function(){var r=[],t=Object.getOwnPropertyNames(this).filter(function(a){return a!="content"&&a!="appearanceStreamContent"&&a!="scope"&&a!="objId"&&a.substring(0,1)!="_"});for(var e in t)if(Object.getOwnPropertyDescriptor(this,t[e]).configurable===!1){var i=t[e],o=this[i];o&&(Array.isArray(o)?r.push({key:i,value:su(o,this.objId,this.scope)}):o instanceof $r?(o.scope=this.scope,r.push({key:i,value:o.objId+" 0 R"})):typeof o!="function"&&r.push({key:i,value:o}))}return r};var lu=function(){$r.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var r,t=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(e){r=e.trim()},get:function(){return r||null}})};cr(lu,$r);var uu=function(){$r.call(this);var r,t=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return t.length>0?t:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return t}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(r){var e=function(i){return i};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+Zn(e(r))+")"}},set:function(e){r=e}})};cr(uu,$r);var qr=function r(){$r.call(this);var t=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return t},set:function(P){if(isNaN(P))throw new Error('Invalid value "'+P+'" for attribute F supplied.');t=P}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!Ie(t,3)},set:function(P){P?this.F=De(t,3):this.F=Be(t,3)}});var e=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return e},set:function(P){if(isNaN(P))throw new Error('Invalid value "'+P+'" for attribute Ff supplied.');e=P}});var i=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(i.length!==0)return i},set:function(P){i=P!==void 0?P:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!i||isNaN(i[0])?0:i[0]},set:function(P){i[0]=P}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!i||isNaN(i[1])?0:i[1]},set:function(P){i[1]=P}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!i||isNaN(i[2])?0:i[2]},set:function(P){i[2]=P}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!i||isNaN(i[3])?0:i[3]},set:function(P){i[3]=P}});var o="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return o},set:function(P){switch(P){case"/Btn":case"/Tx":case"/Ch":case"/Sig":o=P;break;default:throw new Error('Invalid value "'+P+'" for attribute FT supplied.')}}});var a=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!a||a.length<1){if(this instanceof wo)return;a="FieldObject"+r.FieldNum++}var P=function(O){return O};return this.scope&&(P=this.scope.internal.getEncryptor(this.objId)),"("+Zn(P(a))+")"},set:function(P){a=P.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return a},set:function(P){a=P}});var l="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return l},set:function(P){l=P}});var h="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return h},set:function(P){h=P}});var c=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return c},set:function(P){c=P}});var d=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return d===void 0?50/Js:d},set:function(P){d=P}});var m="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return m},set:function(P){m=P}});var v="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!v||this instanceof wo||this instanceof Qn))return Ns(v,this.objId,this.scope)},set:function(P){P=P.toString(),v=P}});var w=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(w)return this instanceof We?w:Ns(w,this.objId,this.scope)},set:function(P){P=P.toString(),w=this instanceof We?P:P.substr(0,1)==="("?ki(P.substr(1,P.length-2)):ki(P)}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof We?ki(w.substr(1,w.length-1)):w},set:function(P){P=P.toString(),w=this instanceof We?"/"+P:P}});var p=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(p)return p},set:function(P){this.V=P}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(p)return this instanceof We?p:Ns(p,this.objId,this.scope)},set:function(P){P=P.toString(),p=this instanceof We?P:P.substr(0,1)==="("?ki(P.substr(1,P.length-2)):ki(P)}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof We?ki(p.substr(1,p.length-1)):p},set:function(P){P=P.toString(),p=this instanceof We?"/"+P:P}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var C,k=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return k},set:function(P){P=!!P,k=P}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(C)return C},set:function(P){C=P}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,1)},set:function(P){P?this.Ff=De(this.Ff,1):this.Ff=Be(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,2)},set:function(P){P?this.Ff=De(this.Ff,2):this.Ff=Be(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,3)},set:function(P){P?this.Ff=De(this.Ff,3):this.Ff=Be(this.Ff,3)}});var B=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(B!==null)return B},set:function(P){if([0,1,2].indexOf(P)===-1)throw new Error('Invalid value "'+P+'" for attribute Q supplied.');B=P}}),Object.defineProperty(this,"textAlign",{get:function(){var P;switch(B){case 0:default:P="left";break;case 1:P="center";break;case 2:P="right"}return P},configurable:!0,enumerable:!0,set:function(P){switch(P){case"right":case 2:B=2;break;case"center":case 1:B=1;break;case"left":case 0:default:B=0}}})};cr(qr,$r);var Di=function(){qr.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var r=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return r},set:function(e){r=e}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return r},set:function(e){r=e}});var t=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return su(t,this.objId,this.scope)},set:function(e){var i,o;o=[],typeof(i=e)=="string"&&(o=function(a,l,h){h||(h=1);for(var c,d=[];c=l.exec(a);)d.push(c[h]);return d}(i,/\((.*?)\)/g)),t=o}}),this.getOptions=function(){return t},this.setOptions=function(e){t=e,this.sort&&t.sort()},this.addOption=function(e){e=(e=e||"").toString(),t.push(e),this.sort&&t.sort()},this.removeOption=function(e,i){for(i=i||!1,e=(e=e||"").toString();t.indexOf(e)!==-1&&(t.splice(t.indexOf(e),1),i!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,18)},set:function(e){e?this.Ff=De(this.Ff,18):this.Ff=Be(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,19)},set:function(e){this.combo===!0&&(e?this.Ff=De(this.Ff,19):this.Ff=Be(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,20)},set:function(e){e?(this.Ff=De(this.Ff,20),t.sort()):this.Ff=Be(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,22)},set:function(e){e?this.Ff=De(this.Ff,22):this.Ff=Be(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,23)},set:function(e){e?this.Ff=De(this.Ff,23):this.Ff=Be(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,27)},set:function(e){e?this.Ff=De(this.Ff,27):this.Ff=Be(this.Ff,27)}}),this.hasAppearanceStream=!1};cr(Di,qr);var Bi=function(){Di.call(this),this.fontName="helvetica",this.combo=!1};cr(Bi,Di);var Oi=function(){Bi.call(this),this.combo=!0};cr(Oi,Bi);var go=function(){Oi.call(this),this.edit=!0};cr(go,Oi);var We=function(){qr.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,15)},set:function(e){e?this.Ff=De(this.Ff,15):this.Ff=Be(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,16)},set:function(e){e?this.Ff=De(this.Ff,16):this.Ff=Be(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,17)},set:function(e){e?this.Ff=De(this.Ff,17):this.Ff=Be(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,26)},set:function(e){e?this.Ff=De(this.Ff,26):this.Ff=Be(this.Ff,26)}});var r,t={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var e=function(a){return a};if(this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),Object.keys(t).length!==0){var i,o=[];for(i in o.push("<<"),t)o.push("/"+i+" ("+Zn(e(t[i]))+")");return o.push(">>"),o.join(`
`)}},set:function(e){de(e)==="object"&&(t=e)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return t.CA||""},set:function(e){typeof e=="string"&&(t.CA=e)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(e){r=e}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(e){r="/"+e}})};cr(We,qr);var mo=function(){We.call(this),this.pushButton=!0};cr(mo,We);var Mi=function(){We.call(this),this.radio=!0,this.pushButton=!1;var r=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return r},set:function(t){r=t!==void 0?t:[]}})};cr(Mi,We);var wo=function(){var r,t;qr.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return r},set:function(o){r=o}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return t},set:function(o){t=o}});var e,i={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var o=function(h){return h};this.scope&&(o=this.scope.internal.getEncryptor(this.objId));var a,l=[];for(a in l.push("<<"),i)l.push("/"+a+" ("+Zn(o(i[a]))+")");return l.push(">>"),l.join(`
`)},set:function(o){de(o)==="object"&&(i=o)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return i.CA||""},set:function(o){typeof o=="string"&&(i.CA=o)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return e},set:function(o){e=o}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return e.substr(1,e.length-1)},set:function(o){e="/"+o}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Ot.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};cr(wo,qr),Mi.prototype.setAppearance=function(r){if(!("createAppearanceStream"in r)||!("getCA"in r))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var t in this.Kids)if(this.Kids.hasOwnProperty(t)){var e=this.Kids[t];e.appearanceStreamContent=r.createAppearanceStream(e.optionName),e.caption=r.getCA()}},Mi.prototype.createOption=function(r){var t=new wo;return t.Parent=this,t.optionName=r,this.Kids.push(t),ac.call(this.scope,t),t};var vo=function(){We.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Ot.CheckBox.createAppearanceStream()};cr(vo,We);var Qn=function(){qr.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,13)},set:function(t){t?this.Ff=De(this.Ff,13):this.Ff=Be(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,21)},set:function(t){t?this.Ff=De(this.Ff,21):this.Ff=Be(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,23)},set:function(t){t?this.Ff=De(this.Ff,23):this.Ff=Be(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,24)},set:function(t){t?this.Ff=De(this.Ff,24):this.Ff=Be(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,25)},set:function(t){t?this.Ff=De(this.Ff,25):this.Ff=Be(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,26)},set:function(t){t?this.Ff=De(this.Ff,26):this.Ff=Be(this.Ff,26)}});var r=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return r},set:function(t){r=t}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return r},set:function(t){Number.isInteger(t)&&(r=t)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};cr(Qn,qr);var bo=function(){Qn.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!Ie(this.Ff,14)},set:function(r){r?this.Ff=De(this.Ff,14):this.Ff=Be(this.Ff,14)}}),this.password=!0};cr(bo,Qn);var Ot={CheckBox:{createAppearanceStream:function(){return{N:{On:Ot.CheckBox.YesNormal},D:{On:Ot.CheckBox.YesPushDown,Off:Ot.CheckBox.OffPushDown}}},YesPushDown:function(r){var t=Zr(r);t.scope=r.scope;var e=[],i=r.scope.internal.getFont(r.fontName,r.fontStyle).id,o=r.scope.__private__.encodeColorString(r.color),a=Rs(r,r.caption);return e.push("0.749023 g"),e.push("0 0 "+Xt(Ot.internal.getWidth(r))+" "+Xt(Ot.internal.getHeight(r))+" re"),e.push("f"),e.push("BMC"),e.push("q"),e.push("0 0 1 rg"),e.push("/"+i+" "+Xt(a.fontSize)+" Tf "+o),e.push("BT"),e.push(a.text),e.push("ET"),e.push("Q"),e.push("EMC"),t.stream=e.join(`
`),t},YesNormal:function(r){var t=Zr(r);t.scope=r.scope;var e=r.scope.internal.getFont(r.fontName,r.fontStyle).id,i=r.scope.__private__.encodeColorString(r.color),o=[],a=Ot.internal.getHeight(r),l=Ot.internal.getWidth(r),h=Rs(r,r.caption);return o.push("1 g"),o.push("0 0 "+Xt(l)+" "+Xt(a)+" re"),o.push("f"),o.push("q"),o.push("0 0 1 rg"),o.push("0 0 "+Xt(l-1)+" "+Xt(a-1)+" re"),o.push("W"),o.push("n"),o.push("0 g"),o.push("BT"),o.push("/"+e+" "+Xt(h.fontSize)+" Tf "+i),o.push(h.text),o.push("ET"),o.push("Q"),t.stream=o.join(`
`),t},OffPushDown:function(r){var t=Zr(r);t.scope=r.scope;var e=[];return e.push("0.749023 g"),e.push("0 0 "+Xt(Ot.internal.getWidth(r))+" "+Xt(Ot.internal.getHeight(r))+" re"),e.push("f"),t.stream=e.join(`
`),t}},RadioButton:{Circle:{createAppearanceStream:function(r){var t={D:{Off:Ot.RadioButton.Circle.OffPushDown},N:{}};return t.N[r]=Ot.RadioButton.Circle.YesNormal,t.D[r]=Ot.RadioButton.Circle.YesPushDown,t},getCA:function(){return"l"},YesNormal:function(r){var t=Zr(r);t.scope=r.scope;var e=[],i=Ot.internal.getWidth(r)<=Ot.internal.getHeight(r)?Ot.internal.getWidth(r)/4:Ot.internal.getHeight(r)/4;i=Number((.9*i).toFixed(5));var o=Ot.internal.Bezier_C,a=Number((i*o).toFixed(5));return e.push("q"),e.push("1 0 0 1 "+In(Ot.internal.getWidth(r)/2)+" "+In(Ot.internal.getHeight(r)/2)+" cm"),e.push(i+" 0 m"),e.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),e.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),e.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),e.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),e.push("f"),e.push("Q"),t.stream=e.join(`
`),t},YesPushDown:function(r){var t=Zr(r);t.scope=r.scope;var e=[],i=Ot.internal.getWidth(r)<=Ot.internal.getHeight(r)?Ot.internal.getWidth(r)/4:Ot.internal.getHeight(r)/4;i=Number((.9*i).toFixed(5));var o=Number((2*i).toFixed(5)),a=Number((o*Ot.internal.Bezier_C).toFixed(5)),l=Number((i*Ot.internal.Bezier_C).toFixed(5));return e.push("0.749023 g"),e.push("q"),e.push("1 0 0 1 "+In(Ot.internal.getWidth(r)/2)+" "+In(Ot.internal.getHeight(r)/2)+" cm"),e.push(o+" 0 m"),e.push(o+" "+a+" "+a+" "+o+" 0 "+o+" c"),e.push("-"+a+" "+o+" -"+o+" "+a+" -"+o+" 0 c"),e.push("-"+o+" -"+a+" -"+a+" -"+o+" 0 -"+o+" c"),e.push(a+" -"+o+" "+o+" -"+a+" "+o+" 0 c"),e.push("f"),e.push("Q"),e.push("0 g"),e.push("q"),e.push("1 0 0 1 "+In(Ot.internal.getWidth(r)/2)+" "+In(Ot.internal.getHeight(r)/2)+" cm"),e.push(i+" 0 m"),e.push(i+" "+l+" "+l+" "+i+" 0 "+i+" c"),e.push("-"+l+" "+i+" -"+i+" "+l+" -"+i+" 0 c"),e.push("-"+i+" -"+l+" -"+l+" -"+i+" 0 -"+i+" c"),e.push(l+" -"+i+" "+i+" -"+l+" "+i+" 0 c"),e.push("f"),e.push("Q"),t.stream=e.join(`
`),t},OffPushDown:function(r){var t=Zr(r);t.scope=r.scope;var e=[],i=Ot.internal.getWidth(r)<=Ot.internal.getHeight(r)?Ot.internal.getWidth(r)/4:Ot.internal.getHeight(r)/4;i=Number((.9*i).toFixed(5));var o=Number((2*i).toFixed(5)),a=Number((o*Ot.internal.Bezier_C).toFixed(5));return e.push("0.749023 g"),e.push("q"),e.push("1 0 0 1 "+In(Ot.internal.getWidth(r)/2)+" "+In(Ot.internal.getHeight(r)/2)+" cm"),e.push(o+" 0 m"),e.push(o+" "+a+" "+a+" "+o+" 0 "+o+" c"),e.push("-"+a+" "+o+" -"+o+" "+a+" -"+o+" 0 c"),e.push("-"+o+" -"+a+" -"+a+" -"+o+" 0 -"+o+" c"),e.push(a+" -"+o+" "+o+" -"+a+" "+o+" 0 c"),e.push("f"),e.push("Q"),t.stream=e.join(`
`),t}},Cross:{createAppearanceStream:function(r){var t={D:{Off:Ot.RadioButton.Cross.OffPushDown},N:{}};return t.N[r]=Ot.RadioButton.Cross.YesNormal,t.D[r]=Ot.RadioButton.Cross.YesPushDown,t},getCA:function(){return"8"},YesNormal:function(r){var t=Zr(r);t.scope=r.scope;var e=[],i=Ot.internal.calculateCross(r);return e.push("q"),e.push("1 1 "+Xt(Ot.internal.getWidth(r)-2)+" "+Xt(Ot.internal.getHeight(r)-2)+" re"),e.push("W"),e.push("n"),e.push(Xt(i.x1.x)+" "+Xt(i.x1.y)+" m"),e.push(Xt(i.x2.x)+" "+Xt(i.x2.y)+" l"),e.push(Xt(i.x4.x)+" "+Xt(i.x4.y)+" m"),e.push(Xt(i.x3.x)+" "+Xt(i.x3.y)+" l"),e.push("s"),e.push("Q"),t.stream=e.join(`
`),t},YesPushDown:function(r){var t=Zr(r);t.scope=r.scope;var e=Ot.internal.calculateCross(r),i=[];return i.push("0.749023 g"),i.push("0 0 "+Xt(Ot.internal.getWidth(r))+" "+Xt(Ot.internal.getHeight(r))+" re"),i.push("f"),i.push("q"),i.push("1 1 "+Xt(Ot.internal.getWidth(r)-2)+" "+Xt(Ot.internal.getHeight(r)-2)+" re"),i.push("W"),i.push("n"),i.push(Xt(e.x1.x)+" "+Xt(e.x1.y)+" m"),i.push(Xt(e.x2.x)+" "+Xt(e.x2.y)+" l"),i.push(Xt(e.x4.x)+" "+Xt(e.x4.y)+" m"),i.push(Xt(e.x3.x)+" "+Xt(e.x3.y)+" l"),i.push("s"),i.push("Q"),t.stream=i.join(`
`),t},OffPushDown:function(r){var t=Zr(r);t.scope=r.scope;var e=[];return e.push("0.749023 g"),e.push("0 0 "+Xt(Ot.internal.getWidth(r))+" "+Xt(Ot.internal.getHeight(r))+" re"),e.push("f"),t.stream=e.join(`
`),t}}},createDefaultAppearanceStream:function(r){var t=r.scope.internal.getFont(r.fontName,r.fontStyle).id,e=r.scope.__private__.encodeColorString(r.color);return"/"+t+" "+r.fontSize+" Tf "+e}};Ot.internal={Bezier_C:.551915024494,calculateCross:function(r){var t=Ot.internal.getWidth(r),e=Ot.internal.getHeight(r),i=Math.min(t,e);return{x1:{x:(t-i)/2,y:(e-i)/2+i},x2:{x:(t-i)/2+i,y:(e-i)/2},x3:{x:(t-i)/2,y:(e-i)/2},x4:{x:(t-i)/2+i,y:(e-i)/2+i}}}},Ot.internal.getWidth=function(r){var t=0;return de(r)==="object"&&(t=jl(r.Rect[2])),t},Ot.internal.getHeight=function(r){var t=0;return de(r)==="object"&&(t=jl(r.Rect[3])),t};var ac=ke.addField=function(r){if(ic(this,r),!(r instanceof qr))throw new Error("Invalid argument passed to jsPDF.addField.");var t;return(t=r).scope.internal.acroformPlugin.printedOut&&(t.scope.internal.acroformPlugin.printedOut=!1,t.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),t.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(t),r.page=r.scope.internal.getCurrentPageInfo().pageNumber,this};ke.AcroFormChoiceField=Di,ke.AcroFormListBox=Bi,ke.AcroFormComboBox=Oi,ke.AcroFormEditBox=go,ke.AcroFormButton=We,ke.AcroFormPushButton=mo,ke.AcroFormRadioButton=Mi,ke.AcroFormCheckBox=vo,ke.AcroFormTextField=Qn,ke.AcroFormPasswordField=bo,ke.AcroFormAppearance=Ot,ke.AcroForm={ChoiceField:Di,ListBox:Bi,ComboBox:Oi,EditBox:go,Button:We,PushButton:mo,RadioButton:Mi,CheckBox:vo,TextField:Qn,PasswordField:bo,Appearance:Ot},Ut.AcroForm={ChoiceField:Di,ListBox:Bi,ComboBox:Oi,EditBox:go,Button:We,PushButton:mo,RadioButton:Mi,CheckBox:vo,TextField:Qn,PasswordField:bo,Appearance:Ot};Ut.AcroForm;function hu(r){return r.reduce(function(t,e,i){return t[e]=i,t},{})}(function(r){r.__addimage__={};var t="UNKNOWN",e={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},i=r.__addimage__.getImageFileTypeByImageData=function(_,j){var V,R,ot,nt,ut,Z=t;if((j=j||t)==="RGBA"||_.data!==void 0&&_.data instanceof Uint8ClampedArray&&"height"in _&&"width"in _)return"RGBA";if(yt(_))for(ut in e)for(ot=e[ut],V=0;V<ot.length;V+=1){for(nt=!0,R=0;R<ot[V].length;R+=1)if(ot[V][R]!==void 0&&ot[V][R]!==_[R]){nt=!1;break}if(nt===!0){Z=ut;break}}else for(ut in e)for(ot=e[ut],V=0;V<ot.length;V+=1){for(nt=!0,R=0;R<ot[V].length;R+=1)if(ot[V][R]!==void 0&&ot[V][R]!==_.charCodeAt(R)){nt=!1;break}if(nt===!0){Z=ut;break}}return Z===t&&j!==t&&(Z=j),Z},o=function _(j){for(var V=this.internal.write,R=this.internal.putStream,ot=(0,this.internal.getFilters)();ot.indexOf("FlateEncode")!==-1;)ot.splice(ot.indexOf("FlateEncode"),1);j.objectId=this.internal.newObject();var nt=[];if(nt.push({key:"Type",value:"/XObject"}),nt.push({key:"Subtype",value:"/Image"}),nt.push({key:"Width",value:j.width}),nt.push({key:"Height",value:j.height}),j.colorSpace===B.INDEXED?nt.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(j.palette.length/3-1)+" "+("sMask"in j&&j.sMask!==void 0?j.objectId+2:j.objectId+1)+" 0 R]"}):(nt.push({key:"ColorSpace",value:"/"+j.colorSpace}),j.colorSpace===B.DEVICE_CMYK&&nt.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),nt.push({key:"BitsPerComponent",value:j.bitsPerComponent}),"decodeParameters"in j&&j.decodeParameters!==void 0&&nt.push({key:"DecodeParms",value:"<<"+j.decodeParameters+">>"}),"transparency"in j&&Array.isArray(j.transparency)){for(var ut="",Z=0,ht=j.transparency.length;Z<ht;Z++)ut+=j.transparency[Z]+" "+j.transparency[Z]+" ";nt.push({key:"Mask",value:"["+ut+"]"})}j.sMask!==void 0&&nt.push({key:"SMask",value:j.objectId+1+" 0 R"});var dt=j.filter!==void 0?["/"+j.filter]:void 0;if(R({data:j.data,additionalKeyValues:nt,alreadyAppliedFilters:dt,objectId:j.objectId}),V("endobj"),"sMask"in j&&j.sMask!==void 0){var It="/Predictor "+j.predictor+" /Colors 1 /BitsPerComponent "+j.bitsPerComponent+" /Columns "+j.width,N={width:j.width,height:j.height,colorSpace:"DeviceGray",bitsPerComponent:j.bitsPerComponent,decodeParameters:It,data:j.sMask};"filter"in j&&(N.filter=j.filter),_.call(this,N)}if(j.colorSpace===B.INDEXED){var I=this.internal.newObject();R({data:q(new Uint8Array(j.palette)),objectId:I}),V("endobj")}},a=function(){var _=this.internal.collections.addImage_images;for(var j in _)o.call(this,_[j])},l=function(){var _,j=this.internal.collections.addImage_images,V=this.internal.write;for(var R in j)V("/I"+(_=j[R]).index,_.objectId,"0","R")},h=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",a),this.internal.events.subscribe("putXobjectDict",l))},c=function(){var _=this.internal.collections.addImage_images;return h.call(this),_},d=function(){return Object.keys(this.internal.collections.addImage_images).length},m=function(_){return typeof r["process"+_.toUpperCase()]=="function"},v=function(_){return de(_)==="object"&&_.nodeType===1},w=function(_,j){if(_.nodeName==="IMG"&&_.hasAttribute("src")){var V=""+_.getAttribute("src");if(V.indexOf("data:image/")===0)return ma(unescape(V).split("base64,").pop());var R=r.loadFile(V,!0);if(R!==void 0)return R}if(_.nodeName==="CANVAS"){if(_.width===0||_.height===0)throw new Error("Given canvas must have data. Canvas width: "+_.width+", height: "+_.height);var ot;switch(j){case"PNG":ot="image/png";break;case"WEBP":ot="image/webp";break;case"JPEG":case"JPG":default:ot="image/jpeg"}return ma(_.toDataURL(ot,1).split("base64,").pop())}},p=function(_){var j=this.internal.collections.addImage_images;if(j){for(var V in j)if(_===j[V].alias)return j[V]}},C=function(_,j,V){return _||j||(_=-96,j=-96),_<0&&(_=-1*V.width*72/_/this.internal.scaleFactor),j<0&&(j=-1*V.height*72/j/this.internal.scaleFactor),_===0&&(_=j*V.width/V.height),j===0&&(j=_*V.height/V.width),[_,j]},k=function(_,j,V,R,ot,nt){var ut=C.call(this,V,R,ot),Z=this.internal.getCoordinateString,ht=this.internal.getVerticalCoordinateString,dt=c.call(this);if(V=ut[0],R=ut[1],dt[ot.index]=ot,nt){nt*=Math.PI/180;var It=Math.cos(nt),N=Math.sin(nt),I=function(U){return U.toFixed(4)},M=[I(It),I(N),I(-1*N),I(It),0,0,"cm"]}this.internal.write("q"),nt?(this.internal.write([1,"0","0",1,Z(_),ht(j+R),"cm"].join(" ")),this.internal.write(M.join(" ")),this.internal.write([Z(V),"0","0",Z(R),"0","0","cm"].join(" "))):this.internal.write([Z(V),"0","0",Z(R),Z(_),ht(j+R),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+ot.index+" Do"),this.internal.write("Q")},B=r.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};r.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var P=r.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},O=r.__addimage__.sHashCode=function(_){var j,V,R=0;if(typeof _=="string")for(V=_.length,j=0;j<V;j++)R=(R<<5)-R+_.charCodeAt(j),R|=0;else if(yt(_))for(V=_.byteLength/2,j=0;j<V;j++)R=(R<<5)-R+_[j],R|=0;return R},E=r.__addimage__.validateStringAsBase64=function(_){(_=_||"").toString().trim();var j=!0;return _.length===0&&(j=!1),_.length%4!=0&&(j=!1),/^[A-Za-z0-9+/]+$/.test(_.substr(0,_.length-2))===!1&&(j=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(_.substr(-2))===!1&&(j=!1),j},K=r.__addimage__.extractImageFromDataUrl=function(_){if(_==null||!(_=_.trim()).startsWith("data:"))return null;var j=_.indexOf(",");return j<0?null:_.substring(0,j).trim().endsWith("base64")?_.substring(j+1):null},st=r.__addimage__.supportsArrayBuffer=function(){return typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"};r.__addimage__.isArrayBuffer=function(_){return st()&&_ instanceof ArrayBuffer};var yt=r.__addimage__.isArrayBufferView=function(_){return st()&&typeof Uint32Array<"u"&&(_ instanceof Int8Array||_ instanceof Uint8Array||typeof Uint8ClampedArray<"u"&&_ instanceof Uint8ClampedArray||_ instanceof Int16Array||_ instanceof Uint16Array||_ instanceof Int32Array||_ instanceof Uint32Array||_ instanceof Float32Array||_ instanceof Float64Array)},Q=r.__addimage__.binaryStringToUint8Array=function(_){for(var j=_.length,V=new Uint8Array(j),R=0;R<j;R++)V[R]=_.charCodeAt(R);return V},q=r.__addimage__.arrayBufferToBinaryString=function(_){for(var j="",V=yt(_)?_:new Uint8Array(_),R=0;R<V.length;R+=8192)j+=String.fromCharCode.apply(null,V.subarray(R,R+8192));return j};r.addImage=function(){var _,j,V,R,ot,nt,ut,Z,ht;if(typeof arguments[1]=="number"?(j=t,V=arguments[1],R=arguments[2],ot=arguments[3],nt=arguments[4],ut=arguments[5],Z=arguments[6],ht=arguments[7]):(j=arguments[1],V=arguments[2],R=arguments[3],ot=arguments[4],nt=arguments[5],ut=arguments[6],Z=arguments[7],ht=arguments[8]),de(_=arguments[0])==="object"&&!v(_)&&"imageData"in _){var dt=_;_=dt.imageData,j=dt.format||j||t,V=dt.x||V||0,R=dt.y||R||0,ot=dt.w||dt.width||ot,nt=dt.h||dt.height||nt,ut=dt.alias||ut,Z=dt.compression||Z,ht=dt.rotation||dt.angle||ht}var It=this.internal.getFilters();if(Z===void 0&&It.indexOf("FlateEncode")!==-1&&(Z="SLOW"),isNaN(V)||isNaN(R))throw new Error("Invalid coordinates passed to jsPDF.addImage");h.call(this);var N=rt.call(this,_,j,ut,Z);return k.call(this,V,R,ot,nt,N,ht),this};var rt=function(_,j,V,R){var ot,nt,ut;if(typeof _=="string"&&i(_)===t){_=unescape(_);var Z=pt(_,!1);(Z!==""||(Z=r.loadFile(_,!0))!==void 0)&&(_=Z)}if(v(_)&&(_=w(_,j)),j=i(_,j),!m(j))throw new Error("addImage does not support files of type '"+j+"', please ensure that a plugin for '"+j+"' support is added.");if(((ut=V)==null||ut.length===0)&&(V=function(ht){return typeof ht=="string"||yt(ht)?O(ht):yt(ht.data)?O(ht.data):null}(_)),(ot=p.call(this,V))||(st()&&(_ instanceof Uint8Array||j==="RGBA"||(nt=_,_=Q(_))),ot=this["process"+j.toUpperCase()](_,d.call(this),V,function(ht){return ht&&typeof ht=="string"&&(ht=ht.toUpperCase()),ht in r.image_compression?ht:P.NONE}(R),nt)),!ot)throw new Error("An unknown error occurred whilst processing the image.");return ot},pt=r.__addimage__.convertBase64ToBinaryString=function(_,j){j=typeof j!="boolean"||j;var V,R="";if(typeof _=="string"){var ot;V=(ot=K(_))!==null&&ot!==void 0?ot:_;try{R=ma(V)}catch(nt){if(j)throw E(V)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+nt.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return R};r.getImageProperties=function(_){var j,V,R="";if(v(_)&&(_=w(_)),typeof _=="string"&&i(_)===t&&((R=pt(_,!1))===""&&(R=r.loadFile(_)||""),_=R),V=i(_),!m(V))throw new Error("addImage does not support files of type '"+V+"', please ensure that a plugin for '"+V+"' support is added.");if(!st()||_ instanceof Uint8Array||(_=Q(_)),!(j=this["process"+V.toUpperCase()](_)))throw new Error("An unknown error occurred whilst processing the image");return j.fileType=V,j}})(Ut.API),function(r){var t=function(e){if(e!==void 0&&e!="")return!0};Ut.API.events.push(["addPage",function(e){this.internal.getPageInfo(e.pageNumber).pageContext.annotations=[]}]),r.events.push(["putPage",function(e){for(var i,o,a,l=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString,c=this.internal.getPageInfoByObjId(e.objId),d=e.pageContext.annotations,m=!1,v=0;v<d.length&&!m;v++)switch((i=d[v]).type){case"link":(t(i.options.url)||t(i.options.pageNumber))&&(m=!0);break;case"reference":case"text":case"freetext":m=!0}if(m!=0){this.internal.write("/Annots [");for(var w=0;w<d.length;w++){i=d[w];var p=this.internal.pdfEscape,C=this.internal.getEncryptor(e.objId);switch(i.type){case"reference":this.internal.write(" "+i.object.objId+" 0 R ");break;case"text":var k=this.internal.newAdditionalObject(),B=this.internal.newAdditionalObject(),P=this.internal.getEncryptor(k.objId),O=i.title||"Note";a="<</Type /Annot /Subtype /Text "+(o="/Rect ["+l(i.bounds.x)+" "+h(i.bounds.y+i.bounds.h)+" "+l(i.bounds.x+i.bounds.w)+" "+h(i.bounds.y)+"] ")+"/Contents ("+p(P(i.contents))+")",a+=" /Popup "+B.objId+" 0 R",a+=" /P "+c.objId+" 0 R",a+=" /T ("+p(P(O))+") >>",k.content=a;var E=k.objId+" 0 R";a="<</Type /Annot /Subtype /Popup "+(o="/Rect ["+l(i.bounds.x+30)+" "+h(i.bounds.y+i.bounds.h)+" "+l(i.bounds.x+i.bounds.w+30)+" "+h(i.bounds.y)+"] ")+" /Parent "+E,i.open&&(a+=" /Open true"),a+=" >>",B.content=a,this.internal.write(k.objId,"0 R",B.objId,"0 R");break;case"freetext":o="/Rect ["+l(i.bounds.x)+" "+h(i.bounds.y)+" "+l(i.bounds.x+i.bounds.w)+" "+h(i.bounds.y+i.bounds.h)+"] ";var K=i.color||"#000000";a="<</Type /Annot /Subtype /FreeText "+o+"/Contents ("+p(C(i.contents))+")",a+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+K+")",a+=" /Border [0 0 0]",a+=" >>",this.internal.write(a);break;case"link":if(i.options.name){var st=this.annotations._nameMap[i.options.name];i.options.pageNumber=st.page,i.options.top=st.y}else i.options.top||(i.options.top=0);if(o="/Rect ["+i.finalBounds.x+" "+i.finalBounds.y+" "+i.finalBounds.w+" "+i.finalBounds.h+"] ",a="",i.options.url)a="<</Type /Annot /Subtype /Link "+o+"/Border [0 0 0] /A <</S /URI /URI ("+p(C(i.options.url))+") >>";else if(i.options.pageNumber)switch(a="<</Type /Annot /Subtype /Link "+o+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(i.options.pageNumber).objId+" 0 R",i.options.magFactor=i.options.magFactor||"XYZ",i.options.magFactor){case"Fit":a+=" /Fit]";break;case"FitH":a+=" /FitH "+i.options.top+"]";break;case"FitV":i.options.left=i.options.left||0,a+=" /FitV "+i.options.left+"]";break;case"XYZ":default:var yt=h(i.options.top);i.options.left=i.options.left||0,i.options.zoom===void 0&&(i.options.zoom=0),a+=" /XYZ "+i.options.left+" "+yt+" "+i.options.zoom+"]"}a!=""&&(a+=" >>",this.internal.write(a))}}this.internal.write("]")}}]),r.createAnnotation=function(e){var i=this.internal.getCurrentPageInfo();switch(e.type){case"link":this.link(e.bounds.x,e.bounds.y,e.bounds.w,e.bounds.h,e);break;case"text":case"freetext":i.pageContext.annotations.push(e)}},r.link=function(e,i,o,a,l){var h=this.internal.getCurrentPageInfo(),c=this.internal.getCoordinateString,d=this.internal.getVerticalCoordinateString;h.pageContext.annotations.push({finalBounds:{x:c(e),y:d(i),w:c(e+o),h:d(i+a)},options:l,type:"link"})},r.textWithLink=function(e,i,o,a){var l,h,c=this.getTextWidth(e),d=this.internal.getLineHeight()/this.internal.scaleFactor;if(a.maxWidth!==void 0){h=a.maxWidth;var m=this.splitTextToSize(e,h).length;l=Math.ceil(d*m)}else h=c,l=d;return this.text(e,i,o,a),o+=.2*d,a.align==="center"&&(i-=c/2),a.align==="right"&&(i-=c),this.link(i,o-d,h,l,a),c},r.getTextWidth=function(e){var i=this.internal.getFontSize();return this.getStringUnitWidth(e)*i/this.internal.scaleFactor}}(Ut.API),function(r){var t={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},e={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},i={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},o=[1570,1571,1573,1575];r.__arabicParser__={};var a=r.__arabicParser__.isInArabicSubstitutionA=function(k){return t[k.charCodeAt(0)]!==void 0},l=r.__arabicParser__.isArabicLetter=function(k){return typeof k=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(k)},h=r.__arabicParser__.isArabicEndLetter=function(k){return l(k)&&a(k)&&t[k.charCodeAt(0)].length<=2},c=r.__arabicParser__.isArabicAlfLetter=function(k){return l(k)&&o.indexOf(k.charCodeAt(0))>=0};r.__arabicParser__.arabicLetterHasIsolatedForm=function(k){return l(k)&&a(k)&&t[k.charCodeAt(0)].length>=1};var d=r.__arabicParser__.arabicLetterHasFinalForm=function(k){return l(k)&&a(k)&&t[k.charCodeAt(0)].length>=2};r.__arabicParser__.arabicLetterHasInitialForm=function(k){return l(k)&&a(k)&&t[k.charCodeAt(0)].length>=3};var m=r.__arabicParser__.arabicLetterHasMedialForm=function(k){return l(k)&&a(k)&&t[k.charCodeAt(0)].length==4},v=r.__arabicParser__.resolveLigatures=function(k){var B=0,P=e,O="",E=0;for(B=0;B<k.length;B+=1)P[k.charCodeAt(B)]!==void 0?(E++,typeof(P=P[k.charCodeAt(B)])=="number"&&(O+=String.fromCharCode(P),P=e,E=0),B===k.length-1&&(P=e,O+=k.charAt(B-(E-1)),B-=E-1,E=0)):(P=e,O+=k.charAt(B-E),B-=E,E=0);return O};r.__arabicParser__.isArabicDiacritic=function(k){return k!==void 0&&i[k.charCodeAt(0)]!==void 0};var w=r.__arabicParser__.getCorrectForm=function(k,B,P){return l(k)?a(k)===!1?-1:!d(k)||!l(B)&&!l(P)||!l(P)&&h(B)||h(k)&&!l(B)||h(k)&&c(B)||h(k)&&h(B)?0:m(k)&&l(B)&&!h(B)&&l(P)&&d(P)?3:h(k)||!l(P)?1:2:-1},p=function(k){var B=0,P=0,O=0,E="",K="",st="",yt=(k=k||"").split("\\s+"),Q=[];for(B=0;B<yt.length;B+=1){for(Q.push(""),P=0;P<yt[B].length;P+=1)E=yt[B][P],K=yt[B][P-1],st=yt[B][P+1],l(E)?(O=w(E,K,st),Q[B]+=O!==-1?String.fromCharCode(t[E.charCodeAt(0)][O]):E):Q[B]+=E;Q[B]=v(Q[B])}return Q.join(" ")},C=r.__arabicParser__.processArabic=r.processArabic=function(){var k,B=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,P=[];if(Array.isArray(B)){var O=0;for(P=[],O=0;O<B.length;O+=1)Array.isArray(B[O])?P.push([p(B[O][0]),B[O][1],B[O][2]]):P.push([p(B[O])]);k=P}else k=p(B);return typeof arguments[0]=="string"?k:(arguments[0].text=k,arguments[0])};r.events.push(["preProcessText",C])}(Ut.API),Ut.API.autoPrint=function(r){var t;switch((r=r||{}).variant=r.variant||"non-conform",r.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){t=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+t+" 0 R")})}return this},function(r){var t=function(){var e=void 0;Object.defineProperty(this,"pdf",{get:function(){return e},set:function(h){e=h}});var i=150;Object.defineProperty(this,"width",{get:function(){return i},set:function(h){i=isNaN(h)||Number.isInteger(h)===!1||h<0?150:h,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=i+1)}});var o=300;Object.defineProperty(this,"height",{get:function(){return o},set:function(h){o=isNaN(h)||Number.isInteger(h)===!1||h<0?300:h,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=o+1)}});var a=[];Object.defineProperty(this,"childNodes",{get:function(){return a},set:function(h){a=h}});var l={};Object.defineProperty(this,"style",{get:function(){return l},set:function(h){l=h}}),Object.defineProperty(this,"parentNode",{})};t.prototype.getContext=function(e,i){var o;if((e=e||"2d")!=="2d")return null;for(o in i)this.pdf.context2d.hasOwnProperty(o)&&(this.pdf.context2d[o]=i[o]);return this.pdf.context2d._canvas=this,this.pdf.context2d},t.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},r.events.push(["initialized",function(){this.canvas=new t,this.canvas.pdf=this}])}(Ut.API),function(r){var t={left:0,top:0,bottom:0,right:0},e=!1,i=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},t),this.internal.__cell__.margins.width=this.getPageWidth(),o.call(this))},o=function(){this.internal.__cell__.lastCell=new a,this.internal.__cell__.pages=1},a=function(){var c=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return c},set:function(k){c=k}});var d=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return d},set:function(k){d=k}});var m=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return m},set:function(k){m=k}});var v=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return v},set:function(k){v=k}});var w=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return w},set:function(k){w=k}});var p=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return p},set:function(k){p=k}});var C=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return C},set:function(k){C=k}}),this};a.prototype.clone=function(){return new a(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},a.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},r.setHeaderFunction=function(c){return i.call(this),this.internal.__cell__.headerFunction=typeof c=="function"?c:void 0,this},r.getTextDimensions=function(c,d){i.call(this);var m=(d=d||{}).fontSize||this.getFontSize(),v=d.font||this.getFont(),w=d.scaleFactor||this.internal.scaleFactor,p=0,C=0,k=0,B=this;if(!Array.isArray(c)&&typeof c!="string"){if(typeof c!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");c=String(c)}var P=d.maxWidth;P>0?typeof c=="string"?c=this.splitTextToSize(c,P):Object.prototype.toString.call(c)==="[object Array]"&&(c=c.reduce(function(E,K){return E.concat(B.splitTextToSize(K,P))},[])):c=Array.isArray(c)?c:[c];for(var O=0;O<c.length;O++)p<(k=this.getStringUnitWidth(c[O],{font:v})*m)&&(p=k);return p!==0&&(C=c.length),{w:p/=w,h:Math.max((C*m*this.getLineHeightFactor()-m*(this.getLineHeightFactor()-1))/w,0)}},r.cellAddPage=function(){i.call(this),this.addPage();var c=this.internal.__cell__.margins||t;return this.internal.__cell__.lastCell=new a(c.left,c.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var l=r.cell=function(){var c;c=arguments[0]instanceof a?arguments[0]:new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),i.call(this);var d=this.internal.__cell__.lastCell,m=this.internal.__cell__.padding,v=this.internal.__cell__.margins||t,w=this.internal.__cell__.tableHeaderRow,p=this.internal.__cell__.printHeaders;return d.lineNumber!==void 0&&(d.lineNumber===c.lineNumber?(c.x=(d.x||0)+(d.width||0),c.y=d.y||0):d.y+d.height+c.height+v.bottom>this.getPageHeight()?(this.cellAddPage(),c.y=v.top,p&&w&&(this.printHeaderRow(c.lineNumber,!0),c.y+=w[0].height)):c.y=d.y+d.height||c.y),c.text[0]!==void 0&&(this.rect(c.x,c.y,c.width,c.height,e===!0?"FD":void 0),c.align==="right"?this.text(c.text,c.x+c.width-m,c.y+m,{align:"right",baseline:"top"}):c.align==="center"?this.text(c.text,c.x+c.width/2,c.y+m,{align:"center",baseline:"top",maxWidth:c.width-m-m}):this.text(c.text,c.x+m,c.y+m,{align:"left",baseline:"top",maxWidth:c.width-m-m})),this.internal.__cell__.lastCell=c,this};r.table=function(c,d,m,v,w){if(i.call(this),!m)throw new Error("No data for PDF table.");var p,C,k,B,P=[],O=[],E=[],K={},st={},yt=[],Q=[],q=(w=w||{}).autoSize||!1,rt=w.printHeaders!==!1,pt=w.css&&w.css["font-size"]!==void 0?16*w.css["font-size"]:w.fontSize||12,_=w.margins||Object.assign({width:this.getPageWidth()},t),j=typeof w.padding=="number"?w.padding:3,V=w.headerBackgroundColor||"#c8c8c8",R=w.headerTextColor||"#000";if(o.call(this),this.internal.__cell__.printHeaders=rt,this.internal.__cell__.margins=_,this.internal.__cell__.table_font_size=pt,this.internal.__cell__.padding=j,this.internal.__cell__.headerBackgroundColor=V,this.internal.__cell__.headerTextColor=R,this.setFontSize(pt),v==null)O=P=Object.keys(m[0]),E=P.map(function(){return"left"});else if(Array.isArray(v)&&de(v[0])==="object")for(P=v.map(function(dt){return dt.name}),O=v.map(function(dt){return dt.prompt||dt.name||""}),E=v.map(function(dt){return dt.align||"left"}),p=0;p<v.length;p+=1)st[v[p].name]=v[p].width*(19.049976/25.4);else Array.isArray(v)&&typeof v[0]=="string"&&(O=P=v,E=P.map(function(){return"left"}));if(q||Array.isArray(v)&&typeof v[0]=="string")for(p=0;p<P.length;p+=1){for(K[B=P[p]]=m.map(function(dt){return dt[B]}),this.setFont(void 0,"bold"),yt.push(this.getTextDimensions(O[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),C=K[B],this.setFont(void 0,"normal"),k=0;k<C.length;k+=1)yt.push(this.getTextDimensions(C[k],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);st[B]=Math.max.apply(null,yt)+j+j,yt=[]}if(rt){var ot={};for(p=0;p<P.length;p+=1)ot[P[p]]={},ot[P[p]].text=O[p],ot[P[p]].align=E[p];var nt=h.call(this,ot,st);Q=P.map(function(dt){return new a(c,d,st[dt],nt,ot[dt].text,void 0,ot[dt].align)}),this.setTableHeaderRow(Q),this.printHeaderRow(1,!1)}var ut=v.reduce(function(dt,It){return dt[It.name]=It.align,dt},{});for(p=0;p<m.length;p+=1){"rowStart"in w&&w.rowStart instanceof Function&&w.rowStart({row:p,data:m[p]},this);var Z=h.call(this,m[p],st);for(k=0;k<P.length;k+=1){var ht=m[p][P[k]];"cellStart"in w&&w.cellStart instanceof Function&&w.cellStart({row:p,col:k,data:ht},this),l.call(this,new a(c,d,st[P[k]],Z,ht,p+2,ut[P[k]]))}}return this.internal.__cell__.table_x=c,this.internal.__cell__.table_y=d,this};var h=function(c,d){var m=this.internal.__cell__.padding,v=this.internal.__cell__.table_font_size,w=this.internal.scaleFactor;return Object.keys(c).map(function(p){var C=c[p];return this.splitTextToSize(C.hasOwnProperty("text")?C.text:C,d[p]-m-m)},this).map(function(p){return this.getLineHeightFactor()*p.length*v/w+m+m},this).reduce(function(p,C){return Math.max(p,C)},0)};r.setTableHeaderRow=function(c){i.call(this),this.internal.__cell__.tableHeaderRow=c},r.printHeaderRow=function(c,d){if(i.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var m;if(e=!0,typeof this.internal.__cell__.headerFunction=="function"){var v=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new a(v[0],v[1],v[2],v[3],void 0,-1)}this.setFont(void 0,"bold");for(var w=[],p=0;p<this.internal.__cell__.tableHeaderRow.length;p+=1){m=this.internal.__cell__.tableHeaderRow[p].clone(),d&&(m.y=this.internal.__cell__.margins.top||0,w.push(m)),m.lineNumber=c;var C=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),l.call(this,m),this.setTextColor(C)}w.length>0&&this.setTableHeaderRow(w),this.setFont(void 0,"normal"),e=!1}}(Ut.API);var cu={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},fu=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],qs=hu(fu),du=[100,200,300,400,500,600,700,800,900],oc=hu(du);function zs(r){var t=r.family.replace(/"|'/g,"").toLowerCase(),e=function(a){return cu[a=a||"normal"]?a:"normal"}(r.style),i=function(a){if(!a)return 400;if(typeof a=="number")return a>=100&&a<=900&&a%100==0?a:400;if(/^\d00$/.test(a))return parseInt(a);switch(a){case"bold":return 700;case"normal":default:return 400}}(r.weight),o=function(a){return typeof qs[a=a||"normal"]=="number"?a:"normal"}(r.stretch);return{family:t,style:e,weight:i,stretch:o,src:r.src||[],ref:r.ref||{name:t,style:[o,e,i].join(" ")}}}function Cl(r,t,e,i){var o;for(o=e;o>=0&&o<t.length;o+=i)if(r[t[o]])return r[t[o]];for(o=e;o>=0&&o<t.length;o-=i)if(r[t[o]])return r[t[o]]}var sc={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},Fl={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function Il(r){return[r.stretch,r.style,r.weight,r.family].join(" ")}function lc(r,t,e){for(var i=(e=e||{}).defaultFontFamily||"times",o=Object.assign({},sc,e.genericFontFamilies||{}),a=null,l=null,h=0;h<t.length;++h)if(o[(a=zs(t[h])).family]&&(a.family=o[a.family]),r.hasOwnProperty(a.family)){l=r[a.family];break}if(!(l=l||r[i]))throw new Error("Could not find a font-family for the rule '"+Il(a)+"' and default family '"+i+"'.");if(l=function(c,d){if(d[c])return d[c];var m=qs[c],v=m<=qs.normal?-1:1,w=Cl(d,fu,m,v);if(!w)throw new Error("Could not find a matching font-stretch value for "+c);return w}(a.stretch,l),l=function(c,d){if(d[c])return d[c];for(var m=cu[c],v=0;v<m.length;++v)if(d[m[v]])return d[m[v]];throw new Error("Could not find a matching font-style for "+c)}(a.style,l),!(l=function(c,d){if(d[c])return d[c];if(c===400&&d[500])return d[500];if(c===500&&d[400])return d[400];var m=oc[c],v=Cl(d,du,m,c<400?-1:1);if(!v)throw new Error("Could not find a matching font-weight for value "+c);return v}(a.weight,l)))throw new Error("Failed to resolve a font for the rule '"+Il(a)+"'.");return l}function Dl(r){return r.trimLeft()}function uc(r,t){for(var e=0;e<r.length;){if(r.charAt(e)===t)return[r.substring(0,e),r.substring(e+1)];e+=1}return null}function hc(r){var t=r.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return t===null?null:[t[0],r.substring(t[0].length)]}var fo,Bl,Ol,Ls=["times"];(function(r){var t,e,i,o,a,l,h,c,d,m=function(N){return N=N||{},this.isStrokeTransparent=N.isStrokeTransparent||!1,this.strokeOpacity=N.strokeOpacity||1,this.strokeStyle=N.strokeStyle||"#000000",this.fillStyle=N.fillStyle||"#000000",this.isFillTransparent=N.isFillTransparent||!1,this.fillOpacity=N.fillOpacity||1,this.font=N.font||"10px sans-serif",this.textBaseline=N.textBaseline||"alphabetic",this.textAlign=N.textAlign||"left",this.lineWidth=N.lineWidth||1,this.lineJoin=N.lineJoin||"miter",this.lineCap=N.lineCap||"butt",this.path=N.path||[],this.transform=N.transform!==void 0?N.transform.clone():new c,this.globalCompositeOperation=N.globalCompositeOperation||"normal",this.globalAlpha=N.globalAlpha||1,this.clip_path=N.clip_path||[],this.currentPoint=N.currentPoint||new l,this.miterLimit=N.miterLimit||10,this.lastPoint=N.lastPoint||new l,this.lineDashOffset=N.lineDashOffset||0,this.lineDash=N.lineDash||[],this.margin=N.margin||[0,0,0,0],this.prevPageLastElemOffset=N.prevPageLastElemOffset||0,this.ignoreClearRect=typeof N.ignoreClearRect!="boolean"||N.ignoreClearRect,this};r.events.push(["initialized",function(){this.context2d=new v(this),t=this.internal.f2,e=this.internal.getCoordinateString,i=this.internal.getVerticalCoordinateString,o=this.internal.getHorizontalCoordinate,a=this.internal.getVerticalCoordinate,l=this.internal.Point,h=this.internal.Rectangle,c=this.internal.Matrix,d=new m}]);var v=function(N){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var I=N;Object.defineProperty(this,"pdf",{get:function(){return I}});var M=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return M},set:function(ft){M=!!ft}});var U=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return U},set:function(ft){U=!!ft}});var Y=0;Object.defineProperty(this,"posX",{get:function(){return Y},set:function(ft){isNaN(ft)||(Y=ft)}});var et=0;Object.defineProperty(this,"posY",{get:function(){return et},set:function(ft){isNaN(ft)||(et=ft)}}),Object.defineProperty(this,"margin",{get:function(){return d.margin},set:function(ft){var T;typeof ft=="number"?T=[ft,ft,ft,ft]:((T=new Array(4))[0]=ft[0],T[1]=ft.length>=2?ft[1]:T[0],T[2]=ft.length>=3?ft[2]:T[0],T[3]=ft.length>=4?ft[3]:T[1]),d.margin=T}});var it=!1;Object.defineProperty(this,"autoPaging",{get:function(){return it},set:function(ft){it=ft}});var at=0;Object.defineProperty(this,"lastBreak",{get:function(){return at},set:function(ft){at=ft}});var Nt=[];Object.defineProperty(this,"pageBreaks",{get:function(){return Nt},set:function(ft){Nt=ft}}),Object.defineProperty(this,"ctx",{get:function(){return d},set:function(ft){ft instanceof m&&(d=ft)}}),Object.defineProperty(this,"path",{get:function(){return d.path},set:function(ft){d.path=ft}});var At=[];Object.defineProperty(this,"ctxStack",{get:function(){return At},set:function(ft){At=ft}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(ft){var T;T=w(ft),this.ctx.fillStyle=T.style,this.ctx.isFillTransparent=T.a===0,this.ctx.fillOpacity=T.a,this.pdf.setFillColor(T.r,T.g,T.b,{a:T.a}),this.pdf.setTextColor(T.r,T.g,T.b,{a:T.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(ft){var T=w(ft);this.ctx.strokeStyle=T.style,this.ctx.isStrokeTransparent=T.a===0,this.ctx.strokeOpacity=T.a,T.a===0?this.pdf.setDrawColor(255,255,255):(T.a,this.pdf.setDrawColor(T.r,T.g,T.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(ft){["butt","round","square"].indexOf(ft)!==-1&&(this.ctx.lineCap=ft,this.pdf.setLineCap(ft))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(ft){isNaN(ft)||(this.ctx.lineWidth=ft,this.pdf.setLineWidth(ft))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(ft){["bevel","round","miter"].indexOf(ft)!==-1&&(this.ctx.lineJoin=ft,this.pdf.setLineJoin(ft))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(ft){isNaN(ft)||(this.ctx.miterLimit=ft,this.pdf.setMiterLimit(ft))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(ft){this.ctx.textBaseline=ft}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(ft){["right","end","center","left","start"].indexOf(ft)!==-1&&(this.ctx.textAlign=ft)}});var jt=null;function Pt(ft,T){if(jt===null){var Qt=function(Mt){var xt=[];return Object.keys(Mt).forEach(function(Lt){Mt[Lt].forEach(function(Ct){var kt=null;switch(Ct){case"bold":kt={family:Lt,weight:"bold"};break;case"italic":kt={family:Lt,style:"italic"};break;case"bolditalic":kt={family:Lt,weight:"bold",style:"italic"};break;case"":case"normal":kt={family:Lt}}kt!==null&&(kt.ref={name:Lt,style:Ct},xt.push(kt))})}),xt}(ft.getFontList());jt=function(Mt){for(var xt={},Lt=0;Lt<Mt.length;++Lt){var Ct=zs(Mt[Lt]),kt=Ct.family,Et=Ct.stretch,Jt=Ct.style,te=Ct.weight;xt[kt]=xt[kt]||{},xt[kt][Et]=xt[kt][Et]||{},xt[kt][Et][Jt]=xt[kt][Et][Jt]||{},xt[kt][Et][Jt][te]=Ct}return xt}(Qt.concat(T))}return jt}var Ht=null;Object.defineProperty(this,"fontFaces",{get:function(){return Ht},set:function(ft){jt=null,Ht=ft}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(ft){var T;if(this.ctx.font=ft,(T=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(ft))!==null){var Qt=T[1];T[2];var Mt=T[3],xt=T[4];T[5];var Lt=T[6],Ct=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(xt)[2];xt=Math.floor(Ct==="px"?parseFloat(xt)*this.pdf.internal.scaleFactor:Ct==="em"?parseFloat(xt)*this.pdf.getFontSize():parseFloat(xt)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(xt);var kt=function(Vt){var re,Ft,Ye=[],se=Vt.trim();if(se==="")return Ls;if(se in Fl)return[Fl[se]];for(;se!=="";){switch(Ft=null,re=(se=Dl(se)).charAt(0)){case'"':case"'":Ft=uc(se.substring(1),re);break;default:Ft=hc(se)}if(Ft===null||(Ye.push(Ft[0]),(se=Dl(Ft[1]))!==""&&se.charAt(0)!==","))return Ls;se=se.replace(/^,/,"")}return Ye}(Lt);if(this.fontFaces){var Et=lc(Pt(this.pdf,this.fontFaces),kt.map(function(Vt){return{family:Vt,stretch:"normal",weight:Mt,style:Qt}}));this.pdf.setFont(Et.ref.name,Et.ref.style)}else{var Jt="";(Mt==="bold"||parseInt(Mt,10)>=700||Qt==="bold")&&(Jt="bold"),Qt==="italic"&&(Jt+="italic"),Jt.length===0&&(Jt="normal");for(var te="",ee={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},ae=0;ae<kt.length;ae++){if(this.pdf.internal.getFont(kt[ae],Jt,{noFallback:!0,disableWarning:!0})!==void 0){te=kt[ae];break}if(Jt==="bolditalic"&&this.pdf.internal.getFont(kt[ae],"bold",{noFallback:!0,disableWarning:!0})!==void 0)te=kt[ae],Jt="bold";else if(this.pdf.internal.getFont(kt[ae],"normal",{noFallback:!0,disableWarning:!0})!==void 0){te=kt[ae],Jt="normal";break}}if(te===""){for(var pe=0;pe<kt.length;pe++)if(ee[kt[pe]]){te=ee[kt[pe]];break}}te=te===""?"Times":te,this.pdf.setFont(te,Jt)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(ft){this.ctx.globalCompositeOperation=ft}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(ft){this.ctx.globalAlpha=ft}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(ft){this.ctx.lineDashOffset=ft,It.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(ft){this.ctx.lineDash=ft,It.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(ft){this.ctx.ignoreClearRect=!!ft}})};v.prototype.setLineDash=function(N){this.lineDash=N},v.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},v.prototype.fill=function(){K.call(this,"fill",!1)},v.prototype.stroke=function(){K.call(this,"stroke",!1)},v.prototype.beginPath=function(){this.path=[{type:"begin"}]},v.prototype.moveTo=function(N,I){if(isNaN(N)||isNaN(I))throw be.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var M=this.ctx.transform.applyToPoint(new l(N,I));this.path.push({type:"mt",x:M.x,y:M.y}),this.ctx.lastPoint=new l(N,I)},v.prototype.closePath=function(){var N=new l(0,0),I=0;for(I=this.path.length-1;I!==-1;I--)if(this.path[I].type==="begin"&&de(this.path[I+1])==="object"&&typeof this.path[I+1].x=="number"){N=new l(this.path[I+1].x,this.path[I+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new l(N.x,N.y)},v.prototype.lineTo=function(N,I){if(isNaN(N)||isNaN(I))throw be.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var M=this.ctx.transform.applyToPoint(new l(N,I));this.path.push({type:"lt",x:M.x,y:M.y}),this.ctx.lastPoint=new l(M.x,M.y)},v.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),K.call(this,null,!0)},v.prototype.quadraticCurveTo=function(N,I,M,U){if(isNaN(M)||isNaN(U)||isNaN(N)||isNaN(I))throw be.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var Y=this.ctx.transform.applyToPoint(new l(M,U)),et=this.ctx.transform.applyToPoint(new l(N,I));this.path.push({type:"qct",x1:et.x,y1:et.y,x:Y.x,y:Y.y}),this.ctx.lastPoint=new l(Y.x,Y.y)},v.prototype.bezierCurveTo=function(N,I,M,U,Y,et){if(isNaN(Y)||isNaN(et)||isNaN(N)||isNaN(I)||isNaN(M)||isNaN(U))throw be.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var it=this.ctx.transform.applyToPoint(new l(Y,et)),at=this.ctx.transform.applyToPoint(new l(N,I)),Nt=this.ctx.transform.applyToPoint(new l(M,U));this.path.push({type:"bct",x1:at.x,y1:at.y,x2:Nt.x,y2:Nt.y,x:it.x,y:it.y}),this.ctx.lastPoint=new l(it.x,it.y)},v.prototype.arc=function(N,I,M,U,Y,et){if(isNaN(N)||isNaN(I)||isNaN(M)||isNaN(U)||isNaN(Y))throw be.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(et=!!et,!this.ctx.transform.isIdentity){var it=this.ctx.transform.applyToPoint(new l(N,I));N=it.x,I=it.y;var at=this.ctx.transform.applyToPoint(new l(0,M)),Nt=this.ctx.transform.applyToPoint(new l(0,0));M=Math.sqrt(Math.pow(at.x-Nt.x,2)+Math.pow(at.y-Nt.y,2))}Math.abs(Y-U)>=2*Math.PI&&(U=0,Y=2*Math.PI),this.path.push({type:"arc",x:N,y:I,radius:M,startAngle:U,endAngle:Y,counterclockwise:et})},v.prototype.arcTo=function(N,I,M,U,Y){throw new Error("arcTo not implemented.")},v.prototype.rect=function(N,I,M,U){if(isNaN(N)||isNaN(I)||isNaN(M)||isNaN(U))throw be.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(N,I),this.lineTo(N+M,I),this.lineTo(N+M,I+U),this.lineTo(N,I+U),this.lineTo(N,I),this.lineTo(N+M,I),this.lineTo(N,I)},v.prototype.fillRect=function(N,I,M,U){if(isNaN(N)||isNaN(I)||isNaN(M)||isNaN(U))throw be.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!p.call(this)){var Y={};this.lineCap!=="butt"&&(Y.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&(Y.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(N,I,M,U),this.fill(),Y.hasOwnProperty("lineCap")&&(this.lineCap=Y.lineCap),Y.hasOwnProperty("lineJoin")&&(this.lineJoin=Y.lineJoin)}},v.prototype.strokeRect=function(N,I,M,U){if(isNaN(N)||isNaN(I)||isNaN(M)||isNaN(U))throw be.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");C.call(this)||(this.beginPath(),this.rect(N,I,M,U),this.stroke())},v.prototype.clearRect=function(N,I,M,U){if(isNaN(N)||isNaN(I)||isNaN(M)||isNaN(U))throw be.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(N,I,M,U))},v.prototype.save=function(N){N=typeof N!="boolean"||N;for(var I=this.pdf.internal.getCurrentPageInfo().pageNumber,M=0;M<this.pdf.internal.getNumberOfPages();M++)this.pdf.setPage(M+1),this.pdf.internal.out("q");if(this.pdf.setPage(I),N){this.ctx.fontSize=this.pdf.internal.getFontSize();var U=new m(this.ctx);this.ctxStack.push(this.ctx),this.ctx=U}},v.prototype.restore=function(N){N=typeof N!="boolean"||N;for(var I=this.pdf.internal.getCurrentPageInfo().pageNumber,M=0;M<this.pdf.internal.getNumberOfPages();M++)this.pdf.setPage(M+1),this.pdf.internal.out("Q");this.pdf.setPage(I),N&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},v.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var w=function(N){var I,M,U,Y;if(N.isCanvasGradient===!0&&(N=N.getColor()),!N)return{r:0,g:0,b:0,a:0,style:N};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(N))I=0,M=0,U=0,Y=0;else{var et=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(N);if(et!==null)I=parseInt(et[1]),M=parseInt(et[2]),U=parseInt(et[3]),Y=1;else if((et=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(N))!==null)I=parseInt(et[1]),M=parseInt(et[2]),U=parseInt(et[3]),Y=parseFloat(et[4]);else{if(Y=1,typeof N=="string"&&N.charAt(0)!=="#"){var it=new iu(N);N=it.ok?it.toHex():"#000000"}N.length===4?(I=N.substring(1,2),I+=I,M=N.substring(2,3),M+=M,U=N.substring(3,4),U+=U):(I=N.substring(1,3),M=N.substring(3,5),U=N.substring(5,7)),I=parseInt(I,16),M=parseInt(M,16),U=parseInt(U,16)}}return{r:I,g:M,b:U,a:Y,style:N}},p=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},C=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};v.prototype.fillText=function(N,I,M,U){if(isNaN(I)||isNaN(M)||typeof N!="string")throw be.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(U=isNaN(U)?void 0:U,!p.call(this)){var Y=Z(this.ctx.transform.rotation),et=this.ctx.transform.scaleX;j.call(this,{text:N,x:I,y:M,scale:et,angle:Y,align:this.textAlign,maxWidth:U})}},v.prototype.strokeText=function(N,I,M,U){if(isNaN(I)||isNaN(M)||typeof N!="string")throw be.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!C.call(this)){U=isNaN(U)?void 0:U;var Y=Z(this.ctx.transform.rotation),et=this.ctx.transform.scaleX;j.call(this,{text:N,x:I,y:M,scale:et,renderingMode:"stroke",angle:Y,align:this.textAlign,maxWidth:U})}},v.prototype.measureText=function(N){if(typeof N!="string")throw be.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var I=this.pdf,M=this.pdf.internal.scaleFactor,U=I.internal.getFontSize(),Y=I.getStringUnitWidth(N)*U/I.internal.scaleFactor,et=function(it){var at=(it=it||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return at}}),this};return new et({width:Y*=Math.round(96*M/72*1e4)/1e4})},v.prototype.scale=function(N,I){if(isNaN(N)||isNaN(I))throw be.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var M=new c(N,0,0,I,0,0);this.ctx.transform=this.ctx.transform.multiply(M)},v.prototype.rotate=function(N){if(isNaN(N))throw be.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var I=new c(Math.cos(N),Math.sin(N),-Math.sin(N),Math.cos(N),0,0);this.ctx.transform=this.ctx.transform.multiply(I)},v.prototype.translate=function(N,I){if(isNaN(N)||isNaN(I))throw be.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var M=new c(1,0,0,1,N,I);this.ctx.transform=this.ctx.transform.multiply(M)},v.prototype.transform=function(N,I,M,U,Y,et){if(isNaN(N)||isNaN(I)||isNaN(M)||isNaN(U)||isNaN(Y)||isNaN(et))throw be.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var it=new c(N,I,M,U,Y,et);this.ctx.transform=this.ctx.transform.multiply(it)},v.prototype.setTransform=function(N,I,M,U,Y,et){N=isNaN(N)?1:N,I=isNaN(I)?0:I,M=isNaN(M)?0:M,U=isNaN(U)?1:U,Y=isNaN(Y)?0:Y,et=isNaN(et)?0:et,this.ctx.transform=new c(N,I,M,U,Y,et)};var k=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};v.prototype.drawImage=function(N,I,M,U,Y,et,it,at,Nt){var At=this.pdf.getImageProperties(N),jt=1,Pt=1,Ht=1,ft=1;U!==void 0&&at!==void 0&&(Ht=at/U,ft=Nt/Y,jt=At.width/U*at/U,Pt=At.height/Y*Nt/Y),et===void 0&&(et=I,it=M,I=0,M=0),U!==void 0&&at===void 0&&(at=U,Nt=Y),U===void 0&&at===void 0&&(at=At.width,Nt=At.height);for(var T,Qt=this.ctx.transform.decompose(),Mt=Z(Qt.rotate.shx),xt=new c,Lt=(xt=(xt=(xt=xt.multiply(Qt.translate)).multiply(Qt.skew)).multiply(Qt.scale)).applyToRectangle(new h(et-I*Ht,it-M*ft,U*jt,Y*Pt)),Ct=B.call(this,Lt),kt=[],Et=0;Et<Ct.length;Et+=1)kt.indexOf(Ct[Et])===-1&&kt.push(Ct[Et]);if(E(kt),this.autoPaging)for(var Jt=kt[0],te=kt[kt.length-1],ee=Jt;ee<te+1;ee++){this.pdf.setPage(ee);var ae=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],pe=ee===1?this.posY+this.margin[0]:this.margin[0],Vt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],re=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],Ft=ee===1?0:Vt+(ee-2)*re;if(this.ctx.clip_path.length!==0){var Ye=this.path;T=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=O(T,this.posX+this.margin[3],-Ft+pe+this.ctx.prevPageLastElemOffset),st.call(this,"fill",!0),this.path=Ye}var se=JSON.parse(JSON.stringify(Lt));se=O([se],this.posX+this.margin[3],-Ft+pe+this.ctx.prevPageLastElemOffset)[0];var Pr=(ee>Jt||ee<te)&&k.call(this);Pr&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ae,re,null).clip().discardPath()),this.pdf.addImage(N,"JPEG",se.x,se.y,se.w,se.h,null,null,Mt),Pr&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(N,"JPEG",Lt.x,Lt.y,Lt.w,Lt.h,null,null,Mt)};var B=function(N,I,M){var U=[];I=I||this.pdf.internal.pageSize.width,M=M||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var Y=this.posY+this.ctx.prevPageLastElemOffset;switch(N.type){default:case"mt":case"lt":U.push(Math.floor((N.y+Y)/M)+1);break;case"arc":U.push(Math.floor((N.y+Y-N.radius)/M)+1),U.push(Math.floor((N.y+Y+N.radius)/M)+1);break;case"qct":var et=ht(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x,N.y);U.push(Math.floor((et.y+Y)/M)+1),U.push(Math.floor((et.y+et.h+Y)/M)+1);break;case"bct":var it=dt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x2,N.y2,N.x,N.y);U.push(Math.floor((it.y+Y)/M)+1),U.push(Math.floor((it.y+it.h+Y)/M)+1);break;case"rect":U.push(Math.floor((N.y+Y)/M)+1),U.push(Math.floor((N.y+N.h+Y)/M)+1)}for(var at=0;at<U.length;at+=1)for(;this.pdf.internal.getNumberOfPages()<U[at];)P.call(this);return U},P=function(){var N=this.fillStyle,I=this.strokeStyle,M=this.font,U=this.lineCap,Y=this.lineWidth,et=this.lineJoin;this.pdf.addPage(),this.fillStyle=N,this.strokeStyle=I,this.font=M,this.lineCap=U,this.lineWidth=Y,this.lineJoin=et},O=function(N,I,M){for(var U=0;U<N.length;U++)switch(N[U].type){case"bct":N[U].x2+=I,N[U].y2+=M;case"qct":N[U].x1+=I,N[U].y1+=M;case"mt":case"lt":case"arc":default:N[U].x+=I,N[U].y+=M}return N},E=function(N){return N.sort(function(I,M){return I-M})},K=function(N,I){for(var M,U,Y=this.fillStyle,et=this.strokeStyle,it=this.lineCap,at=this.lineWidth,Nt=Math.abs(at*this.ctx.transform.scaleX),At=this.lineJoin,jt=JSON.parse(JSON.stringify(this.path)),Pt=JSON.parse(JSON.stringify(this.path)),Ht=[],ft=0;ft<Pt.length;ft++)if(Pt[ft].x!==void 0)for(var T=B.call(this,Pt[ft]),Qt=0;Qt<T.length;Qt+=1)Ht.indexOf(T[Qt])===-1&&Ht.push(T[Qt]);for(var Mt=0;Mt<Ht.length;Mt++)for(;this.pdf.internal.getNumberOfPages()<Ht[Mt];)P.call(this);if(E(Ht),this.autoPaging)for(var xt=Ht[0],Lt=Ht[Ht.length-1],Ct=xt;Ct<Lt+1;Ct++){this.pdf.setPage(Ct),this.fillStyle=Y,this.strokeStyle=et,this.lineCap=it,this.lineWidth=Nt,this.lineJoin=At;var kt=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],Et=Ct===1?this.posY+this.margin[0]:this.margin[0],Jt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],te=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],ee=Ct===1?0:Jt+(Ct-2)*te;if(this.ctx.clip_path.length!==0){var ae=this.path;M=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=O(M,this.posX+this.margin[3],-ee+Et+this.ctx.prevPageLastElemOffset),st.call(this,N,!0),this.path=ae}if(U=JSON.parse(JSON.stringify(jt)),this.path=O(U,this.posX+this.margin[3],-ee+Et+this.ctx.prevPageLastElemOffset),I===!1||Ct===0){var pe=(Ct>xt||Ct<Lt)&&k.call(this);pe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],kt,te,null).clip().discardPath()),st.call(this,N,I),pe&&this.pdf.restoreGraphicsState()}this.lineWidth=at}else this.lineWidth=Nt,st.call(this,N,I),this.lineWidth=at;this.path=jt},st=function(N,I){if((N!=="stroke"||I||!C.call(this))&&(N==="stroke"||I||!p.call(this))){for(var M,U,Y=[],et=this.path,it=0;it<et.length;it++){var at=et[it];switch(at.type){case"begin":Y.push({begin:!0});break;case"close":Y.push({close:!0});break;case"mt":Y.push({start:at,deltas:[],abs:[]});break;case"lt":var Nt=Y.length;if(et[it-1]&&!isNaN(et[it-1].x)&&(M=[at.x-et[it-1].x,at.y-et[it-1].y],Nt>0)){for(;Nt>=0;Nt--)if(Y[Nt-1].close!==!0&&Y[Nt-1].begin!==!0){Y[Nt-1].deltas.push(M),Y[Nt-1].abs.push(at);break}}break;case"bct":M=[at.x1-et[it-1].x,at.y1-et[it-1].y,at.x2-et[it-1].x,at.y2-et[it-1].y,at.x-et[it-1].x,at.y-et[it-1].y],Y[Y.length-1].deltas.push(M);break;case"qct":var At=et[it-1].x+2/3*(at.x1-et[it-1].x),jt=et[it-1].y+2/3*(at.y1-et[it-1].y),Pt=at.x+2/3*(at.x1-at.x),Ht=at.y+2/3*(at.y1-at.y),ft=at.x,T=at.y;M=[At-et[it-1].x,jt-et[it-1].y,Pt-et[it-1].x,Ht-et[it-1].y,ft-et[it-1].x,T-et[it-1].y],Y[Y.length-1].deltas.push(M);break;case"arc":Y.push({deltas:[],abs:[],arc:!0}),Array.isArray(Y[Y.length-1].abs)&&Y[Y.length-1].abs.push(at)}}U=I?null:N==="stroke"?"stroke":"fill";for(var Qt=!1,Mt=0;Mt<Y.length;Mt++)if(Y[Mt].arc)for(var xt=Y[Mt].abs,Lt=0;Lt<xt.length;Lt++){var Ct=xt[Lt];Ct.type==="arc"?q.call(this,Ct.x,Ct.y,Ct.radius,Ct.startAngle,Ct.endAngle,Ct.counterclockwise,void 0,I,!Qt):V.call(this,Ct.x,Ct.y),Qt=!0}else if(Y[Mt].close===!0)this.pdf.internal.out("h"),Qt=!1;else if(Y[Mt].begin!==!0){var kt=Y[Mt].start.x,Et=Y[Mt].start.y;R.call(this,Y[Mt].deltas,kt,Et),Qt=!0}U&&rt.call(this,U),I&&pt.call(this)}},yt=function(N){var I=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,M=I*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return N-M;case"top":return N+I-M;case"hanging":return N+I-2*M;case"middle":return N+I/2-M;case"ideographic":return N;case"alphabetic":default:return N}},Q=function(N){return N+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};v.prototype.createLinearGradient=function(){var N=function(){};return N.colorStops=[],N.addColorStop=function(I,M){this.colorStops.push([I,M])},N.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},N.isCanvasGradient=!0,N},v.prototype.createPattern=function(){return this.createLinearGradient()},v.prototype.createRadialGradient=function(){return this.createLinearGradient()};var q=function(N,I,M,U,Y,et,it,at,Nt){for(var At=nt.call(this,M,U,Y,et),jt=0;jt<At.length;jt++){var Pt=At[jt];jt===0&&(Nt?_.call(this,Pt.x1+N,Pt.y1+I):V.call(this,Pt.x1+N,Pt.y1+I)),ot.call(this,N,I,Pt.x2,Pt.y2,Pt.x3,Pt.y3,Pt.x4,Pt.y4)}at?pt.call(this):rt.call(this,it)},rt=function(N){switch(N){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},pt=function(){this.pdf.clip(),this.pdf.discardPath()},_=function(N,I){this.pdf.internal.out(e(N)+" "+i(I)+" m")},j=function(N){var I;switch(N.align){case"right":case"end":I="right";break;case"center":I="center";break;case"left":case"start":default:I="left"}var M=this.pdf.getTextDimensions(N.text),U=yt.call(this,N.y),Y=Q.call(this,U)-M.h,et=this.ctx.transform.applyToPoint(new l(N.x,U)),it=this.ctx.transform.decompose(),at=new c;at=(at=(at=at.multiply(it.translate)).multiply(it.skew)).multiply(it.scale);for(var Nt,At,jt,Pt=this.ctx.transform.applyToRectangle(new h(N.x,U,M.w,M.h)),Ht=at.applyToRectangle(new h(N.x,Y,M.w,M.h)),ft=B.call(this,Ht),T=[],Qt=0;Qt<ft.length;Qt+=1)T.indexOf(ft[Qt])===-1&&T.push(ft[Qt]);if(E(T),this.autoPaging)for(var Mt=T[0],xt=T[T.length-1],Lt=Mt;Lt<xt+1;Lt++){this.pdf.setPage(Lt);var Ct=Lt===1?this.posY+this.margin[0]:this.margin[0],kt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Et=this.pdf.internal.pageSize.height-this.margin[2],Jt=Et-this.margin[0],te=this.pdf.internal.pageSize.width-this.margin[1],ee=te-this.margin[3],ae=Lt===1?0:kt+(Lt-2)*Jt;if(this.ctx.clip_path.length!==0){var pe=this.path;Nt=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=O(Nt,this.posX+this.margin[3],-1*ae+Ct),st.call(this,"fill",!0),this.path=pe}var Vt=O([JSON.parse(JSON.stringify(Ht))],this.posX+this.margin[3],-ae+Ct+this.ctx.prevPageLastElemOffset)[0];N.scale>=.01&&(At=this.pdf.internal.getFontSize(),this.pdf.setFontSize(At*N.scale),jt=this.lineWidth,this.lineWidth=jt*N.scale);var re=this.autoPaging!=="text";if(re||Vt.y+Vt.h<=Et){if(re||Vt.y>=Ct&&Vt.x<=te){var Ft=re?N.text:this.pdf.splitTextToSize(N.text,N.maxWidth||te-Vt.x)[0],Ye=O([JSON.parse(JSON.stringify(Pt))],this.posX+this.margin[3],-ae+Ct+this.ctx.prevPageLastElemOffset)[0],se=re&&(Lt>Mt||Lt<xt)&&k.call(this);se&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ee,Jt,null).clip().discardPath()),this.pdf.text(Ft,Ye.x,Ye.y,{angle:N.angle,align:I,renderingMode:N.renderingMode}),se&&this.pdf.restoreGraphicsState()}}else Vt.y<Et&&(this.ctx.prevPageLastElemOffset+=Et-Vt.y);N.scale>=.01&&(this.pdf.setFontSize(At),this.lineWidth=jt)}else N.scale>=.01&&(At=this.pdf.internal.getFontSize(),this.pdf.setFontSize(At*N.scale),jt=this.lineWidth,this.lineWidth=jt*N.scale),this.pdf.text(N.text,et.x+this.posX,et.y+this.posY,{angle:N.angle,align:I,renderingMode:N.renderingMode,maxWidth:N.maxWidth}),N.scale>=.01&&(this.pdf.setFontSize(At),this.lineWidth=jt)},V=function(N,I,M,U){M=M||0,U=U||0,this.pdf.internal.out(e(N+M)+" "+i(I+U)+" l")},R=function(N,I,M){return this.pdf.lines(N,I,M,null,null)},ot=function(N,I,M,U,Y,et,it,at){this.pdf.internal.out([t(o(M+N)),t(a(U+I)),t(o(Y+N)),t(a(et+I)),t(o(it+N)),t(a(at+I)),"c"].join(" "))},nt=function(N,I,M,U){for(var Y=2*Math.PI,et=Math.PI/2;I>M;)I-=Y;var it=Math.abs(M-I);it<Y&&U&&(it=Y-it);for(var at=[],Nt=U?-1:1,At=I;it>1e-5;){var jt=At+Nt*Math.min(it,et);at.push(ut.call(this,N,At,jt)),it-=Math.abs(jt-At),At=jt}return at},ut=function(N,I,M){var U=(M-I)/2,Y=N*Math.cos(U),et=N*Math.sin(U),it=Y,at=-et,Nt=it*it+at*at,At=Nt+it*Y+at*et,jt=4/3*(Math.sqrt(2*Nt*At)-At)/(it*et-at*Y),Pt=it-jt*at,Ht=at+jt*it,ft=Pt,T=-Ht,Qt=U+I,Mt=Math.cos(Qt),xt=Math.sin(Qt);return{x1:N*Math.cos(I),y1:N*Math.sin(I),x2:Pt*Mt-Ht*xt,y2:Pt*xt+Ht*Mt,x3:ft*Mt-T*xt,y3:ft*xt+T*Mt,x4:N*Math.cos(M),y4:N*Math.sin(M)}},Z=function(N){return 180*N/Math.PI},ht=function(N,I,M,U,Y,et){var it=N+.5*(M-N),at=I+.5*(U-I),Nt=Y+.5*(M-Y),At=et+.5*(U-et),jt=Math.min(N,Y,it,Nt),Pt=Math.max(N,Y,it,Nt),Ht=Math.min(I,et,at,At),ft=Math.max(I,et,at,At);return new h(jt,Ht,Pt-jt,ft-Ht)},dt=function(N,I,M,U,Y,et,it,at){var Nt,At,jt,Pt,Ht,ft,T,Qt,Mt,xt,Lt,Ct,kt,Et,Jt=M-N,te=U-I,ee=Y-M,ae=et-U,pe=it-Y,Vt=at-et;for(At=0;At<41;At++)Mt=(T=(jt=N+(Nt=At/40)*Jt)+Nt*((Ht=M+Nt*ee)-jt))+Nt*(Ht+Nt*(Y+Nt*pe-Ht)-T),xt=(Qt=(Pt=I+Nt*te)+Nt*((ft=U+Nt*ae)-Pt))+Nt*(ft+Nt*(et+Nt*Vt-ft)-Qt),At==0?(Lt=Mt,Ct=xt,kt=Mt,Et=xt):(Lt=Math.min(Lt,Mt),Ct=Math.min(Ct,xt),kt=Math.max(kt,Mt),Et=Math.max(Et,xt));return new h(Math.round(Lt),Math.round(Ct),Math.round(kt-Lt),Math.round(Et-Ct))},It=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var N,I,M=(N=this.ctx.lineDash,I=this.ctx.lineDashOffset,JSON.stringify({lineDash:N,lineDashOffset:I}));this.prevLineDash!==M&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=M)}}})(Ut.API),function(r){var t=function(a){var l,h,c,d,m,v,w,p,C,k;for(h=[],c=0,d=(a+=l="\0\0\0\0".slice(a.length%4||4)).length;d>c;c+=4)(m=(a.charCodeAt(c)<<24)+(a.charCodeAt(c+1)<<16)+(a.charCodeAt(c+2)<<8)+a.charCodeAt(c+3))!==0?(v=(m=((m=((m=((m=(m-(k=m%85))/85)-(C=m%85))/85)-(p=m%85))/85)-(w=m%85))/85)%85,h.push(v+33,w+33,p+33,C+33,k+33)):h.push(122);return function(B,P){for(var O=P;O>0;O--)B.pop()}(h,l.length),String.fromCharCode.apply(String,h)+"~>"},e=function(a){var l,h,c,d,m,v=String,w="length",p=255,C="charCodeAt",k="slice",B="replace";for(a[k](-2),a=a[k](0,-2)[B](/\s/g,"")[B]("z","!!!!!"),c=[],d=0,m=(a+=l="uuuuu"[k](a[w]%5||5))[w];m>d;d+=5)h=52200625*(a[C](d)-33)+614125*(a[C](d+1)-33)+7225*(a[C](d+2)-33)+85*(a[C](d+3)-33)+(a[C](d+4)-33),c.push(p&h>>24,p&h>>16,p&h>>8,p&h);return function(P,O){for(var E=O;E>0;E--)P.pop()}(c,l[w]),v.fromCharCode.apply(v,c)},i=function(a){var l=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((a=a.replace(/\s/g,"")).indexOf(">")!==-1&&(a=a.substr(0,a.indexOf(">"))),a.length%2&&(a+="0"),l.test(a)===!1)return"";for(var h="",c=0;c<a.length;c+=2)h+=String.fromCharCode("0x"+(a[c]+a[c+1]));return h},o=function(a){for(var l=new Uint8Array(a.length),h=a.length;h--;)l[h]=a.charCodeAt(h);return a=(l=Os(l)).reduce(function(c,d){return c+String.fromCharCode(d)},"")};r.processDataByFilters=function(a,l){var h=0,c=a||"",d=[];for(typeof(l=l||[])=="string"&&(l=[l]),h=0;h<l.length;h+=1)switch(l[h]){case"ASCII85Decode":case"/ASCII85Decode":c=e(c),d.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":c=t(c),d.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":c=i(c),d.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":c=c.split("").map(function(m){return("0"+m.charCodeAt().toString(16)).slice(-2)}).join("")+">",d.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":c=o(c),d.push("/FlateDecode");break;default:throw new Error('The filter: "'+l[h]+'" is not implemented')}return{data:c,reverseChain:d.reverse().join(" ")}}}(Ut.API),function(r){r.loadFile=function(t,e,i){return function(o,a,l){a=a!==!1,l=typeof l=="function"?l:function(){};var h=void 0;try{h=function(c,d,m){var v=new XMLHttpRequest,w=0,p=function(C){var k=C.length,B=[],P=String.fromCharCode;for(w=0;w<k;w+=1)B.push(P(255&C.charCodeAt(w)));return B.join("")};if(v.open("GET",c,!d),v.overrideMimeType("text/plain; charset=x-user-defined"),d===!1&&(v.onload=function(){v.status===200?m(p(this.responseText)):m(void 0)}),v.send(null),d&&v.status===200)return p(v.responseText)}(o,a,l)}catch{}return h}(t,e,i)},r.loadImageFile=r.loadFile}(Ut.API),function(r){function t(){return(Wt.html2canvas?Promise.resolve(Wt.html2canvas):Cs(()=>import("./html2canvas.esm-CBrSDip1.js"),[])).catch(function(l){return Promise.reject(new Error("Could not load html2canvas: "+l))}).then(function(l){return l.default?l.default:l})}function e(){return(Wt.DOMPurify?Promise.resolve(Wt.DOMPurify):Cs(()=>import("./purify.es-CQJ0hv7W.js"),[])).catch(function(l){return Promise.reject(new Error("Could not load dompurify: "+l))}).then(function(l){return l.default?l.default:l})}var i=function(l){var h=de(l);return h==="undefined"?"undefined":h==="string"||l instanceof String?"string":h==="number"||l instanceof Number?"number":h==="function"||l instanceof Function?"function":l&&l.constructor===Array?"array":l&&l.nodeType===1?"element":h==="object"?"object":"unknown"},o=function(l,h){var c=document.createElement(l);for(var d in h.className&&(c.className=h.className),h.innerHTML&&h.dompurify&&(c.innerHTML=h.dompurify.sanitize(h.innerHTML)),h.style)c.style[d]=h.style[d];return c},a=function l(h){var c=Object.assign(l.convert(Promise.resolve()),JSON.parse(JSON.stringify(l.template))),d=l.convert(Promise.resolve(),c);return d=(d=d.setProgress(1,l,1,[l])).set(h)};(a.prototype=Object.create(Promise.prototype)).constructor=a,a.convert=function(l,h){return l.__proto__=h||a.prototype,l},a.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},a.prototype.from=function(l,h){return this.then(function(){switch(h=h||function(c){switch(i(c)){case"string":return"string";case"element":return c.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}}(l)){case"string":return this.then(e).then(function(c){return this.set({src:o("div",{innerHTML:l,dompurify:c})})});case"element":return this.set({src:l});case"canvas":return this.set({canvas:l});case"img":return this.set({img:l});default:return this.error("Unknown source type.")}})},a.prototype.to=function(l){switch(l){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},a.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var l={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},h=function c(d,m){for(var v=d.nodeType===3?document.createTextNode(d.nodeValue):d.cloneNode(!1),w=d.firstChild;w;w=w.nextSibling)m!==!0&&w.nodeType===1&&w.nodeName==="SCRIPT"||v.appendChild(c(w,m));return d.nodeType===1&&(d.nodeName==="CANVAS"?(v.width=d.width,v.height=d.height,v.getContext("2d").drawImage(d,0,0)):d.nodeName!=="TEXTAREA"&&d.nodeName!=="SELECT"||(v.value=d.value),v.addEventListener("load",function(){v.scrollTop=d.scrollTop,v.scrollLeft=d.scrollLeft},!0)),v}(this.prop.src,this.opt.html2canvas.javascriptEnabled);h.tagName==="BODY"&&(l.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=o("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=o("div",{className:"html2pdf__container",style:l}),this.prop.container.appendChild(h),this.prop.container.firstChild.appendChild(o("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},a.prototype.toCanvas=function(){var l=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(l).then(t).then(function(h){var c=Object.assign({},this.opt.html2canvas);return delete c.onrendered,h(this.prop.container,c)}).then(function(h){(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},a.prototype.toContext2d=function(){var l=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(l).then(t).then(function(h){var c=this.opt.jsPDF,d=this.opt.fontFaces,m=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,v=Object.assign({async:!0,allowTaint:!0,scale:m,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete v.onrendered,c.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,c.context2d.posX=this.opt.x,c.context2d.posY=this.opt.y,c.context2d.margin=this.opt.margin,c.context2d.fontFaces=d,d)for(var w=0;w<d.length;++w){var p=d[w],C=p.src.find(function(k){return k.format==="truetype"});C&&c.addFont(C.url,p.ref.name,p.ref.style)}return v.windowHeight=v.windowHeight||0,v.windowHeight=v.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):v.windowHeight,c.context2d.save(!0),h(this.prop.container,v)}).then(function(h){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},a.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var l=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=l})},a.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},a.prototype.output=function(l,h,c){return(c=c||"pdf").toLowerCase()==="img"||c.toLowerCase()==="image"?this.outputImg(l,h):this.outputPdf(l,h)},a.prototype.outputPdf=function(l,h){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(l,h)})},a.prototype.outputImg=function(l){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(l){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+l+'" is not supported.'}})},a.prototype.save=function(l){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(l?{filename:l}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},a.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},a.prototype.set=function(l){if(i(l)!=="object")return this;var h=Object.keys(l||{}).map(function(c){if(c in a.template.prop)return function(){this.prop[c]=l[c]};switch(c){case"margin":return this.setMargin.bind(this,l.margin);case"jsPDF":return function(){return this.opt.jsPDF=l.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,l.pageSize);default:return function(){this.opt[c]=l[c]}}},this);return this.then(function(){return this.thenList(h)})},a.prototype.get=function(l,h){return this.then(function(){var c=l in a.template.prop?this.prop[l]:this.opt[l];return h?h(c):c})},a.prototype.setMargin=function(l){return this.then(function(){switch(i(l)){case"number":l=[l,l,l,l];case"array":if(l.length===2&&(l=[l[0],l[1],l[0],l[1]]),l.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=l}).then(this.setPageSize)},a.prototype.setPageSize=function(l){function h(c,d){return Math.floor(c*d/72*96)}return this.then(function(){(l=l||Ut.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(l.inner={width:l.width-this.opt.margin[1]-this.opt.margin[3],height:l.height-this.opt.margin[0]-this.opt.margin[2]},l.inner.px={width:h(l.inner.width,l.k),height:h(l.inner.height,l.k)},l.inner.ratio=l.inner.height/l.inner.width),this.prop.pageSize=l})},a.prototype.setProgress=function(l,h,c,d){return l!=null&&(this.progress.val=l),h!=null&&(this.progress.state=h),c!=null&&(this.progress.n=c),d!=null&&(this.progress.stack=d),this.progress.ratio=this.progress.val/this.progress.state,this},a.prototype.updateProgress=function(l,h,c,d){return this.setProgress(l?this.progress.val+l:null,h||null,c?this.progress.n+c:null,d?this.progress.stack.concat(d):null)},a.prototype.then=function(l,h){var c=this;return this.thenCore(l,h,function(d,m){return c.updateProgress(null,null,1,[d]),Promise.prototype.then.call(this,function(v){return c.updateProgress(null,d),v}).then(d,m).then(function(v){return c.updateProgress(1),v})})},a.prototype.thenCore=function(l,h,c){c=c||Promise.prototype.then,l&&(l=l.bind(this)),h&&(h=h.bind(this));var d=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?this:a.convert(Object.assign({},this),Promise.prototype),m=c.call(d,l,h);return a.convert(m,this.__proto__)},a.prototype.thenExternal=function(l,h){return Promise.prototype.then.call(this,l,h)},a.prototype.thenList=function(l){var h=this;return l.forEach(function(c){h=h.thenCore(c)}),h},a.prototype.catch=function(l){l&&(l=l.bind(this));var h=Promise.prototype.catch.call(this,l);return a.convert(h,this)},a.prototype.catchExternal=function(l){return Promise.prototype.catch.call(this,l)},a.prototype.error=function(l){return this.then(function(){throw new Error(l)})},a.prototype.using=a.prototype.set,a.prototype.saveAs=a.prototype.save,a.prototype.export=a.prototype.output,a.prototype.run=a.prototype.then,Ut.getPageSize=function(l,h,c){if(de(l)==="object"){var d=l;l=d.orientation,h=d.unit||h,c=d.format||c}h=h||"mm",c=c||"a4",l=(""+(l||"P")).toLowerCase();var m,v=(""+c).toLowerCase(),w={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(h){case"pt":m=1;break;case"mm":m=72/25.4;break;case"cm":m=72/2.54;break;case"in":m=72;break;case"px":m=.75;break;case"pc":case"em":m=12;break;case"ex":m=6;break;default:throw"Invalid unit: "+h}var p,C=0,k=0;if(w.hasOwnProperty(v))C=w[v][1]/m,k=w[v][0]/m;else try{C=c[1],k=c[0]}catch{throw new Error("Invalid format: "+c)}if(l==="p"||l==="portrait")l="p",k>C&&(p=k,k=C,C=p);else{if(l!=="l"&&l!=="landscape")throw"Invalid orientation: "+l;l="l",C>k&&(p=k,k=C,C=p)}return{width:k,height:C,unit:h,k:m,orientation:l}},r.html=function(l,h){(h=h||{}).callback=h.callback||function(){},h.html2canvas=h.html2canvas||{},h.html2canvas.canvas=h.html2canvas.canvas||this.canvas,h.jsPDF=h.jsPDF||this,h.fontFaces=h.fontFaces?h.fontFaces.map(zs):null;var c=new a(h);return h.worker?c:c.from(l).doCallback()}}(Ut.API),Ut.API.addJS=function(r){return Ol=r,this.internal.events.subscribe("postPutResources",function(){fo=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(fo+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),Bl=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+Ol+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){fo!==void 0&&Bl!==void 0&&this.internal.out("/Names <</JavaScript "+fo+" 0 R>>")}),this},function(r){var t;r.events.push(["postPutResources",function(){var e=this,i=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var o=e.outline.render().split(/\r\n/),a=0;a<o.length;a++){var l=o[a],h=i.exec(l);if(h!=null){var c=h[1];e.internal.newObjectDeferredBegin(c,!1)}e.internal.write(l)}if(this.outline.createNamedDestinations){var d=this.internal.pages.length,m=[];for(a=0;a<d;a++){var v=e.internal.newObject();m.push(v);var w=e.internal.getPageInfo(a+1);e.internal.write("<< /D["+w.objId+" 0 R /XYZ null null null]>> endobj")}var p=e.internal.newObject();for(e.internal.write("<< /Names [ "),a=0;a<m.length;a++)e.internal.write("(page_"+(a+1)+")"+m[a]+" 0 R");e.internal.write(" ] >>","endobj"),t=e.internal.newObject(),e.internal.write("<< /Dests "+p+" 0 R"),e.internal.write(">>","endobj")}}]),r.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+t+" 0 R"))}]),r.events.push(["initialized",function(){var e=this;e.outline={createNamedDestinations:!1,root:{children:[]}},e.outline.add=function(i,o,a){var l={title:o,options:a,children:[]};return i==null&&(i=this.root),i.children.push(l),l},e.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=e,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},e.outline.genIds_r=function(i){i.id=e.internal.newObjectDeferred();for(var o=0;o<i.children.length;o++)this.genIds_r(i.children[o])},e.outline.renderRoot=function(i){this.objStart(i),this.line("/Type /Outlines"),i.children.length>0&&(this.line("/First "+this.makeRef(i.children[0])),this.line("/Last "+this.makeRef(i.children[i.children.length-1]))),this.line("/Count "+this.count_r({count:0},i)),this.objEnd()},e.outline.renderItems=function(i){for(var o=this.ctx.pdf.internal.getVerticalCoordinateString,a=0;a<i.children.length;a++){var l=i.children[a];this.objStart(l),this.line("/Title "+this.makeString(l.title)),this.line("/Parent "+this.makeRef(i)),a>0&&this.line("/Prev "+this.makeRef(i.children[a-1])),a<i.children.length-1&&this.line("/Next "+this.makeRef(i.children[a+1])),l.children.length>0&&(this.line("/First "+this.makeRef(l.children[0])),this.line("/Last "+this.makeRef(l.children[l.children.length-1])));var h=this.count=this.count_r({count:0},l);if(h>0&&this.line("/Count "+h),l.options&&l.options.pageNumber){var c=e.internal.getPageInfo(l.options.pageNumber);this.line("/Dest ["+c.objId+" 0 R /XYZ 0 "+o(0)+" 0]")}this.objEnd()}for(var d=0;d<i.children.length;d++)this.renderItems(i.children[d])},e.outline.line=function(i){this.ctx.val+=i+`\r
`},e.outline.makeRef=function(i){return i.id+" 0 R"},e.outline.makeString=function(i){return"("+e.internal.pdfEscape(i)+")"},e.outline.objStart=function(i){this.ctx.val+=`\r
`+i.id+` 0 obj\r
<<\r
`},e.outline.objEnd=function(){this.ctx.val+=`>> \r
endobj\r
`},e.outline.count_r=function(i,o){for(var a=0;a<o.children.length;a++)i.count++,this.count_r(i,o.children[a]);return i.count}}])}(Ut.API),function(r){var t=[192,193,194,195,196,197,198,199];r.processJPEG=function(e,i,o,a,l,h){var c,d=this.decode.DCT_DECODE,m=null;if(typeof e=="string"||this.__addimage__.isArrayBuffer(e)||this.__addimage__.isArrayBufferView(e)){switch(e=l||e,e=this.__addimage__.isArrayBuffer(e)?new Uint8Array(e):e,(c=function(v){for(var w,p=256*v.charCodeAt(4)+v.charCodeAt(5),C=v.length,k={width:0,height:0,numcomponents:1},B=4;B<C;B+=2){if(B+=p,t.indexOf(v.charCodeAt(B+1))!==-1){w=256*v.charCodeAt(B+5)+v.charCodeAt(B+6),k={width:256*v.charCodeAt(B+7)+v.charCodeAt(B+8),height:w,numcomponents:v.charCodeAt(B+9)};break}p=256*v.charCodeAt(B+2)+v.charCodeAt(B+3)}return k}(e=this.__addimage__.isArrayBufferView(e)?this.__addimage__.arrayBufferToBinaryString(e):e)).numcomponents){case 1:h=this.color_spaces.DEVICE_GRAY;break;case 4:h=this.color_spaces.DEVICE_CMYK;break;case 3:h=this.color_spaces.DEVICE_RGB}m={data:e,width:c.width,height:c.height,colorSpace:h,bitsPerComponent:8,filter:d,index:i,alias:o}}return m}}(Ut.API);var ji,po,Ml,Tl,El,cc=function(){var r,t,e;function i(a){var l,h,c,d,m,v,w,p,C,k,B,P,O,E;for(this.data=a,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},v=null;;){switch(l=this.readUInt32(),C=(function(){var K,st;for(st=[],K=0;K<4;++K)st.push(String.fromCharCode(this.data[this.pos++]));return st}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(l);break;case"fcTL":v&&this.animation.frames.push(v),this.pos+=4,v={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},m=this.readUInt16(),d=this.readUInt16()||100,v.delay=1e3*m/d,v.disposeOp=this.data[this.pos++],v.blendOp=this.data[this.pos++],v.data=[];break;case"IDAT":case"fdAT":for(C==="fdAT"&&(this.pos+=4,l-=4),a=(v!=null?v.data:void 0)||this.imgData,P=0;0<=l?P<l:P>l;0<=l?++P:--P)a.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(c=this.palette.length/3,this.transparency.indexed=this.read(l),this.transparency.indexed.length>c)throw new Error("More transparent colors than palette size");if((k=c-this.transparency.indexed.length)>0)for(O=0;0<=k?O<k:O>k;0<=k?++O:--O)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(l)[0];break;case 2:this.transparency.rgb=this.read(l)}break;case"tEXt":w=(B=this.read(l)).indexOf(0),p=String.fromCharCode.apply(String,B.slice(0,w)),this.text[p]=String.fromCharCode.apply(String,B.slice(w+1));break;case"IEND":return v&&this.animation.frames.push(v),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=(E=this.colorType)===4||E===6,h=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*h,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=l}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}i.prototype.read=function(a){var l,h;for(h=[],l=0;0<=a?l<a:l>a;0<=a?++l:--l)h.push(this.data[this.pos++]);return h},i.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},i.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},i.prototype.decodePixels=function(a){var l=this.pixelBitlength/8,h=new Uint8Array(this.width*this.height*l),c=0,d=this;if(a==null&&(a=this.imgData),a.length===0)return new Uint8Array(0);function m(v,w,p,C){var k,B,P,O,E,K,st,yt,Q,q,rt,pt,_,j,V,R,ot,nt,ut,Z,ht,dt=Math.ceil((d.width-v)/p),It=Math.ceil((d.height-w)/C),N=d.width==dt&&d.height==It;for(j=l*dt,pt=N?h:new Uint8Array(j*It),K=a.length,_=0,B=0;_<It&&c<K;){switch(a[c++]){case 0:for(O=ot=0;ot<j;O=ot+=1)pt[B++]=a[c++];break;case 1:for(O=nt=0;nt<j;O=nt+=1)k=a[c++],E=O<l?0:pt[B-l],pt[B++]=(k+E)%256;break;case 2:for(O=ut=0;ut<j;O=ut+=1)k=a[c++],P=(O-O%l)/l,V=_&&pt[(_-1)*j+P*l+O%l],pt[B++]=(V+k)%256;break;case 3:for(O=Z=0;Z<j;O=Z+=1)k=a[c++],P=(O-O%l)/l,E=O<l?0:pt[B-l],V=_&&pt[(_-1)*j+P*l+O%l],pt[B++]=(k+Math.floor((E+V)/2))%256;break;case 4:for(O=ht=0;ht<j;O=ht+=1)k=a[c++],P=(O-O%l)/l,E=O<l?0:pt[B-l],_===0?V=R=0:(V=pt[(_-1)*j+P*l+O%l],R=P&&pt[(_-1)*j+(P-1)*l+O%l]),st=E+V-R,yt=Math.abs(st-E),q=Math.abs(st-V),rt=Math.abs(st-R),Q=yt<=q&&yt<=rt?E:q<=rt?V:R,pt[B++]=(k+Q)%256;break;default:throw new Error("Invalid filter algorithm: "+a[c-1])}if(!N){var I=((w+_*C)*d.width+v)*l,M=_*j;for(O=0;O<dt;O+=1){for(var U=0;U<l;U+=1)h[I++]=pt[M++];I+=(p-1)*l}}_++}}return a=Hh(a),d.interlaceMethod==1?(m(0,0,8,8),m(4,0,8,8),m(0,4,4,8),m(2,0,4,4),m(0,2,2,4),m(1,0,2,2),m(0,1,1,2)):m(0,0,1,1),h},i.prototype.decodePalette=function(){var a,l,h,c,d,m,v,w,p;for(h=this.palette,m=this.transparency.indexed||[],d=new Uint8Array((m.length||0)+h.length),c=0,a=0,l=v=0,w=h.length;v<w;l=v+=3)d[c++]=h[l],d[c++]=h[l+1],d[c++]=h[l+2],d[c++]=(p=m[a++])!=null?p:255;return d},i.prototype.copyToImageData=function(a,l){var h,c,d,m,v,w,p,C,k,B,P;if(c=this.colors,k=null,h=this.hasAlphaChannel,this.palette.length&&(k=(P=this._decodedPalette)!=null?P:this._decodedPalette=this.decodePalette(),c=4,h=!0),C=(d=a.data||a).length,v=k||l,m=w=0,c===1)for(;m<C;)p=k?4*l[m/4]:w,B=v[p++],d[m++]=B,d[m++]=B,d[m++]=B,d[m++]=h?v[p++]:255,w=p;else for(;m<C;)p=k?4*l[m/4]:w,d[m++]=v[p++],d[m++]=v[p++],d[m++]=v[p++],d[m++]=h?v[p++]:255,w=p},i.prototype.decode=function(){var a;return a=new Uint8Array(this.width*this.height*4),this.copyToImageData(a,this.decodePixels()),a};var o=function(){if(Object.prototype.toString.call(Wt)==="[object Window]"){try{t=Wt.document.createElement("canvas"),e=t.getContext("2d")}catch{return!1}return!0}return!1};return o(),r=function(a){var l;if(o()===!0)return e.width=a.width,e.height=a.height,e.clearRect(0,0,a.width,a.height),e.putImageData(a,0,0),(l=new Image).src=t.toDataURL(),l;throw new Error("This method requires a Browser with Canvas-capability.")},i.prototype.decodeFrames=function(a){var l,h,c,d,m,v,w,p;if(this.animation){for(p=[],h=m=0,v=(w=this.animation.frames).length;m<v;h=++m)l=w[h],c=a.createImageData(l.width,l.height),d=this.decodePixels(new Uint8Array(l.data)),this.copyToImageData(c,d),l.imageData=c,p.push(l.image=r(c));return p}},i.prototype.renderFrame=function(a,l){var h,c,d;return h=(c=this.animation.frames)[l],d=c[l-1],l===0&&a.clearRect(0,0,this.width,this.height),(d!=null?d.disposeOp:void 0)===1?a.clearRect(d.xOffset,d.yOffset,d.width,d.height):(d!=null?d.disposeOp:void 0)===2&&a.putImageData(d.imageData,d.xOffset,d.yOffset),h.blendOp===0&&a.clearRect(h.xOffset,h.yOffset,h.width,h.height),a.drawImage(h.image,h.xOffset,h.yOffset)},i.prototype.animate=function(a){var l,h,c,d,m,v,w=this;return h=0,v=this.animation,d=v.numFrames,c=v.frames,m=v.numPlays,(l=function(){var p,C;if(p=h++%d,C=c[p],w.renderFrame(a,p),d>1&&h/d<m)return w.animation._timeout=setTimeout(l,C.delay)})()},i.prototype.stopAnimation=function(){var a;return clearTimeout((a=this.animation)!=null?a._timeout:void 0)},i.prototype.render=function(a){var l,h;return a._png&&a._png.stopAnimation(),a._png=this,a.width=this.width,a.height=this.height,l=a.getContext("2d"),this.animation?(this.decodeFrames(l),this.animate(l)):(h=l.createImageData(this.width,this.height),this.copyToImageData(h,this.decodePixels()),l.putImageData(h,0,0))},i}();/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 *//**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function fc(r){var t=0;if(r[t++]!==71||r[t++]!==73||r[t++]!==70||r[t++]!==56||(r[t++]+1&253)!=56||r[t++]!==97)throw new Error("Invalid GIF 87a/89a header.");var e=r[t++]|r[t++]<<8,i=r[t++]|r[t++]<<8,o=r[t++],a=o>>7,l=1<<(7&o)+1;r[t++],r[t++];var h=null,c=null;a&&(h=t,c=l,t+=3*l);var d=!0,m=[],v=0,w=null,p=0,C=null;for(this.width=e,this.height=i;d&&t<r.length;)switch(r[t++]){case 33:switch(r[t++]){case 255:if(r[t]!==11||r[t+1]==78&&r[t+2]==69&&r[t+3]==84&&r[t+4]==83&&r[t+5]==67&&r[t+6]==65&&r[t+7]==80&&r[t+8]==69&&r[t+9]==50&&r[t+10]==46&&r[t+11]==48&&r[t+12]==3&&r[t+13]==1&&r[t+16]==0)t+=14,C=r[t++]|r[t++]<<8,t++;else for(t+=12;;){if(!((_=r[t++])>=0))throw Error("Invalid block size");if(_===0)break;t+=_}break;case 249:if(r[t++]!==4||r[t+4]!==0)throw new Error("Invalid graphics extension block.");var k=r[t++];v=r[t++]|r[t++]<<8,w=r[t++],(1&k)==0&&(w=null),p=k>>2&7,t++;break;case 254:for(;;){if(!((_=r[t++])>=0))throw Error("Invalid block size");if(_===0)break;t+=_}break;default:throw new Error("Unknown graphic control label: 0x"+r[t-1].toString(16))}break;case 44:var B=r[t++]|r[t++]<<8,P=r[t++]|r[t++]<<8,O=r[t++]|r[t++]<<8,E=r[t++]|r[t++]<<8,K=r[t++],st=K>>6&1,yt=1<<(7&K)+1,Q=h,q=c,rt=!1;K>>7&&(rt=!0,Q=t,q=yt,t+=3*yt);var pt=t;for(t++;;){var _;if(!((_=r[t++])>=0))throw Error("Invalid block size");if(_===0)break;t+=_}m.push({x:B,y:P,width:O,height:E,has_local_palette:rt,palette_offset:Q,palette_size:q,data_offset:pt,data_length:t-pt,transparent_index:w,interlaced:!!st,delay:v,disposal:p});break;case 59:d=!1;break;default:throw new Error("Unknown gif block: 0x"+r[t-1].toString(16))}this.numFrames=function(){return m.length},this.loopCount=function(){return C},this.frameInfo=function(j){if(j<0||j>=m.length)throw new Error("Frame index out of range.");return m[j]},this.decodeAndBlitFrameBGRA=function(j,V){var R=this.frameInfo(j),ot=R.width*R.height,nt=new Uint8Array(ot);Rl(r,R.data_offset,nt,ot);var ut=R.palette_offset,Z=R.transparent_index;Z===null&&(Z=256);var ht=R.width,dt=e-ht,It=ht,N=4*(R.y*e+R.x),I=4*((R.y+R.height)*e+R.x),M=N,U=4*dt;R.interlaced===!0&&(U+=4*e*7);for(var Y=8,et=0,it=nt.length;et<it;++et){var at=nt[et];if(It===0&&(It=ht,(M+=U)>=I&&(U=4*dt+4*e*(Y-1),M=N+(ht+dt)*(Y<<1),Y>>=1)),at===Z)M+=4;else{var Nt=r[ut+3*at],At=r[ut+3*at+1],jt=r[ut+3*at+2];V[M++]=jt,V[M++]=At,V[M++]=Nt,V[M++]=255}--It}},this.decodeAndBlitFrameRGBA=function(j,V){var R=this.frameInfo(j),ot=R.width*R.height,nt=new Uint8Array(ot);Rl(r,R.data_offset,nt,ot);var ut=R.palette_offset,Z=R.transparent_index;Z===null&&(Z=256);var ht=R.width,dt=e-ht,It=ht,N=4*(R.y*e+R.x),I=4*((R.y+R.height)*e+R.x),M=N,U=4*dt;R.interlaced===!0&&(U+=4*e*7);for(var Y=8,et=0,it=nt.length;et<it;++et){var at=nt[et];if(It===0&&(It=ht,(M+=U)>=I&&(U=4*dt+4*e*(Y-1),M=N+(ht+dt)*(Y<<1),Y>>=1)),at===Z)M+=4;else{var Nt=r[ut+3*at],At=r[ut+3*at+1],jt=r[ut+3*at+2];V[M++]=Nt,V[M++]=At,V[M++]=jt,V[M++]=255}--It}}}function Rl(r,t,e,i){for(var o=r[t++],a=1<<o,l=a+1,h=l+1,c=o+1,d=(1<<c)-1,m=0,v=0,w=0,p=r[t++],C=new Int32Array(4096),k=null;;){for(;m<16&&p!==0;)v|=r[t++]<<m,m+=8,p===1?p=r[t++]:--p;if(m<c)break;var B=v&d;if(v>>=c,m-=c,B!==a){if(B===l)break;for(var P=B<h?B:k,O=0,E=P;E>a;)E=C[E]>>8,++O;var K=E;if(w+O+(P!==B?1:0)>i)return void be.log("Warning, gif stream longer than expected.");e[w++]=K;var st=w+=O;for(P!==B&&(e[w++]=K),E=P;O--;)E=C[E],e[--st]=255&E,E>>=8;k!==null&&h<4096&&(C[h++]=k<<8|K,h>=d+1&&c<12&&(++c,d=d<<1|1)),k=B}else h=l+1,d=(1<<(c=o+1))-1,k=null}return w!==i&&be.log("Warning, gif stream shorter than expected."),e}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function Ss(r){var t,e,i,o,a,l=Math.floor,h=new Array(64),c=new Array(64),d=new Array(64),m=new Array(64),v=new Array(65535),w=new Array(65535),p=new Array(64),C=new Array(64),k=[],B=0,P=7,O=new Array(64),E=new Array(64),K=new Array(64),st=new Array(256),yt=new Array(2048),Q=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],q=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],rt=[0,1,2,3,4,5,6,7,8,9,10,11],pt=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],_=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],j=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],V=[0,1,2,3,4,5,6,7,8,9,10,11],R=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],ot=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function nt(N,I){for(var M=0,U=0,Y=new Array,et=1;et<=16;et++){for(var it=1;it<=N[et];it++)Y[I[U]]=[],Y[I[U]][0]=M,Y[I[U]][1]=et,U++,M++;M*=2}return Y}function ut(N){for(var I=N[0],M=N[1]-1;M>=0;)I&1<<M&&(B|=1<<P),M--,--P<0&&(B==255?(Z(255),Z(0)):Z(B),P=7,B=0)}function Z(N){k.push(N)}function ht(N){Z(N>>8&255),Z(255&N)}function dt(N,I,M,U,Y){for(var et,it=Y[0],at=Y[240],Nt=function(xt,Lt){var Ct,kt,Et,Jt,te,ee,ae,pe,Vt,re,Ft=0;for(Vt=0;Vt<8;++Vt){Ct=xt[Ft],kt=xt[Ft+1],Et=xt[Ft+2],Jt=xt[Ft+3],te=xt[Ft+4],ee=xt[Ft+5],ae=xt[Ft+6];var Ye=Ct+(pe=xt[Ft+7]),se=Ct-pe,Pr=kt+ae,me=kt-ae,Ae=Et+ee,Ur=Et-ee,ue=Jt+te,Mn=Jt-te,Le=Ye+ue,_r=Ye-ue,rn=Pr+Ae,Se=Pr-Ae;xt[Ft]=Le+rn,xt[Ft+4]=Le-rn;var Yt=.707106781*(Se+_r);xt[Ft+2]=_r+Yt,xt[Ft+6]=_r-Yt;var he=.382683433*((Le=Mn+Ur)-(Se=me+se)),Tn=.5411961*Le+he,Ve=1.306562965*Se+he,Hr=.707106781*(rn=Ur+me),Wr=se+Hr,zt=se-Hr;xt[Ft+5]=zt+Tn,xt[Ft+3]=zt-Tn,xt[Ft+1]=Wr+Ve,xt[Ft+7]=Wr-Ve,Ft+=8}for(Ft=0,Vt=0;Vt<8;++Vt){Ct=xt[Ft],kt=xt[Ft+8],Et=xt[Ft+16],Jt=xt[Ft+24],te=xt[Ft+32],ee=xt[Ft+40],ae=xt[Ft+48];var kr=Ct+(pe=xt[Ft+56]),Vr=Ct-pe,ar=kt+ae,Re=kt-ae,Oe=Et+ee,fr=Et-ee,$n=Jt+te,nn=Jt-te,jr=kr+$n,Cr=kr-$n,Fr=ar+Oe,Gr=ar-Oe;xt[Ft]=jr+Fr,xt[Ft+32]=jr-Fr;var br=.707106781*(Gr+Cr);xt[Ft+16]=Cr+br,xt[Ft+48]=Cr-br;var Jr=.382683433*((jr=nn+fr)-(Gr=Re+Vr)),En=.5411961*jr+Jr,ti=1.306562965*Gr+Jr,ei=.707106781*(Fr=fr+Re),ri=Vr+ei,ni=Vr-ei;xt[Ft+40]=ni+En,xt[Ft+24]=ni-En,xt[Ft+8]=ri+ti,xt[Ft+56]=ri-ti,Ft++}for(Vt=0;Vt<64;++Vt)re=xt[Vt]*Lt[Vt],p[Vt]=re>0?re+.5|0:re-.5|0;return p}(N,I),At=0;At<64;++At)C[Q[At]]=Nt[At];var jt=C[0]-M;M=C[0],jt==0?ut(U[0]):(ut(U[w[et=32767+jt]]),ut(v[et]));for(var Pt=63;Pt>0&&C[Pt]==0;)Pt--;if(Pt==0)return ut(it),M;for(var Ht,ft=1;ft<=Pt;){for(var T=ft;C[ft]==0&&ft<=Pt;)++ft;var Qt=ft-T;if(Qt>=16){Ht=Qt>>4;for(var Mt=1;Mt<=Ht;++Mt)ut(at);Qt&=15}et=32767+C[ft],ut(Y[(Qt<<4)+w[et]]),ut(v[et]),ft++}return Pt!=63&&ut(it),M}function It(N){N=Math.min(Math.max(N,1),100),a!=N&&(function(I){for(var M=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],U=0;U<64;U++){var Y=l((M[U]*I+50)/100);Y=Math.min(Math.max(Y,1),255),h[Q[U]]=Y}for(var et=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],it=0;it<64;it++){var at=l((et[it]*I+50)/100);at=Math.min(Math.max(at,1),255),c[Q[it]]=at}for(var Nt=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],At=0,jt=0;jt<8;jt++)for(var Pt=0;Pt<8;Pt++)d[At]=1/(h[Q[At]]*Nt[jt]*Nt[Pt]*8),m[At]=1/(c[Q[At]]*Nt[jt]*Nt[Pt]*8),At++}(N<50?Math.floor(5e3/N):Math.floor(200-2*N)),a=N)}this.encode=function(N,I){I&&It(I),k=new Array,B=0,P=7,ht(65496),ht(65504),ht(16),Z(74),Z(70),Z(73),Z(70),Z(0),Z(1),Z(1),Z(0),ht(1),ht(1),Z(0),Z(0),function(){ht(65499),ht(132),Z(0);for(var kt=0;kt<64;kt++)Z(h[kt]);Z(1);for(var Et=0;Et<64;Et++)Z(c[Et])}(),function(kt,Et){ht(65472),ht(17),Z(8),ht(Et),ht(kt),Z(3),Z(1),Z(17),Z(0),Z(2),Z(17),Z(1),Z(3),Z(17),Z(1)}(N.width,N.height),function(){ht(65476),ht(418),Z(0);for(var kt=0;kt<16;kt++)Z(q[kt+1]);for(var Et=0;Et<=11;Et++)Z(rt[Et]);Z(16);for(var Jt=0;Jt<16;Jt++)Z(pt[Jt+1]);for(var te=0;te<=161;te++)Z(_[te]);Z(1);for(var ee=0;ee<16;ee++)Z(j[ee+1]);for(var ae=0;ae<=11;ae++)Z(V[ae]);Z(17);for(var pe=0;pe<16;pe++)Z(R[pe+1]);for(var Vt=0;Vt<=161;Vt++)Z(ot[Vt])}(),ht(65498),ht(12),Z(3),Z(1),Z(0),Z(2),Z(17),Z(3),Z(17),Z(0),Z(63),Z(0);var M=0,U=0,Y=0;B=0,P=7,this.encode.displayName="_encode_";for(var et,it,at,Nt,At,jt,Pt,Ht,ft,T=N.data,Qt=N.width,Mt=N.height,xt=4*Qt,Lt=0;Lt<Mt;){for(et=0;et<xt;){for(At=xt*Lt+et,Pt=-1,Ht=0,ft=0;ft<64;ft++)jt=At+(Ht=ft>>3)*xt+(Pt=4*(7&ft)),Lt+Ht>=Mt&&(jt-=xt*(Lt+1+Ht-Mt)),et+Pt>=xt&&(jt-=et+Pt-xt+4),it=T[jt++],at=T[jt++],Nt=T[jt++],O[ft]=(yt[it]+yt[at+256>>0]+yt[Nt+512>>0]>>16)-128,E[ft]=(yt[it+768>>0]+yt[at+1024>>0]+yt[Nt+1280>>0]>>16)-128,K[ft]=(yt[it+1280>>0]+yt[at+1536>>0]+yt[Nt+1792>>0]>>16)-128;M=dt(O,d,M,t,i),U=dt(E,m,U,e,o),Y=dt(K,m,Y,e,o),et+=32}Lt+=8}if(P>=0){var Ct=[];Ct[1]=P+1,Ct[0]=(1<<P+1)-1,ut(Ct)}return ht(65497),new Uint8Array(k)},r=r||50,function(){for(var N=String.fromCharCode,I=0;I<256;I++)st[I]=N(I)}(),t=nt(q,rt),e=nt(j,V),i=nt(pt,_),o=nt(R,ot),function(){for(var N=1,I=2,M=1;M<=15;M++){for(var U=N;U<I;U++)w[32767+U]=M,v[32767+U]=[],v[32767+U][1]=M,v[32767+U][0]=U;for(var Y=-(I-1);Y<=-N;Y++)w[32767+Y]=M,v[32767+Y]=[],v[32767+Y][1]=M,v[32767+Y][0]=I-1+Y;N<<=1,I<<=1}}(),function(){for(var N=0;N<256;N++)yt[N]=19595*N,yt[N+256>>0]=38470*N,yt[N+512>>0]=7471*N+32768,yt[N+768>>0]=-11059*N,yt[N+1024>>0]=-21709*N,yt[N+1280>>0]=32768*N+8421375,yt[N+1536>>0]=-27439*N,yt[N+1792>>0]=-5329*N}(),It(r)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function Er(r,t){if(this.pos=0,this.buffer=r,this.datav=new DataView(r.buffer),this.is_with_alpha=!!t,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function ql(r){function t(q){if(!q)throw Error("assert :P")}function e(q,rt,pt){for(var _=0;4>_;_++)if(q[rt+_]!=pt.charCodeAt(_))return!0;return!1}function i(q,rt,pt,_,j){for(var V=0;V<j;V++)q[rt+V]=pt[_+V]}function o(q,rt,pt,_){for(var j=0;j<_;j++)q[rt+j]=pt}function a(q){return new Int32Array(q)}function l(q,rt){for(var pt=[],_=0;_<q;_++)pt.push(new rt);return pt}function h(q,rt){var pt=[];return function _(j,V,R){for(var ot=R[V],nt=0;nt<ot&&(j.push(R.length>V+1?[]:new rt),!(R.length<V+1));nt++)_(j[nt],V+1,R)}(pt,0,q),pt}var c=function(){var q=this;function rt(n,s){for(var f=1<<s-1>>>0;n&f;)f>>>=1;return f?(n&f-1)+f:n}function pt(n,s,f,g,b){t(!(g%f));do n[s+(g-=f)]=b;while(0<g)}function _(n,s,f,g,b){if(t(2328>=b),512>=b)var x=a(512);else if((x=a(b))==null)return 0;return function(A,L,S,F,H,$){var tt,J,vt=L,lt=1<<S,W=a(16),G=a(16);for(t(H!=0),t(F!=null),t(A!=null),t(0<S),J=0;J<H;++J){if(15<F[J])return 0;++W[F[J]]}if(W[0]==H)return 0;for(G[1]=0,tt=1;15>tt;++tt){if(W[tt]>1<<tt)return 0;G[tt+1]=G[tt]+W[tt]}for(J=0;J<H;++J)tt=F[J],0<F[J]&&($[G[tt]++]=J);if(G[15]==1)return(F=new j).g=0,F.value=$[0],pt(A,vt,1,lt,F),lt;var gt,bt=-1,mt=lt-1,Bt=0,St=1,qt=1,_t=1<<S;for(J=0,tt=1,H=2;tt<=S;++tt,H<<=1){if(St+=qt<<=1,0>(qt-=W[tt]))return 0;for(;0<W[tt];--W[tt])(F=new j).g=tt,F.value=$[J++],pt(A,vt+Bt,H,_t,F),Bt=rt(Bt,tt)}for(tt=S+1,H=2;15>=tt;++tt,H<<=1){if(St+=qt<<=1,0>(qt-=W[tt]))return 0;for(;0<W[tt];--W[tt]){if(F=new j,(Bt&mt)!=bt){for(vt+=_t,gt=1<<(bt=tt)-S;15>bt&&!(0>=(gt-=W[bt]));)++bt,gt<<=1;lt+=_t=1<<(gt=bt-S),A[L+(bt=Bt&mt)].g=gt+S,A[L+bt].value=vt-L-bt}F.g=tt-S,F.value=$[J++],pt(A,vt+(Bt>>S),H,_t,F),Bt=rt(Bt,tt)}}return St!=2*G[15]-1?0:lt}(n,s,f,g,b,x)}function j(){this.value=this.g=0}function V(){this.value=this.g=0}function R(){this.G=l(5,j),this.H=a(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=l(Ge,V)}function ot(n,s,f,g){t(n!=null),t(s!=null),t(2147483648>g),n.Ca=254,n.I=0,n.b=-8,n.Ka=0,n.oa=s,n.pa=f,n.Jd=s,n.Yc=f+g,n.Zc=4<=g?f+g-4+1:f,et(n)}function nt(n,s){for(var f=0;0<s--;)f|=at(n,128)<<s;return f}function ut(n,s){var f=nt(n,s);return it(n)?-f:f}function Z(n,s,f,g){var b,x=0;for(t(n!=null),t(s!=null),t(4294967288>g),n.Sb=g,n.Ra=0,n.u=0,n.h=0,4<g&&(g=4),b=0;b<g;++b)x+=s[f+b]<<8*b;n.Ra=x,n.bb=g,n.oa=s,n.pa=f}function ht(n){for(;8<=n.u&&n.bb<n.Sb;)n.Ra>>>=8,n.Ra+=n.oa[n.pa+n.bb]<<bi-8>>>0,++n.bb,n.u-=8;M(n)&&(n.h=1,n.u=0)}function dt(n,s){if(t(0<=s),!n.h&&s<=vi){var f=I(n)&mi[s];return n.u+=s,ht(n),f}return n.h=1,n.u=0}function It(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function N(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function I(n){return n.Ra>>>(n.u&bi-1)>>>0}function M(n){return t(n.bb<=n.Sb),n.h||n.bb==n.Sb&&n.u>bi}function U(n,s){n.u=s,n.h=M(n)}function Y(n){n.u>=ra&&(t(n.u>=ra),ht(n))}function et(n){t(n!=null&&n.oa!=null),n.pa<n.Zc?(n.I=(n.oa[n.pa++]|n.I<<8)>>>0,n.b+=8):(t(n!=null&&n.oa!=null),n.pa<n.Yc?(n.b+=8,n.I=n.oa[n.pa++]|n.I<<8):n.Ka?n.b=0:(n.I<<=8,n.b+=8,n.Ka=1))}function it(n){return nt(n,1)}function at(n,s){var f=n.Ca;0>n.b&&et(n);var g=n.b,b=f*s>>>8,x=(n.I>>>g>b)+0;for(x?(f-=b,n.I-=b+1<<g>>>0):f=b+1,g=f,b=0;256<=g;)b+=8,g>>=8;return g=7^b+sr[g],n.b-=g,n.Ca=(f<<g)-1,x}function Nt(n,s,f){n[s+0]=f>>24&255,n[s+1]=f>>16&255,n[s+2]=f>>8&255,n[s+3]=f>>0&255}function At(n,s){return n[s+0]<<0|n[s+1]<<8}function jt(n,s){return At(n,s)|n[s+2]<<16}function Pt(n,s){return At(n,s)|At(n,s+2)<<16}function Ht(n,s){var f=1<<s;return t(n!=null),t(0<s),n.X=a(f),n.X==null?0:(n.Mb=32-s,n.Xa=s,1)}function ft(n,s){t(n!=null),t(s!=null),t(n.Xa==s.Xa),i(s.X,0,n.X,0,1<<s.Xa)}function T(){this.X=[],this.Xa=this.Mb=0}function Qt(n,s,f,g){t(f!=null),t(g!=null);var b=f[0],x=g[0];return b==0&&(b=(n*x+s/2)/s),x==0&&(x=(s*b+n/2)/n),0>=b||0>=x?0:(f[0]=b,g[0]=x,1)}function Mt(n,s){return n+(1<<s)-1>>>s}function xt(n,s){return((4278255360&n)+(4278255360&s)>>>0&4278255360)+((16711935&n)+(16711935&s)>>>0&16711935)>>>0}function Lt(n,s){q[s]=function(f,g,b,x,A,L,S){var F;for(F=0;F<A;++F){var H=q[n](L[S+F-1],b,x+F);L[S+F]=xt(f[g+F],H)}}}function Ct(){this.ud=this.hd=this.jd=0}function kt(n,s){return((4278124286&(n^s))>>>1)+(n&s)>>>0}function Et(n){return 0<=n&&256>n?n:0>n?0:255<n?255:void 0}function Jt(n,s){return Et(n+(n-s+.5>>1))}function te(n,s,f){return Math.abs(s-f)-Math.abs(n-f)}function ee(n,s,f,g,b,x,A){for(g=x[A-1],f=0;f<b;++f)x[A+f]=g=xt(n[s+f],g)}function ae(n,s,f,g,b){var x;for(x=0;x<f;++x){var A=n[s+x],L=A>>8&255,S=16711935&(S=(S=16711935&A)+((L<<16)+L));g[b+x]=(4278255360&A)+S>>>0}}function pe(n,s){s.jd=n>>0&255,s.hd=n>>8&255,s.ud=n>>16&255}function Vt(n,s,f,g,b,x){var A;for(A=0;A<g;++A){var L=s[f+A],S=L>>>8,F=L,H=255&(H=(H=L>>>16)+((n.jd<<24>>24)*(S<<24>>24)>>>5));F=255&(F=(F=F+((n.hd<<24>>24)*(S<<24>>24)>>>5))+((n.ud<<24>>24)*(H<<24>>24)>>>5)),b[x+A]=(4278255360&L)+(H<<16)+F}}function re(n,s,f,g,b){q[s]=function(x,A,L,S,F,H,$,tt,J){for(S=$;S<tt;++S)for($=0;$<J;++$)F[H++]=b(L[g(x[A++])])},q[n]=function(x,A,L,S,F,H,$){var tt=8>>x.b,J=x.Ea,vt=x.K[0],lt=x.w;if(8>tt)for(x=(1<<x.b)-1,lt=(1<<tt)-1;A<L;++A){var W,G=0;for(W=0;W<J;++W)W&x||(G=g(S[F++])),H[$++]=b(vt[G&lt]),G>>=tt}else q["VP8LMapColor"+f](S,F,vt,lt,H,$,A,L,J)}}function Ft(n,s,f,g,b){for(f=s+f;s<f;){var x=n[s++];g[b++]=x>>16&255,g[b++]=x>>8&255,g[b++]=x>>0&255}}function Ye(n,s,f,g,b){for(f=s+f;s<f;){var x=n[s++];g[b++]=x>>16&255,g[b++]=x>>8&255,g[b++]=x>>0&255,g[b++]=x>>24&255}}function se(n,s,f,g,b){for(f=s+f;s<f;){var x=(A=n[s++])>>16&240|A>>12&15,A=A>>0&240|A>>28&15;g[b++]=x,g[b++]=A}}function Pr(n,s,f,g,b){for(f=s+f;s<f;){var x=(A=n[s++])>>16&248|A>>13&7,A=A>>5&224|A>>3&31;g[b++]=x,g[b++]=A}}function me(n,s,f,g,b){for(f=s+f;s<f;){var x=n[s++];g[b++]=x>>0&255,g[b++]=x>>8&255,g[b++]=x>>16&255}}function Ae(n,s,f,g,b,x){if(x==0)for(f=s+f;s<f;)Nt(g,((x=n[s++])[0]>>24|x[1]>>8&65280|x[2]<<8&16711680|x[3]<<24)>>>0),b+=32;else i(g,b,n,s,f)}function Ur(n,s){q[s][0]=q[n+"0"],q[s][1]=q[n+"1"],q[s][2]=q[n+"2"],q[s][3]=q[n+"3"],q[s][4]=q[n+"4"],q[s][5]=q[n+"5"],q[s][6]=q[n+"6"],q[s][7]=q[n+"7"],q[s][8]=q[n+"8"],q[s][9]=q[n+"9"],q[s][10]=q[n+"10"],q[s][11]=q[n+"11"],q[s][12]=q[n+"12"],q[s][13]=q[n+"13"],q[s][14]=q[n+"0"],q[s][15]=q[n+"0"]}function ue(n){return n==ts||n==es||n==Ka||n==rs}function Mn(){this.eb=[],this.size=this.A=this.fb=0}function Le(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function _r(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new Mn,this.f.kb=new Le,this.sd=null}function rn(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function Se(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function Yt(n){return alert("todo:WebPSamplerProcessPlane"),n.T}function he(n,s){var f=n.T,g=s.ba.f.RGBA,b=g.eb,x=g.fb+n.ka*g.A,A=Lr[s.ba.S],L=n.y,S=n.O,F=n.f,H=n.N,$=n.ea,tt=n.W,J=s.cc,vt=s.dc,lt=s.Mc,W=s.Nc,G=n.ka,gt=n.ka+n.T,bt=n.U,mt=bt+1>>1;for(G==0?A(L,S,null,null,F,H,$,tt,F,H,$,tt,b,x,null,null,bt):(A(s.ec,s.fc,L,S,J,vt,lt,W,F,H,$,tt,b,x-g.A,b,x,bt),++f);G+2<gt;G+=2)J=F,vt=H,lt=$,W=tt,H+=n.Rc,tt+=n.Rc,x+=2*g.A,A(L,(S+=2*n.fa)-n.fa,L,S,J,vt,lt,W,F,H,$,tt,b,x-g.A,b,x,bt);return S+=n.fa,n.j+gt<n.o?(i(s.ec,s.fc,L,S,bt),i(s.cc,s.dc,F,H,mt),i(s.Mc,s.Nc,$,tt,mt),f--):1&gt||A(L,S,null,null,F,H,$,tt,F,H,$,tt,b,x+g.A,null,null,bt),f}function Tn(n,s,f){var g=n.F,b=[n.J];if(g!=null){var x=n.U,A=s.ba.S,L=A==Ya||A==Ka;s=s.ba.f.RGBA;var S=[0],F=n.ka;S[0]=n.T,n.Kb&&(F==0?--S[0]:(--F,b[0]-=n.width),n.j+n.ka+n.T==n.o&&(S[0]=n.o-n.j-F));var H=s.eb;F=s.fb+F*s.A,n=we(g,b[0],n.width,x,S,H,F+(L?0:3),s.A),t(f==S),n&&ue(A)&&Ar(H,F,L,x,S,s.A)}return 0}function Ve(n){var s=n.ma,f=s.ba.S,g=11>f,b=f==Ga||f==Ja||f==Ya||f==$o||f==12||ue(f);if(s.memory=null,s.Ib=null,s.Jb=null,s.Nd=null,!ta(s.Oa,n,b?11:12))return 0;if(b&&ue(f)&&wt(),n.da)alert("todo:use_scaling");else{if(g){if(s.Ib=Yt,n.Kb){if(f=n.U+1>>1,s.memory=a(n.U+2*f),s.memory==null)return 0;s.ec=s.memory,s.fc=0,s.cc=s.ec,s.dc=s.fc+n.U,s.Mc=s.cc,s.Nc=s.dc+f,s.Ib=he,wt()}}else alert("todo:EmitYUV");b&&(s.Jb=Tn,g&&X())}if(g&&!nl){for(n=0;256>n;++n)Ku[n]=89858*(n-128)+Qa>>Xa,Zu[n]=-22014*(n-128)+Qa,Qu[n]=-45773*(n-128),Xu[n]=113618*(n-128)+Qa>>Xa;for(n=ua;n<as;++n)s=76283*(n-16)+Qa>>Xa,$u[n-ua]=dr(s,255),th[n-ua]=dr(s+8>>4,15);nl=1}return 1}function Hr(n){var s=n.ma,f=n.U,g=n.T;return t(!(1&n.ka)),0>=f||0>=g?0:(f=s.Ib(n,s),s.Jb!=null&&s.Jb(n,s,f),s.Dc+=f,1)}function Wr(n){n.ma.memory=null}function zt(n,s,f,g){return dt(n,8)!=47?0:(s[0]=dt(n,14)+1,f[0]=dt(n,14)+1,g[0]=dt(n,1),dt(n,3)!=0?0:!n.h)}function kr(n,s){if(4>n)return n+1;var f=n-2>>1;return(2+(1&n)<<f)+dt(s,f)+1}function Vr(n,s){return 120<s?s-120:1<=(f=((f=Ou[s-1])>>4)*n+(8-(15&f)))?f:1;var f}function ar(n,s,f){var g=I(f),b=n[s+=255&g].g-8;return 0<b&&(U(f,f.u+8),g=I(f),s+=n[s].value,s+=g&(1<<b)-1),U(f,f.u+n[s].g),n[s].value}function Re(n,s,f){return f.g+=n.g,f.value+=n.value<<s>>>0,t(8>=f.g),n.g}function Oe(n,s,f){var g=n.xc;return t((s=g==0?0:n.vc[n.md*(f>>g)+(s>>g)])<n.Wb),n.Ya[s]}function fr(n,s,f,g){var b=n.ab,x=n.c*s,A=n.C;s=A+s;var L=f,S=g;for(g=n.Ta,f=n.Ua;0<b--;){var F=n.gc[b],H=A,$=s,tt=L,J=S,vt=(S=g,L=f,F.Ea);switch(t(H<$),t($<=F.nc),F.hc){case 2:Ra(tt,J,($-H)*vt,S,L);break;case 0:var lt=H,W=$,G=S,gt=L,bt=(_t=F).Ea;lt==0&&(Qo(tt,J,null,null,1,G,gt),ee(tt,J+1,0,0,bt-1,G,gt+1),J+=bt,gt+=bt,++lt);for(var mt=1<<_t.b,Bt=mt-1,St=Mt(bt,_t.b),qt=_t.K,_t=_t.w+(lt>>_t.b)*St;lt<W;){var le=qt,ce=_t,oe=1;for(na(tt,J,G,gt-bt,1,G,gt);oe<bt;){var ne=(oe&~Bt)+mt;ne>bt&&(ne=bt),(0,wn[le[ce++]>>8&15])(tt,J+ +oe,G,gt+oe-bt,ne-oe,G,gt+oe),oe=ne}J+=bt,gt+=bt,++lt&Bt||(_t+=St)}$!=F.nc&&i(S,L-vt,S,L+($-H-1)*vt,vt);break;case 1:for(vt=tt,W=J,bt=(tt=F.Ea)-(gt=tt&~(G=(J=1<<F.b)-1)),lt=Mt(tt,F.b),mt=F.K,F=F.w+(H>>F.b)*lt;H<$;){for(Bt=mt,St=F,qt=new Ct,_t=W+gt,le=W+tt;W<_t;)pe(Bt[St++],qt),Hn(qt,vt,W,J,S,L),W+=J,L+=J;W<le&&(pe(Bt[St++],qt),Hn(qt,vt,W,bt,S,L),W+=bt,L+=bt),++H&G||(F+=lt)}break;case 3:if(tt==S&&J==L&&0<F.b){for(W=S,tt=vt=L+($-H)*vt-(gt=($-H)*Mt(F.Ea,F.b)),J=S,G=L,lt=[],gt=(bt=gt)-1;0<=gt;--gt)lt[gt]=J[G+gt];for(gt=bt-1;0<=gt;--gt)W[tt+gt]=lt[gt];yr(F,H,$,S,vt,S,L)}else yr(F,H,$,tt,J,S,L)}L=g,S=f}S!=f&&i(g,f,L,S,x)}function $n(n,s){var f=n.V,g=n.Ba+n.c*n.C,b=s-n.C;if(t(s<=n.l.o),t(16>=b),0<b){var x=n.l,A=n.Ta,L=n.Ua,S=x.width;if(fr(n,b,f,g),b=L=[L],t((f=n.C)<(g=s)),t(x.v<x.va),g>x.o&&(g=x.o),f<x.j){var F=x.j-f;f=x.j,b[0]+=F*S}if(f>=g?f=0:(b[0]+=4*x.v,x.ka=f-x.j,x.U=x.va-x.v,x.T=g-f,f=1),f){if(L=L[0],11>(f=n.ca).S){var H=f.f.RGBA,$=(g=f.S,b=x.U,x=x.T,F=H.eb,H.A),tt=x;for(H=H.fb+n.Ma*H.A;0<tt--;){var J=A,vt=L,lt=b,W=F,G=H;switch(g){case Va:lr(J,vt,lt,W,G);break;case Ga:rr(J,vt,lt,W,G);break;case ts:rr(J,vt,lt,W,G),Ar(W,G,0,lt,1,0);break;case Ys:ln(J,vt,lt,W,G);break;case Ja:Ae(J,vt,lt,W,G,1);break;case es:Ae(J,vt,lt,W,G,1),Ar(W,G,0,lt,1,0);break;case Ya:Ae(J,vt,lt,W,G,0);break;case Ka:Ae(J,vt,lt,W,G,0),Ar(W,G,1,lt,1,0);break;case $o:xn(J,vt,lt,W,G);break;case rs:xn(J,vt,lt,W,G),ye(W,G,lt,1,0);break;case Ks:sn(J,vt,lt,W,G);break;default:t(0)}L+=S,H+=$}n.Ma+=x}else alert("todo:EmitRescaledRowsYUVA");t(n.Ma<=f.height)}}n.C=s,t(n.C<=n.i)}function nn(n){var s;if(0<n.ua)return 0;for(s=0;s<n.Wb;++s){var f=n.Ya[s].G,g=n.Ya[s].H;if(0<f[1][g[1]+0].g||0<f[2][g[2]+0].g||0<f[3][g[3]+0].g)return 0}return 1}function jr(n,s,f,g,b,x){if(n.Z!=0){var A=n.qd,L=n.rd;for(t(Sn[n.Z]!=null);s<f;++s)Sn[n.Z](A,L,g,b,g,b,x),A=g,L=b,b+=x;n.qd=A,n.rd=L}}function Cr(n,s){var f=n.l.ma,g=f.Z==0||f.Z==1?n.l.j:n.C;if(g=n.C<g?g:n.C,t(s<=n.l.o),s>g){var b=n.l.width,x=f.ca,A=f.tb+b*g,L=n.V,S=n.Ba+n.c*g,F=n.gc;t(n.ab==1),t(F[0].hc==3),qa(F[0],g,s,L,S,x,A),jr(f,g,s,x,A,b)}n.C=n.Ma=s}function Fr(n,s,f,g,b,x,A){var L=n.$/g,S=n.$%g,F=n.m,H=n.s,$=f+n.$,tt=$;b=f+g*b;var J=f+g*x,vt=280+H.ua,lt=n.Pb?L:16777216,W=0<H.ua?H.Wa:null,G=H.wc,gt=$<J?Oe(H,S,L):null;t(n.C<x),t(J<=b);var bt=!1;t:for(;;){for(;bt||$<J;){var mt=0;if(L>=lt){var Bt=$-f;t((lt=n).Pb),lt.wd=lt.m,lt.xd=Bt,0<lt.s.ua&&ft(lt.s.Wa,lt.s.vb),lt=L+Tu}if(S&G||(gt=Oe(H,S,L)),t(gt!=null),gt.Qb&&(s[$]=gt.qb,bt=!0),!bt)if(Y(F),gt.jc){mt=F,Bt=s;var St=$,qt=gt.pd[I(mt)&Ge-1];t(gt.jc),256>qt.g?(U(mt,mt.u+qt.g),Bt[St]=qt.value,mt=0):(U(mt,mt.u+qt.g-256),t(256<=qt.value),mt=qt.value),mt==0&&(bt=!0)}else mt=ar(gt.G[0],gt.H[0],F);if(F.h)break;if(bt||256>mt){if(!bt)if(gt.nd)s[$]=(gt.qb|mt<<8)>>>0;else{if(Y(F),bt=ar(gt.G[1],gt.H[1],F),Y(F),Bt=ar(gt.G[2],gt.H[2],F),St=ar(gt.G[3],gt.H[3],F),F.h)break;s[$]=(St<<24|bt<<16|mt<<8|Bt)>>>0}if(bt=!1,++$,++S>=g&&(S=0,++L,A!=null&&L<=x&&!(L%16)&&A(n,L),W!=null))for(;tt<$;)mt=s[tt++],W.X[(506832829*mt&**********)>>>W.Mb]=mt}else if(280>mt){if(mt=kr(mt-256,F),Bt=ar(gt.G[4],gt.H[4],F),Y(F),Bt=Vr(g,Bt=kr(Bt,F)),F.h)break;if($-f<Bt||b-$<mt)break t;for(St=0;St<mt;++St)s[$+St]=s[$+St-Bt];for($+=mt,S+=mt;S>=g;)S-=g,++L,A!=null&&L<=x&&!(L%16)&&A(n,L);if(t($<=b),S&G&&(gt=Oe(H,S,L)),W!=null)for(;tt<$;)mt=s[tt++],W.X[(506832829*mt&**********)>>>W.Mb]=mt}else{if(!(mt<vt))break t;for(bt=mt-280,t(W!=null);tt<$;)mt=s[tt++],W.X[(506832829*mt&**********)>>>W.Mb]=mt;mt=$,t(!(bt>>>(Bt=W).Xa)),s[mt]=Bt.X[bt],bt=!0}bt||t(F.h==M(F))}if(n.Pb&&F.h&&$<b)t(n.m.h),n.a=5,n.m=n.wd,n.$=n.xd,0<n.s.ua&&ft(n.s.vb,n.s.Wa);else{if(F.h)break t;A!=null&&A(n,L>x?x:L),n.a=0,n.$=$-f}return 1}return n.a=3,0}function Gr(n){t(n!=null),n.vc=null,n.yc=null,n.Ya=null;var s=n.Wa;s!=null&&(s.X=null),n.vb=null,t(n!=null)}function br(){var n=new Xo;return n==null?null:(n.a=0,n.xb=Zs,Ur("Predictor","VP8LPredictors"),Ur("Predictor","VP8LPredictors_C"),Ur("PredictorAdd","VP8LPredictorsAdd"),Ur("PredictorAdd","VP8LPredictorsAdd_C"),Ra=ae,Hn=Vt,lr=Ft,rr=Ye,xn=se,sn=Pr,ln=me,q.VP8LMapColor32b=yi,q.VP8LMapColor8b=za,n)}function Jr(n,s,f,g,b){var x=1,A=[n],L=[s],S=g.m,F=g.s,H=null,$=0;t:for(;;){if(f)for(;x&&dt(S,1);){var tt=A,J=L,vt=g,lt=1,W=vt.m,G=vt.gc[vt.ab],gt=dt(W,2);if(vt.Oc&1<<gt)x=0;else{switch(vt.Oc|=1<<gt,G.hc=gt,G.Ea=tt[0],G.nc=J[0],G.K=[null],++vt.ab,t(4>=vt.ab),gt){case 0:case 1:G.b=dt(W,3)+2,lt=Jr(Mt(G.Ea,G.b),Mt(G.nc,G.b),0,vt,G.K),G.K=G.K[0];break;case 3:var bt,mt=dt(W,8)+1,Bt=16<mt?0:4<mt?1:2<mt?2:3;if(tt[0]=Mt(G.Ea,Bt),G.b=Bt,bt=lt=Jr(mt,1,0,vt,G.K)){var St,qt=mt,_t=G,le=1<<(8>>_t.b),ce=a(le);if(ce==null)bt=0;else{var oe=_t.K[0],ne=_t.w;for(ce[0]=_t.K[0][0],St=1;St<1*qt;++St)ce[St]=xt(oe[ne+St],ce[St-1]);for(;St<4*le;++St)ce[St]=0;_t.K[0]=null,_t.K[0]=ce,bt=1}}lt=bt;break;case 2:break;default:t(0)}x=lt}}if(A=A[0],L=L[0],x&&dt(S,1)&&!(x=1<=($=dt(S,4))&&11>=$)){g.a=3;break t}var ve;if(ve=x)e:{var ge,$t,Te,ur=g,Ee=A,hr=L,fe=$,gr=f,mr=ur.m,Ue=ur.s,Je=[null],ir=1,Sr=0,Qr=Mu[fe];r:for(;;){if(gr&&dt(mr,1)){var He=dt(mr,3)+2,fn=Mt(Ee,He),Yn=Mt(hr,He),Li=fn*Yn;if(!Jr(fn,Yn,0,ur,Je))break r;for(Je=Je[0],Ue.xc=He,ge=0;ge<Li;++ge){var Pn=Je[ge]>>8&65535;Je[ge]=Pn,Pn>=ir&&(ir=Pn+1)}}if(mr.h)break r;for($t=0;5>$t;++$t){var _e=Xs[$t];!$t&&0<fe&&(_e+=1<<fe),Sr<_e&&(Sr=_e)}var os=l(ir*Qr,j),ol=ir,sl=l(ol,R);if(sl==null)var $a=null;else t(65536>=ol),$a=sl;var ha=a(Sr);if($a==null||ha==null||os==null){ur.a=1;break r}var to=os;for(ge=Te=0;ge<ir;++ge){var Mr=$a[ge],Si=Mr.G,Pi=Mr.H,ll=0,eo=1,ul=0;for($t=0;5>$t;++$t){_e=Xs[$t],Si[$t]=to,Pi[$t]=Te,!$t&&0<fe&&(_e+=1<<fe);i:{var ro,ss=_e,no=ur,ca=ha,nh=to,ih=Te,ls=0,_n=no.m,ah=dt(_n,1);if(o(ca,0,0,ss),ah){var oh=dt(_n,1)+1,sh=dt(_n,1),hl=dt(_n,sh==0?1:8);ca[hl]=1,oh==2&&(ca[hl=dt(_n,8)]=1);var io=1}else{var cl=a(19),fl=dt(_n,4)+4;if(19<fl){no.a=3;var ao=0;break i}for(ro=0;ro<fl;++ro)cl[Bu[ro]]=dt(_n,3);var us=void 0,fa=void 0,dl=no,lh=cl,oo=ss,pl=ca,hs=0,kn=dl.m,gl=8,ml=l(128,j);n:for(;_(ml,0,7,lh,19);){if(dt(kn,1)){var uh=2+2*dt(kn,3);if((us=2+dt(kn,uh))>oo)break n}else us=oo;for(fa=0;fa<oo&&us--;){Y(kn);var vl=ml[0+(127&I(kn))];U(kn,kn.u+vl.g);var _i=vl.value;if(16>_i)pl[fa++]=_i,_i!=0&&(gl=_i);else{var hh=_i==16,bl=_i-16,ch=Iu[bl],yl=dt(kn,Fu[bl])+ch;if(fa+yl>oo)break n;for(var fh=hh?gl:0;0<yl--;)pl[fa++]=fh}}hs=1;break n}hs||(dl.a=3),io=hs}(io=io&&!_n.h)&&(ls=_(nh,ih,8,ca,ss)),io&&ls!=0?ao=ls:(no.a=3,ao=0)}if(ao==0)break r;if(eo&&Du[$t]==1&&(eo=to[Te].g==0),ll+=to[Te].g,Te+=ao,3>=$t){var da,cs=ha[0];for(da=1;da<_e;++da)ha[da]>cs&&(cs=ha[da]);ul+=cs}}if(Mr.nd=eo,Mr.Qb=0,eo&&(Mr.qb=(Si[3][Pi[3]+0].value<<24|Si[1][Pi[1]+0].value<<16|Si[2][Pi[2]+0].value)>>>0,ll==0&&256>Si[0][Pi[0]+0].value&&(Mr.Qb=1,Mr.qb+=Si[0][Pi[0]+0].value<<8)),Mr.jc=!Mr.Qb&&6>ul,Mr.jc){var so,dn=Mr;for(so=0;so<Ge;++so){var jn=so,Cn=dn.pd[jn],lo=dn.G[0][dn.H[0]+jn];256<=lo.value?(Cn.g=lo.g+256,Cn.value=lo.value):(Cn.g=0,Cn.value=0,jn>>=Re(lo,8,Cn),jn>>=Re(dn.G[1][dn.H[1]+jn],16,Cn),jn>>=Re(dn.G[2][dn.H[2]+jn],0,Cn),Re(dn.G[3][dn.H[3]+jn],24,Cn))}}}Ue.vc=Je,Ue.Wb=ir,Ue.Ya=$a,Ue.yc=os,ve=1;break e}ve=0}if(!(x=ve)){g.a=3;break t}if(0<$){if(F.ua=1<<$,!Ht(F.Wa,$)){g.a=1,x=0;break t}}else F.ua=0;var fs=g,wl=A,dh=L,ds=fs.s,ps=ds.xc;if(fs.c=wl,fs.i=dh,ds.md=Mt(wl,ps),ds.wc=ps==0?-1:(1<<ps)-1,f){g.xb=Wu;break t}if((H=a(A*L))==null){g.a=1,x=0;break t}x=(x=Fr(g,H,0,A,L,L,null))&&!S.h;break t}return x?(b!=null?b[0]=H:(t(H==null),t(f)),g.$=0,f||Gr(F)):Gr(F),x}function En(n,s){var f=n.c*n.i,g=f+s+16*s;return t(n.c<=s),n.V=a(g),n.V==null?(n.Ta=null,n.Ua=0,n.a=1,0):(n.Ta=n.V,n.Ua=n.Ba+f+s,1)}function ti(n,s){var f=n.C,g=s-f,b=n.V,x=n.Ba+n.c*f;for(t(s<=n.l.o);0<g;){var A=16<g?16:g,L=n.l.ma,S=n.l.width,F=S*A,H=L.ca,$=L.tb+S*f,tt=n.Ta,J=n.Ua;fr(n,A,b,x),Fe(tt,J,H,$,F),jr(L,f,f+A,H,$,S),g-=A,b+=A*n.c,f+=A}t(f==s),n.C=n.Ma=s}function ei(){this.ub=this.yd=this.td=this.Rb=0}function ri(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function ni(){this.Fb=this.Bb=this.Cb=0,this.Zb=a(4),this.Lb=a(4)}function ya(){this.Yb=function(){var n=[];return function s(f,g,b){for(var x=b[g],A=0;A<x&&(f.push(b.length>g+1?[]:0),!(b.length<g+1));A++)s(f[A],g+1,b)}(n,0,[3,11]),n}()}function _o(){this.jb=a(3),this.Wc=h([4,8],ya),this.Xc=h([4,17],ya)}function ko(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new a(4),this.od=new a(4)}function ii(){this.ld=this.La=this.dd=this.tc=0}function wa(){this.Na=this.la=0}function jo(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Ei(){this.ad=a(384),this.Za=0,this.Ob=a(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function Co(){this.uc=this.M=this.Nb=0,this.wa=Array(new ii),this.Y=0,this.ya=Array(new Ei),this.aa=0,this.l=new ai}function xa(){this.y=a(16),this.f=a(8),this.ea=a(8)}function Fo(){this.cb=this.a=0,this.sc="",this.m=new It,this.Od=new ei,this.Kc=new ri,this.ed=new ko,this.Qa=new ni,this.Ic=this.$c=this.Aa=0,this.D=new Co,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=l(8,It),this.ia=0,this.pb=l(4,jo),this.Pa=new _o,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new xa),this.Hd=0,this.rb=Array(new wa),this.sb=0,this.wa=Array(new ii),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Ei),this.L=this.aa=0,this.gd=h([4,2],ii),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function ai(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function Io(){var n=new Fo;return n!=null&&(n.a=0,n.sc="OK",n.cb=0,n.Xb=0,la||(la=La)),n}function je(n,s,f){return n.a==0&&(n.a=s,n.sc=f,n.cb=0),0}function Aa(n,s,f){return 3<=f&&n[s+0]==157&&n[s+1]==1&&n[s+2]==42}function Na(n,s){if(n==null)return 0;if(n.a=0,n.sc="OK",s==null)return je(n,2,"null VP8Io passed to VP8GetHeaders()");var f=s.data,g=s.w,b=s.ha;if(4>b)return je(n,7,"Truncated header.");var x=f[g+0]|f[g+1]<<8|f[g+2]<<16,A=n.Od;if(A.Rb=!(1&x),A.td=x>>1&7,A.yd=x>>4&1,A.ub=x>>5,3<A.td)return je(n,3,"Incorrect keyframe parameters.");if(!A.yd)return je(n,4,"Frame not displayable.");g+=3,b-=3;var L=n.Kc;if(A.Rb){if(7>b)return je(n,7,"cannot parse picture header");if(!Aa(f,g,b))return je(n,3,"Bad code word");L.c=16383&(f[g+4]<<8|f[g+3]),L.Td=f[g+4]>>6,L.i=16383&(f[g+6]<<8|f[g+5]),L.Ud=f[g+6]>>6,g+=7,b-=7,n.za=L.c+15>>4,n.Ub=L.i+15>>4,s.width=L.c,s.height=L.i,s.Da=0,s.j=0,s.v=0,s.va=s.width,s.o=s.height,s.da=0,s.ib=s.width,s.hb=s.height,s.U=s.width,s.T=s.height,o((x=n.Pa).jb,0,255,x.jb.length),t((x=n.Qa)!=null),x.Cb=0,x.Bb=0,x.Fb=1,o(x.Zb,0,0,x.Zb.length),o(x.Lb,0,0,x.Lb)}if(A.ub>b)return je(n,7,"bad partition length");ot(x=n.m,f,g,A.ub),g+=A.ub,b-=A.ub,A.Rb&&(L.Ld=it(x),L.Kd=it(x)),L=n.Qa;var S,F=n.Pa;if(t(x!=null),t(L!=null),L.Cb=it(x),L.Cb){if(L.Bb=it(x),it(x)){for(L.Fb=it(x),S=0;4>S;++S)L.Zb[S]=it(x)?ut(x,7):0;for(S=0;4>S;++S)L.Lb[S]=it(x)?ut(x,6):0}if(L.Bb)for(S=0;3>S;++S)F.jb[S]=it(x)?nt(x,8):255}else L.Bb=0;if(x.Ka)return je(n,3,"cannot parse segment header");if((L=n.ed).zd=it(x),L.Tb=nt(x,6),L.wb=nt(x,3),L.Pc=it(x),L.Pc&&it(x)){for(F=0;4>F;++F)it(x)&&(L.vd[F]=ut(x,6));for(F=0;4>F;++F)it(x)&&(L.od[F]=ut(x,6))}if(n.L=L.Tb==0?0:L.zd?1:2,x.Ka)return je(n,3,"cannot parse filter header");var H=b;if(b=S=g,g=S+H,L=H,n.Xb=(1<<nt(n.m,2))-1,H<3*(F=n.Xb))f=7;else{for(S+=3*F,L-=3*F,H=0;H<F;++H){var $=f[b+0]|f[b+1]<<8|f[b+2]<<16;$>L&&($=L),ot(n.Jc[+H],f,S,$),S+=$,L-=$,b+=3}ot(n.Jc[+F],f,S,L),f=S<g?0:5}if(f!=0)return je(n,f,"cannot parse partitions");for(f=nt(S=n.m,7),b=it(S)?ut(S,4):0,g=it(S)?ut(S,4):0,L=it(S)?ut(S,4):0,F=it(S)?ut(S,4):0,S=it(S)?ut(S,4):0,H=n.Qa,$=0;4>$;++$){if(H.Cb){var tt=H.Zb[$];H.Fb||(tt+=f)}else{if(0<$){n.pb[$]=n.pb[0];continue}tt=f}var J=n.pb[$];J.Sc[0]=ns[dr(tt+b,127)],J.Sc[1]=is[dr(tt+0,127)],J.Eb[0]=2*ns[dr(tt+g,127)],J.Eb[1]=101581*is[dr(tt+L,127)]>>16,8>J.Eb[1]&&(J.Eb[1]=8),J.Qc[0]=ns[dr(tt+F,117)],J.Qc[1]=is[dr(tt+S,127)],J.lc=tt+S}if(!A.Rb)return je(n,4,"Not a key frame.");for(it(x),A=n.Pa,f=0;4>f;++f){for(b=0;8>b;++b)for(g=0;3>g;++g)for(L=0;11>L;++L)F=at(x,Uu[f][b][g][L])?nt(x,8):qu[f][b][g][L],A.Wc[f][b].Yb[g][L]=F;for(b=0;17>b;++b)A.Xc[f][b]=A.Wc[f][Hu[b]]}return n.kc=it(x),n.kc&&(n.Bd=nt(x,8)),n.cb=1}function La(n,s,f,g,b,x,A){var L=s[b].Yb[f];for(f=0;16>b;++b){if(!at(n,L[f+0]))return b;for(;!at(n,L[f+1]);)if(L=s[++b].Yb[0],f=0,b==16)return 16;var S=s[b+1].Yb;if(at(n,L[f+2])){var F=n,H=0;if(at(F,(tt=L)[($=f)+3]))if(at(F,tt[$+6])){for(L=0,$=2*(H=at(F,tt[$+8]))+(tt=at(F,tt[$+9+H])),H=0,tt=Eu[$];tt[L];++L)H+=H+at(F,tt[L]);H+=3+(8<<$)}else at(F,tt[$+7])?(H=7+2*at(F,165),H+=at(F,145)):H=5+at(F,159);else H=at(F,tt[$+4])?3+at(F,tt[$+5]):2;L=S[2]}else H=1,L=S[1];S=A+Ru[b],0>(F=n).b&&et(F);var $,tt=F.b,J=($=F.Ca>>1)-(F.I>>tt)>>31;--F.b,F.Ca+=J,F.Ca|=1,F.I-=($+1&J)<<tt,x[S]=((H^J)-J)*g[(0<b)+0]}return 16}function Ri(n){var s=n.rb[n.sb-1];s.la=0,s.Na=0,o(n.zc,0,0,n.zc.length),n.ja=0}function Do(n,s){if(n==null)return 0;if(s==null)return je(n,2,"NULL VP8Io parameter in VP8Decode().");if(!n.cb&&!Na(n,s))return 0;if(t(n.cb),s.ac==null||s.ac(s)){s.ob&&(n.L=0);var f=Za[n.L];if(n.L==2?(n.yb=0,n.zb=0):(n.yb=s.v-f>>4,n.zb=s.j-f>>4,0>n.yb&&(n.yb=0),0>n.zb&&(n.zb=0)),n.Va=s.o+15+f>>4,n.Hb=s.va+15+f>>4,n.Hb>n.za&&(n.Hb=n.za),n.Va>n.Ub&&(n.Va=n.Ub),0<n.L){var g=n.ed;for(f=0;4>f;++f){var b;if(n.Qa.Cb){var x=n.Qa.Lb[f];n.Qa.Fb||(x+=g.Tb)}else x=g.Tb;for(b=0;1>=b;++b){var A=n.gd[f][b],L=x;if(g.Pc&&(L+=g.vd[0],b&&(L+=g.od[0])),0<(L=0>L?0:63<L?63:L)){var S=L;0<g.wb&&(S=4<g.wb?S>>2:S>>1)>9-g.wb&&(S=9-g.wb),1>S&&(S=1),A.dd=S,A.tc=2*L+S,A.ld=40<=L?2:15<=L?1:0}else A.tc=0;A.La=b}}}f=0}else je(n,6,"Frame setup failed"),f=n.a;if(f=f==0){if(f){n.$c=0,0<n.Aa||(n.Ic=rh);t:{f=n.Ic,g=4*(S=n.za);var F=32*S,H=S+1,$=0<n.L?S*(0<n.Aa?2:1):0,tt=(n.Aa==2?2:1)*S;if((A=g+832+(b=3*(16*f+Za[n.L])/2*F)+(x=n.Fa!=null&&0<n.Fa.length?n.Kc.c*n.Kc.i:0))!=A)f=0;else{if(A>n.Vb){if(n.Vb=0,n.Ec=a(A),n.Fc=0,n.Ec==null){f=je(n,1,"no memory during frame initialization.");break t}n.Vb=A}A=n.Ec,L=n.Fc,n.Ac=A,n.Bc=L,L+=g,n.Gd=l(F,xa),n.Hd=0,n.rb=l(H+1,wa),n.sb=1,n.wa=$?l($,ii):null,n.Y=0,n.D.Nb=0,n.D.wa=n.wa,n.D.Y=n.Y,0<n.Aa&&(n.D.Y+=S),t(!0),n.oc=A,n.pc=L,L+=832,n.ya=l(tt,Ei),n.aa=0,n.D.ya=n.ya,n.D.aa=n.aa,n.Aa==2&&(n.D.aa+=S),n.R=16*S,n.B=8*S,S=(F=Za[n.L])*n.R,F=F/2*n.B,n.sa=A,n.ta=L+S,n.qa=n.sa,n.ra=n.ta+16*f*n.R+F,n.Ha=n.qa,n.Ia=n.ra+8*f*n.B+F,n.$c=0,L+=b,n.mb=x?A:null,n.nb=x?L:null,t(L+x<=n.Fc+n.Vb),Ri(n),o(n.Ac,n.Bc,0,g),f=1}}if(f){if(s.ka=0,s.y=n.sa,s.O=n.ta,s.f=n.qa,s.N=n.ra,s.ea=n.Ha,s.Vd=n.Ia,s.fa=n.R,s.Rc=n.B,s.F=null,s.J=0,!Ha){for(f=-255;255>=f;++f)qe[255+f]=0>f?-f:f;for(f=-1020;1020>=f;++f)hn[1020+f]=-128>f?-128:127<f?127:f;for(f=-112;112>=f;++f)sa[112+f]=-16>f?-16:15<f?15:f;for(f=-255;510>=f;++f)Ni[255+f]=0>f?0:255<f?255:f;Ha=1}wi=Mo,un=Bo,ia=Pa,nr=Oo,wr=_a,Ce=Sa,xi=Vi,Ua=zn,aa=Ko,Wn=Gi,Vn=Yo,An=ci,Gn=Ji,Ai=Ma,Jn=Oa,Nn=Kr,oa=on,xr=Jo,Or[0]=Yr,Or[1]=To,Or[2]=zo,Or[3]=Uo,Or[4]=Ca,Or[5]=ui,Or[6]=Fa,Or[7]=Ui,Or[8]=Wo,Or[9]=Ho,Ln[0]=ka,Ln[1]=Ro,Ln[2]=an,Ln[3]=si,Ln[4]=Ke,Ln[5]=qo,Ln[6]=ja,cn[0]=vn,cn[1]=Eo,cn[2]=Vo,cn[3]=Hi,cn[4]=qn,cn[5]=Go,cn[6]=Wi,f=1}else f=0}f&&(f=function(J,vt){for(J.M=0;J.M<J.Va;++J.M){var lt,W=J.Jc[J.M&J.Xb],G=J.m,gt=J;for(lt=0;lt<gt.za;++lt){var bt=G,mt=gt,Bt=mt.Ac,St=mt.Bc+4*lt,qt=mt.zc,_t=mt.ya[mt.aa+lt];if(mt.Qa.Bb?_t.$b=at(bt,mt.Pa.jb[0])?2+at(bt,mt.Pa.jb[2]):at(bt,mt.Pa.jb[1]):_t.$b=0,mt.kc&&(_t.Ad=at(bt,mt.Bd)),_t.Za=!at(bt,145)+0,_t.Za){var le=_t.Ob,ce=0;for(mt=0;4>mt;++mt){var oe,ne=qt[0+mt];for(oe=0;4>oe;++oe){ne=zu[Bt[St+oe]][ne];for(var ve=Qs[at(bt,ne[0])];0<ve;)ve=Qs[2*ve+at(bt,ne[ve])];ne=-ve,Bt[St+oe]=ne}i(le,ce,Bt,St,4),ce+=4,qt[0+mt]=ne}}else ne=at(bt,156)?at(bt,128)?1:3:at(bt,163)?2:0,_t.Ob[0]=ne,o(Bt,St,ne,4),o(qt,0,ne,4);_t.Dd=at(bt,142)?at(bt,114)?at(bt,183)?1:3:2:0}if(gt.m.Ka)return je(J,7,"Premature end-of-partition0 encountered.");for(;J.ja<J.za;++J.ja){if(gt=W,bt=(G=J).rb[G.sb-1],Bt=G.rb[G.sb+G.ja],lt=G.ya[G.aa+G.ja],St=G.kc?lt.Ad:0)bt.la=Bt.la=0,lt.Za||(bt.Na=Bt.Na=0),lt.Hc=0,lt.Gc=0,lt.ia=0;else{var ge,$t;if(bt=Bt,Bt=gt,St=G.Pa.Xc,qt=G.ya[G.aa+G.ja],_t=G.pb[qt.$b],mt=qt.ad,le=0,ce=G.rb[G.sb-1],ne=oe=0,o(mt,le,0,384),qt.Za)var Te=0,ur=St[3];else{ve=a(16);var Ee=bt.Na+ce.Na;if(Ee=la(Bt,St[1],Ee,_t.Eb,0,ve,0),bt.Na=ce.Na=(0<Ee)+0,1<Ee)wi(ve,0,mt,le);else{var hr=ve[0]+3>>3;for(ve=0;256>ve;ve+=16)mt[le+ve]=hr}Te=1,ur=St[0]}var fe=15&bt.la,gr=15&ce.la;for(ve=0;4>ve;++ve){var mr=1&gr;for(hr=$t=0;4>hr;++hr)fe=fe>>1|(mr=(Ee=la(Bt,ur,Ee=mr+(1&fe),_t.Sc,Te,mt,le))>Te)<<7,$t=$t<<2|(3<Ee?3:1<Ee?2:mt[le+0]!=0),le+=16;fe>>=4,gr=gr>>1|mr<<7,oe=(oe<<8|$t)>>>0}for(ur=fe,Te=gr>>4,ge=0;4>ge;ge+=2){for($t=0,fe=bt.la>>4+ge,gr=ce.la>>4+ge,ve=0;2>ve;++ve){for(mr=1&gr,hr=0;2>hr;++hr)Ee=mr+(1&fe),fe=fe>>1|(mr=0<(Ee=la(Bt,St[2],Ee,_t.Qc,0,mt,le)))<<3,$t=$t<<2|(3<Ee?3:1<Ee?2:mt[le+0]!=0),le+=16;fe>>=2,gr=gr>>1|mr<<5}ne|=$t<<4*ge,ur|=fe<<4<<ge,Te|=(240&gr)<<ge}bt.la=ur,ce.la=Te,qt.Hc=oe,qt.Gc=ne,qt.ia=43690&ne?0:_t.ia,St=!(oe|ne)}if(0<G.L&&(G.wa[G.Y+G.ja]=G.gd[lt.$b][lt.Za],G.wa[G.Y+G.ja].La|=!St),gt.Ka)return je(J,7,"Premature end-of-file encountered.")}if(Ri(J),G=vt,gt=1,lt=(W=J).D,bt=0<W.L&&W.M>=W.zb&&W.M<=W.Va,W.Aa==0)t:{if(lt.M=W.M,lt.uc=bt,$i(W,lt),gt=1,lt=($t=W.D).Nb,bt=(ne=Za[W.L])*W.R,Bt=ne/2*W.B,ve=16*lt*W.R,hr=8*lt*W.B,St=W.sa,qt=W.ta-bt+ve,_t=W.qa,mt=W.ra-Bt+hr,le=W.Ha,ce=W.Ia-Bt+hr,gr=(fe=$t.M)==0,oe=fe>=W.Va-1,W.Aa==2&&$i(W,$t),$t.uc)for(mr=(Ee=W).D.M,t(Ee.D.uc),$t=Ee.yb;$t<Ee.Hb;++$t){Te=$t,ur=mr;var Ue=(Je=(_e=Ee).D).Nb;ge=_e.R;var Je=Je.wa[Je.Y+Te],ir=_e.sa,Sr=_e.ta+16*Ue*ge+16*Te,Qr=Je.dd,He=Je.tc;if(He!=0)if(t(3<=He),_e.L==1)0<Te&&Nn(ir,Sr,ge,He+4),Je.La&&xr(ir,Sr,ge,He),0<ur&&Jn(ir,Sr,ge,He+4),Je.La&&oa(ir,Sr,ge,He);else{var fn=_e.B,Yn=_e.qa,Li=_e.ra+8*Ue*fn+8*Te,Pn=_e.Ha,_e=_e.Ia+8*Ue*fn+8*Te;Ue=Je.ld,0<Te&&(Ua(ir,Sr,ge,He+4,Qr,Ue),Wn(Yn,Li,Pn,_e,fn,He+4,Qr,Ue)),Je.La&&(An(ir,Sr,ge,He,Qr,Ue),Ai(Yn,Li,Pn,_e,fn,He,Qr,Ue)),0<ur&&(xi(ir,Sr,ge,He+4,Qr,Ue),aa(Yn,Li,Pn,_e,fn,He+4,Qr,Ue)),Je.La&&(Vn(ir,Sr,ge,He,Qr,Ue),Gn(Yn,Li,Pn,_e,fn,He,Qr,Ue))}}if(W.ia&&alert("todo:DitherRow"),G.put!=null){if($t=16*fe,fe=16*(fe+1),gr?(G.y=W.sa,G.O=W.ta+ve,G.f=W.qa,G.N=W.ra+hr,G.ea=W.Ha,G.W=W.Ia+hr):($t-=ne,G.y=St,G.O=qt,G.f=_t,G.N=mt,G.ea=le,G.W=ce),oe||(fe-=ne),fe>G.o&&(fe=G.o),G.F=null,G.J=null,W.Fa!=null&&0<W.Fa.length&&$t<fe&&(G.J=Qi(W,G,$t,fe-$t),G.F=W.mb,G.F==null&&G.F.length==0)){gt=je(W,3,"Could not decode alpha data.");break t}$t<G.j&&(ne=G.j-$t,$t=G.j,t(!(1&ne)),G.O+=W.R*ne,G.N+=W.B*(ne>>1),G.W+=W.B*(ne>>1),G.F!=null&&(G.J+=G.width*ne)),$t<fe&&(G.O+=G.v,G.N+=G.v>>1,G.W+=G.v>>1,G.F!=null&&(G.J+=G.v),G.ka=$t-G.j,G.U=G.va-G.v,G.T=fe-$t,gt=G.put(G))}lt+1!=W.Ic||oe||(i(W.sa,W.ta-bt,St,qt+16*W.R,bt),i(W.qa,W.ra-Bt,_t,mt+8*W.B,Bt),i(W.Ha,W.Ia-Bt,le,ce+8*W.B,Bt))}if(!gt)return je(J,6,"Output aborted.")}return 1}(n,s)),s.bc!=null&&s.bc(s),f&=1}return f?(n.cb=0,f):0}function Ir(n,s,f,g,b){b=n[s+f+32*g]+(b>>3),n[s+f+32*g]=-256&b?0>b?0:255:b}function oi(n,s,f,g,b,x){Ir(n,s,0,f,g+b),Ir(n,s,1,f,g+x),Ir(n,s,2,f,g-x),Ir(n,s,3,f,g-b)}function or(n){return(20091*n>>16)+n}function qi(n,s,f,g){var b,x=0,A=a(16);for(b=0;4>b;++b){var L=n[s+0]+n[s+8],S=n[s+0]-n[s+8],F=(35468*n[s+4]>>16)-or(n[s+12]),H=or(n[s+4])+(35468*n[s+12]>>16);A[x+0]=L+H,A[x+1]=S+F,A[x+2]=S-F,A[x+3]=L-H,x+=4,s++}for(b=x=0;4>b;++b)L=(n=A[x+0]+4)+A[x+8],S=n-A[x+8],F=(35468*A[x+4]>>16)-or(A[x+12]),Ir(f,g,0,0,L+(H=or(A[x+4])+(35468*A[x+12]>>16))),Ir(f,g,1,0,S+F),Ir(f,g,2,0,S-F),Ir(f,g,3,0,L-H),x++,g+=32}function Sa(n,s,f,g){var b=n[s+0]+4,x=35468*n[s+4]>>16,A=or(n[s+4]),L=35468*n[s+1]>>16;oi(f,g,0,b+A,n=or(n[s+1]),L),oi(f,g,1,b+x,n,L),oi(f,g,2,b-x,n,L),oi(f,g,3,b-A,n,L)}function Bo(n,s,f,g,b){qi(n,s,f,g),b&&qi(n,s+16,f,g+4)}function Pa(n,s,f,g){un(n,s+0,f,g,1),un(n,s+32,f,g+128,1)}function Oo(n,s,f,g){var b;for(n=n[s+0]+4,b=0;4>b;++b)for(s=0;4>s;++s)Ir(f,g,s,b,n)}function _a(n,s,f,g){n[s+0]&&nr(n,s+0,f,g),n[s+16]&&nr(n,s+16,f,g+4),n[s+32]&&nr(n,s+32,f,g+128),n[s+48]&&nr(n,s+48,f,g+128+4)}function Mo(n,s,f,g){var b,x=a(16);for(b=0;4>b;++b){var A=n[s+0+b]+n[s+12+b],L=n[s+4+b]+n[s+8+b],S=n[s+4+b]-n[s+8+b],F=n[s+0+b]-n[s+12+b];x[0+b]=A+L,x[8+b]=A-L,x[4+b]=F+S,x[12+b]=F-S}for(b=0;4>b;++b)A=(n=x[0+4*b]+3)+x[3+4*b],L=x[1+4*b]+x[2+4*b],S=x[1+4*b]-x[2+4*b],F=n-x[3+4*b],f[g+0]=A+L>>3,f[g+16]=F+S>>3,f[g+32]=A-L>>3,f[g+48]=F-S>>3,g+=64}function zi(n,s,f){var g,b=s-32,x=pr,A=255-n[b-1];for(g=0;g<f;++g){var L,S=x,F=A+n[s-1];for(L=0;L<f;++L)n[s+L]=S[F+n[b+L]];s+=32}}function To(n,s){zi(n,s,4)}function Eo(n,s){zi(n,s,8)}function Ro(n,s){zi(n,s,16)}function an(n,s){var f;for(f=0;16>f;++f)i(n,s+32*f,n,s-32,16)}function si(n,s){var f;for(f=16;0<f;--f)o(n,s,n[s-1],16),s+=32}function li(n,s,f){var g;for(g=0;16>g;++g)o(s,f+32*g,n,16)}function ka(n,s){var f,g=16;for(f=0;16>f;++f)g+=n[s-1+32*f]+n[s+f-32];li(g>>5,n,s)}function Ke(n,s){var f,g=8;for(f=0;16>f;++f)g+=n[s-1+32*f];li(g>>4,n,s)}function qo(n,s){var f,g=8;for(f=0;16>f;++f)g+=n[s+f-32];li(g>>4,n,s)}function ja(n,s){li(128,n,s)}function Gt(n,s,f){return n+2*s+f+2>>2}function zo(n,s){var f,g=s-32;for(g=new Uint8Array([Gt(n[g-1],n[g+0],n[g+1]),Gt(n[g+0],n[g+1],n[g+2]),Gt(n[g+1],n[g+2],n[g+3]),Gt(n[g+2],n[g+3],n[g+4])]),f=0;4>f;++f)i(n,s+32*f,g,0,g.length)}function Uo(n,s){var f=n[s-1],g=n[s-1+32],b=n[s-1+64],x=n[s-1+96];Nt(n,s+0,16843009*Gt(n[s-1-32],f,g)),Nt(n,s+32,16843009*Gt(f,g,b)),Nt(n,s+64,16843009*Gt(g,b,x)),Nt(n,s+96,16843009*Gt(b,x,x))}function Yr(n,s){var f,g=4;for(f=0;4>f;++f)g+=n[s+f-32]+n[s-1+32*f];for(g>>=3,f=0;4>f;++f)o(n,s+32*f,g,4)}function Ca(n,s){var f=n[s-1+0],g=n[s-1+32],b=n[s-1+64],x=n[s-1-32],A=n[s+0-32],L=n[s+1-32],S=n[s+2-32],F=n[s+3-32];n[s+0+96]=Gt(g,b,n[s-1+96]),n[s+1+96]=n[s+0+64]=Gt(f,g,b),n[s+2+96]=n[s+1+64]=n[s+0+32]=Gt(x,f,g),n[s+3+96]=n[s+2+64]=n[s+1+32]=n[s+0+0]=Gt(A,x,f),n[s+3+64]=n[s+2+32]=n[s+1+0]=Gt(L,A,x),n[s+3+32]=n[s+2+0]=Gt(S,L,A),n[s+3+0]=Gt(F,S,L)}function Fa(n,s){var f=n[s+1-32],g=n[s+2-32],b=n[s+3-32],x=n[s+4-32],A=n[s+5-32],L=n[s+6-32],S=n[s+7-32];n[s+0+0]=Gt(n[s+0-32],f,g),n[s+1+0]=n[s+0+32]=Gt(f,g,b),n[s+2+0]=n[s+1+32]=n[s+0+64]=Gt(g,b,x),n[s+3+0]=n[s+2+32]=n[s+1+64]=n[s+0+96]=Gt(b,x,A),n[s+3+32]=n[s+2+64]=n[s+1+96]=Gt(x,A,L),n[s+3+64]=n[s+2+96]=Gt(A,L,S),n[s+3+96]=Gt(L,S,S)}function ui(n,s){var f=n[s-1+0],g=n[s-1+32],b=n[s-1+64],x=n[s-1-32],A=n[s+0-32],L=n[s+1-32],S=n[s+2-32],F=n[s+3-32];n[s+0+0]=n[s+1+64]=x+A+1>>1,n[s+1+0]=n[s+2+64]=A+L+1>>1,n[s+2+0]=n[s+3+64]=L+S+1>>1,n[s+3+0]=S+F+1>>1,n[s+0+96]=Gt(b,g,f),n[s+0+64]=Gt(g,f,x),n[s+0+32]=n[s+1+96]=Gt(f,x,A),n[s+1+32]=n[s+2+96]=Gt(x,A,L),n[s+2+32]=n[s+3+96]=Gt(A,L,S),n[s+3+32]=Gt(L,S,F)}function Ui(n,s){var f=n[s+0-32],g=n[s+1-32],b=n[s+2-32],x=n[s+3-32],A=n[s+4-32],L=n[s+5-32],S=n[s+6-32],F=n[s+7-32];n[s+0+0]=f+g+1>>1,n[s+1+0]=n[s+0+64]=g+b+1>>1,n[s+2+0]=n[s+1+64]=b+x+1>>1,n[s+3+0]=n[s+2+64]=x+A+1>>1,n[s+0+32]=Gt(f,g,b),n[s+1+32]=n[s+0+96]=Gt(g,b,x),n[s+2+32]=n[s+1+96]=Gt(b,x,A),n[s+3+32]=n[s+2+96]=Gt(x,A,L),n[s+3+64]=Gt(A,L,S),n[s+3+96]=Gt(L,S,F)}function Ho(n,s){var f=n[s-1+0],g=n[s-1+32],b=n[s-1+64],x=n[s-1+96];n[s+0+0]=f+g+1>>1,n[s+2+0]=n[s+0+32]=g+b+1>>1,n[s+2+32]=n[s+0+64]=b+x+1>>1,n[s+1+0]=Gt(f,g,b),n[s+3+0]=n[s+1+32]=Gt(g,b,x),n[s+3+32]=n[s+1+64]=Gt(b,x,x),n[s+3+64]=n[s+2+64]=n[s+0+96]=n[s+1+96]=n[s+2+96]=n[s+3+96]=x}function Wo(n,s){var f=n[s-1+0],g=n[s-1+32],b=n[s-1+64],x=n[s-1+96],A=n[s-1-32],L=n[s+0-32],S=n[s+1-32],F=n[s+2-32];n[s+0+0]=n[s+2+32]=f+A+1>>1,n[s+0+32]=n[s+2+64]=g+f+1>>1,n[s+0+64]=n[s+2+96]=b+g+1>>1,n[s+0+96]=x+b+1>>1,n[s+3+0]=Gt(L,S,F),n[s+2+0]=Gt(A,L,S),n[s+1+0]=n[s+3+32]=Gt(f,A,L),n[s+1+32]=n[s+3+64]=Gt(g,f,A),n[s+1+64]=n[s+3+96]=Gt(b,g,f),n[s+1+96]=Gt(x,b,g)}function Vo(n,s){var f;for(f=0;8>f;++f)i(n,s+32*f,n,s-32,8)}function Hi(n,s){var f;for(f=0;8>f;++f)o(n,s,n[s-1],8),s+=32}function Rn(n,s,f){var g;for(g=0;8>g;++g)o(s,f+32*g,n,8)}function vn(n,s){var f,g=8;for(f=0;8>f;++f)g+=n[s+f-32]+n[s-1+32*f];Rn(g>>4,n,s)}function Go(n,s){var f,g=4;for(f=0;8>f;++f)g+=n[s+f-32];Rn(g>>3,n,s)}function qn(n,s){var f,g=4;for(f=0;8>f;++f)g+=n[s-1+32*f];Rn(g>>3,n,s)}function Wi(n,s){Rn(128,n,s)}function hi(n,s,f){var g=n[s-f],b=n[s+0],x=3*(b-g)+Zo[1020+n[s-2*f]-n[s+f]],A=Wa[112+(x+4>>3)];n[s-f]=pr[255+g+Wa[112+(x+3>>3)]],n[s+0]=pr[255+b-A]}function Ia(n,s,f,g){var b=n[s+0],x=n[s+f];return Nr[255+n[s-2*f]-n[s-f]]>g||Nr[255+x-b]>g}function Da(n,s,f,g){return 4*Nr[255+n[s-f]-n[s+0]]+Nr[255+n[s-2*f]-n[s+f]]<=g}function Ba(n,s,f,g,b){var x=n[s-3*f],A=n[s-2*f],L=n[s-f],S=n[s+0],F=n[s+f],H=n[s+2*f],$=n[s+3*f];return 4*Nr[255+L-S]+Nr[255+A-F]>g?0:Nr[255+n[s-4*f]-x]<=b&&Nr[255+x-A]<=b&&Nr[255+A-L]<=b&&Nr[255+$-H]<=b&&Nr[255+H-F]<=b&&Nr[255+F-S]<=b}function Oa(n,s,f,g){var b=2*g+1;for(g=0;16>g;++g)Da(n,s+g,f,b)&&hi(n,s+g,f)}function Kr(n,s,f,g){var b=2*g+1;for(g=0;16>g;++g)Da(n,s+g*f,1,b)&&hi(n,s+g*f,1)}function on(n,s,f,g){var b;for(b=3;0<b;--b)Oa(n,s+=4*f,f,g)}function Jo(n,s,f,g){var b;for(b=3;0<b;--b)Kr(n,s+=4,f,g)}function bn(n,s,f,g,b,x,A,L){for(x=2*x+1;0<b--;){if(Ba(n,s,f,x,A))if(Ia(n,s,f,L))hi(n,s,f);else{var S=n,F=s,H=f,$=S[F-2*H],tt=S[F-H],J=S[F+0],vt=S[F+H],lt=S[F+2*H],W=27*(gt=Zo[1020+3*(J-tt)+Zo[1020+$-vt]])+63>>7,G=18*gt+63>>7,gt=9*gt+63>>7;S[F-3*H]=pr[255+S[F-3*H]+gt],S[F-2*H]=pr[255+$+G],S[F-H]=pr[255+tt+W],S[F+0]=pr[255+J-W],S[F+H]=pr[255+vt-G],S[F+2*H]=pr[255+lt-gt]}s+=g}}function Dr(n,s,f,g,b,x,A,L){for(x=2*x+1;0<b--;){if(Ba(n,s,f,x,A))if(Ia(n,s,f,L))hi(n,s,f);else{var S=n,F=s,H=f,$=S[F-H],tt=S[F+0],J=S[F+H],vt=Wa[112+((lt=3*(tt-$))+4>>3)],lt=Wa[112+(lt+3>>3)],W=vt+1>>1;S[F-2*H]=pr[255+S[F-2*H]+W],S[F-H]=pr[255+$+lt],S[F+0]=pr[255+tt-vt],S[F+H]=pr[255+J-W]}s+=g}}function Vi(n,s,f,g,b,x){bn(n,s,f,1,16,g,b,x)}function zn(n,s,f,g,b,x){bn(n,s,1,f,16,g,b,x)}function Yo(n,s,f,g,b,x){var A;for(A=3;0<A;--A)Dr(n,s+=4*f,f,1,16,g,b,x)}function ci(n,s,f,g,b,x){var A;for(A=3;0<A;--A)Dr(n,s+=4,1,f,16,g,b,x)}function Ko(n,s,f,g,b,x,A,L){bn(n,s,b,1,8,x,A,L),bn(f,g,b,1,8,x,A,L)}function Gi(n,s,f,g,b,x,A,L){bn(n,s,1,b,8,x,A,L),bn(f,g,1,b,8,x,A,L)}function Ji(n,s,f,g,b,x,A,L){Dr(n,s+4*b,b,1,8,x,A,L),Dr(f,g+4*b,b,1,8,x,A,L)}function Ma(n,s,f,g,b,x,A,L){Dr(n,s+4,1,b,8,x,A,L),Dr(f,g+4,1,b,8,x,A,L)}function fi(){this.ba=new _r,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new Se,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Yi(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function Ki(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function Ta(){this.ua=0,this.Wa=new T,this.vb=new T,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new R,this.yc=new j}function Xo(){this.xb=this.a=0,this.l=new ai,this.ca=new _r,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new N,this.Pb=0,this.wd=new N,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new Ta,this.ab=0,this.gc=l(4,Ki),this.Oc=0}function di(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new ai,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function Un(n,s,f,g,b,x,A){for(n=n==null?0:n[s+0],s=0;s<A;++s)b[x+s]=n+f[g+s]&255,n=b[x+s]}function Xi(n,s,f,g,b,x,A){var L;if(n==null)Un(null,null,f,g,b,x,A);else for(L=0;L<A;++L)b[x+L]=n[s+L]+f[g+L]&255}function yn(n,s,f,g,b,x,A){if(n==null)Un(null,null,f,g,b,x,A);else{var L,S=n[s+0],F=S,H=S;for(L=0;L<A;++L)F=H+(S=n[s+L])-F,H=f[g+L]+(-256&F?0>F?0:255:F)&255,F=S,b[x+L]=H}}function Qi(n,s,f,g){var b=s.width,x=s.o;if(t(n!=null&&s!=null),0>f||0>=g||f+g>x)return null;if(!n.Cc){if(n.ga==null){var A;if(n.ga=new di,(A=n.ga==null)||(A=s.width*s.o,t(n.Gb.length==0),n.Gb=a(A),n.Uc=0,n.Gb==null?A=0:(n.mb=n.Gb,n.nb=n.Uc,n.rc=null,A=1),A=!A),!A){A=n.ga;var L=n.Fa,S=n.P,F=n.qc,H=n.mb,$=n.nb,tt=S+1,J=F-1,vt=A.l;if(t(L!=null&&H!=null&&s!=null),Sn[0]=null,Sn[1]=Un,Sn[2]=Xi,Sn[3]=yn,A.ca=H,A.tb=$,A.c=s.width,A.i=s.height,t(0<A.c&&0<A.i),1>=F)s=0;else if(A.$a=L[S+0]>>0&3,A.Z=L[S+0]>>2&3,A.Lc=L[S+0]>>4&3,S=L[S+0]>>6&3,0>A.$a||1<A.$a||4<=A.Z||1<A.Lc||S)s=0;else if(vt.put=Hr,vt.ac=Ve,vt.bc=Wr,vt.ma=A,vt.width=s.width,vt.height=s.height,vt.Da=s.Da,vt.v=s.v,vt.va=s.va,vt.j=s.j,vt.o=s.o,A.$a)t:{t(A.$a==1),s=br();e:for(;;){if(s==null){s=0;break t}if(t(A!=null),A.mc=s,s.c=A.c,s.i=A.i,s.l=A.l,s.l.ma=A,s.l.width=A.c,s.l.height=A.i,s.a=0,Z(s.m,L,tt,J),!Jr(A.c,A.i,1,s,null)||(s.ab==1&&s.gc[0].hc==3&&nn(s.s)?(A.ic=1,L=s.c*s.i,s.Ta=null,s.Ua=0,s.V=a(L),s.Ba=0,s.V==null?(s.a=1,s=0):s=1):(A.ic=0,s=En(s,A.c)),!s))break e;s=1;break t}A.mc=null,s=0}else s=J>=A.c*A.i;A=!s}if(A)return null;n.ga.Lc!=1?n.Ga=0:g=x-f}t(n.ga!=null),t(f+g<=x);t:{if(s=(L=n.ga).c,x=L.l.o,L.$a==0){if(tt=n.rc,J=n.Vc,vt=n.Fa,S=n.P+1+f*s,F=n.mb,H=n.nb+f*s,t(S<=n.P+n.qc),L.Z!=0)for(t(Sn[L.Z]!=null),A=0;A<g;++A)Sn[L.Z](tt,J,vt,S,F,H,s),tt=F,J=H,H+=s,S+=s;else for(A=0;A<g;++A)i(F,H,vt,S,s),tt=F,J=H,H+=s,S+=s;n.rc=tt,n.Vc=J}else{if(t(L.mc!=null),s=f+g,t((A=L.mc)!=null),t(s<=A.i),A.C>=s)s=1;else if(L.ic||X(),L.ic){L=A.V,tt=A.Ba,J=A.c;var lt=A.i,W=(vt=1,S=A.$/J,F=A.$%J,H=A.m,$=A.s,A.$),G=J*lt,gt=J*s,bt=$.wc,mt=W<gt?Oe($,F,S):null;t(W<=G),t(s<=lt),t(nn($));e:for(;;){for(;!H.h&&W<gt;){if(F&bt||(mt=Oe($,F,S)),t(mt!=null),Y(H),256>(lt=ar(mt.G[0],mt.H[0],H)))L[tt+W]=lt,++W,++F>=J&&(F=0,++S<=s&&!(S%16)&&Cr(A,S));else{if(!(280>lt)){vt=0;break e}lt=kr(lt-256,H);var Bt,St=ar(mt.G[4],mt.H[4],H);if(Y(H),!(W>=(St=Vr(J,St=kr(St,H)))&&G-W>=lt)){vt=0;break e}for(Bt=0;Bt<lt;++Bt)L[tt+W+Bt]=L[tt+W+Bt-St];for(W+=lt,F+=lt;F>=J;)F-=J,++S<=s&&!(S%16)&&Cr(A,S);W<gt&&F&bt&&(mt=Oe($,F,S))}t(H.h==M(H))}Cr(A,S>s?s:S);break e}!vt||H.h&&W<G?(vt=0,A.a=H.h?5:3):A.$=W,s=vt}else s=Fr(A,A.V,A.Ba,A.c,A.i,s,ti);if(!s){g=0;break t}}f+g>=x&&(n.Cc=1),g=1}if(!g)return null;if(n.Cc&&((g=n.ga)!=null&&(g.mc=null),n.ga=null,0<n.Ga))return alert("todo:WebPDequantizeLevels"),null}return n.nb+f*b}function u(n,s,f,g,b,x){for(;0<b--;){var A,L=n,S=s+(f?1:0),F=n,H=s+(f?0:3);for(A=0;A<g;++A){var $=F[H+4*A];$!=255&&($*=32897,L[S+4*A+0]=L[S+4*A+0]*$>>23,L[S+4*A+1]=L[S+4*A+1]*$>>23,L[S+4*A+2]=L[S+4*A+2]*$>>23)}s+=x}}function y(n,s,f,g,b){for(;0<g--;){var x;for(x=0;x<f;++x){var A=n[s+2*x+0],L=15&(F=n[s+2*x+1]),S=4369*L,F=(240&F|F>>4)*S>>16;n[s+2*x+0]=(240&A|A>>4)*S>>16&240|(15&A|A<<4)*S>>16>>4&15,n[s+2*x+1]=240&F|L}s+=b}}function D(n,s,f,g,b,x,A,L){var S,F,H=255;for(F=0;F<b;++F){for(S=0;S<g;++S){var $=n[s+S];x[A+4*S]=$,H&=$}s+=f,A+=L}return H!=255}function z(n,s,f,g,b){var x;for(x=0;x<b;++x)f[g+x]=n[s+x]>>8}function X(){Ar=u,ye=y,we=D,Fe=z}function ct(n,s,f){q[n]=function(g,b,x,A,L,S,F,H,$,tt,J,vt,lt,W,G,gt,bt){var mt,Bt=bt-1>>1,St=L[S+0]|F[H+0]<<16,qt=$[tt+0]|J[vt+0]<<16;t(g!=null);var _t=3*St+qt+131074>>2;for(s(g[b+0],255&_t,_t>>16,lt,W),x!=null&&(_t=3*qt+St+131074>>2,s(x[A+0],255&_t,_t>>16,G,gt)),mt=1;mt<=Bt;++mt){var le=L[S+mt]|F[H+mt]<<16,ce=$[tt+mt]|J[vt+mt]<<16,oe=St+le+qt+ce+524296,ne=oe+2*(le+qt)>>3;_t=ne+St>>1,St=(oe=oe+2*(St+ce)>>3)+le>>1,s(g[b+2*mt-1],255&_t,_t>>16,lt,W+(2*mt-1)*f),s(g[b+2*mt-0],255&St,St>>16,lt,W+(2*mt-0)*f),x!=null&&(_t=oe+qt>>1,St=ne+ce>>1,s(x[A+2*mt-1],255&_t,_t>>16,G,gt+(2*mt-1)*f),s(x[A+2*mt+0],255&St,St>>16,G,gt+(2*mt+0)*f)),St=le,qt=ce}1&bt||(_t=3*St+qt+131074>>2,s(g[b+bt-1],255&_t,_t>>16,lt,W+(bt-1)*f),x!=null&&(_t=3*qt+St+131074>>2,s(x[A+bt-1],255&_t,_t>>16,G,gt+(bt-1)*f)))}}function wt(){Lr[Va]=Vu,Lr[Ga]=$s,Lr[Ys]=Gu,Lr[Ja]=tl,Lr[Ya]=el,Lr[$o]=rl,Lr[Ks]=Ju,Lr[ts]=$s,Lr[es]=tl,Lr[Ka]=el,Lr[rs]=rl}function Dt(n){return n&-16384?0>n?0:255:n>>Yu}function Rt(n,s){return Dt((19077*n>>8)+(26149*s>>8)-14234)}function Zt(n,s,f){return Dt((19077*n>>8)-(6419*s>>8)-(13320*f>>8)+8708)}function Kt(n,s){return Dt((19077*n>>8)+(33050*s>>8)-17685)}function ie(n,s,f,g,b){g[b+0]=Rt(n,f),g[b+1]=Zt(n,s,f),g[b+2]=Kt(n,s)}function Ne(n,s,f,g,b){g[b+0]=Kt(n,s),g[b+1]=Zt(n,s,f),g[b+2]=Rt(n,f)}function Pe(n,s,f,g,b){var x=Zt(n,s,f);s=x<<3&224|Kt(n,s)>>3,g[b+0]=248&Rt(n,f)|x>>5,g[b+1]=s}function Me(n,s,f,g,b){var x=240&Kt(n,s)|15;g[b+0]=240&Rt(n,f)|Zt(n,s,f)>>4,g[b+1]=x}function Xe(n,s,f,g,b){g[b+0]=255,ie(n,s,f,g,b+1)}function ze(n,s,f,g,b){Ne(n,s,f,g,b),g[b+3]=255}function Br(n,s,f,g,b){ie(n,s,f,g,b),g[b+3]=255}function dr(n,s){return 0>n?0:n>s?s:n}function Xr(n,s,f){q[n]=function(g,b,x,A,L,S,F,H,$){for(var tt=H+(-2&$)*f;H!=tt;)s(g[b+0],x[A+0],L[S+0],F,H),s(g[b+1],x[A+0],L[S+0],F,H+f),b+=2,++A,++S,H+=2*f;1&$&&s(g[b+0],x[A+0],L[S+0],F,H)}}function Ea(n,s,f){return f==0?n==0?s==0?6:5:s==0?4:0:f}function Zi(n,s,f,g,b){switch(n>>>30){case 3:un(s,f,g,b,0);break;case 2:Ce(s,f,g,b);break;case 1:nr(s,f,g,b)}}function $i(n,s){var f,g,b=s.M,x=s.Nb,A=n.oc,L=n.pc+40,S=n.oc,F=n.pc+584,H=n.oc,$=n.pc+600;for(f=0;16>f;++f)A[L+32*f-1]=129;for(f=0;8>f;++f)S[F+32*f-1]=129,H[$+32*f-1]=129;for(0<b?A[L-1-32]=S[F-1-32]=H[$-1-32]=129:(o(A,L-32-1,127,21),o(S,F-32-1,127,9),o(H,$-32-1,127,9)),g=0;g<n.za;++g){var tt=s.ya[s.aa+g];if(0<g){for(f=-1;16>f;++f)i(A,L+32*f-4,A,L+32*f+12,4);for(f=-1;8>f;++f)i(S,F+32*f-4,S,F+32*f+4,4),i(H,$+32*f-4,H,$+32*f+4,4)}var J=n.Gd,vt=n.Hd+g,lt=tt.ad,W=tt.Hc;if(0<b&&(i(A,L-32,J[vt].y,0,16),i(S,F-32,J[vt].f,0,8),i(H,$-32,J[vt].ea,0,8)),tt.Za){var G=A,gt=L-32+16;for(0<b&&(g>=n.za-1?o(G,gt,J[vt].y[15],4):i(G,gt,J[vt+1].y,0,4)),f=0;4>f;f++)G[gt+128+f]=G[gt+256+f]=G[gt+384+f]=G[gt+0+f];for(f=0;16>f;++f,W<<=2)G=A,gt=L+il[f],Or[tt.Ob[f]](G,gt),Zi(W,lt,16*+f,G,gt)}else if(G=Ea(g,b,tt.Ob[0]),Ln[G](A,L),W!=0)for(f=0;16>f;++f,W<<=2)Zi(W,lt,16*+f,A,L+il[f]);for(f=tt.Gc,G=Ea(g,b,tt.Dd),cn[G](S,F),cn[G](H,$),W=lt,G=S,gt=F,255&(tt=f>>0)&&(170&tt?ia(W,256,G,gt):wr(W,256,G,gt)),tt=H,W=$,255&(f>>=8)&&(170&f?ia(lt,320,tt,W):wr(lt,320,tt,W)),b<n.Ub-1&&(i(J[vt].y,0,A,L+480,16),i(J[vt].f,0,S,F+224,8),i(J[vt].ea,0,H,$+224,8)),f=8*x*n.B,J=n.sa,vt=n.ta+16*g+16*x*n.R,lt=n.qa,tt=n.ra+8*g+f,W=n.Ha,G=n.Ia+8*g+f,f=0;16>f;++f)i(J,vt+f*n.R,A,L+32*f,16);for(f=0;8>f;++f)i(lt,tt+f*n.B,S,F+32*f,8),i(W,G+f*n.B,H,$+32*f,8)}}function pi(n,s,f,g,b,x,A,L,S){var F=[0],H=[0],$=0,tt=S!=null?S.kd:0,J=S??new Yi;if(n==null||12>f)return 7;J.data=n,J.w=s,J.ha=f,s=[s],f=[f],J.gb=[J.gb];t:{var vt=s,lt=f,W=J.gb;if(t(n!=null),t(lt!=null),t(W!=null),W[0]=0,12<=lt[0]&&!e(n,vt[0],"RIFF")){if(e(n,vt[0]+8,"WEBP")){W=3;break t}var G=Pt(n,vt[0]+4);if(12>G||4294967286<G){W=3;break t}if(tt&&G>lt[0]-8){W=7;break t}W[0]=G,vt[0]+=12,lt[0]-=12}W=0}if(W!=0)return W;for(G=0<J.gb[0],f=f[0];;){t:{var gt=n;lt=s,W=f;var bt=F,mt=H,Bt=vt=[0];if((_t=$=[$])[0]=0,8>W[0])W=7;else{if(!e(gt,lt[0],"VP8X")){if(Pt(gt,lt[0]+4)!=10){W=3;break t}if(18>W[0]){W=7;break t}var St=Pt(gt,lt[0]+8),qt=1+jt(gt,lt[0]+12);if(2147483648<=qt*(gt=1+jt(gt,lt[0]+15))){W=3;break t}Bt!=null&&(Bt[0]=St),bt!=null&&(bt[0]=qt),mt!=null&&(mt[0]=gt),lt[0]+=18,W[0]-=18,_t[0]=1}W=0}}if($=$[0],vt=vt[0],W!=0)return W;if(lt=!!(2&vt),!G&&$)return 3;if(x!=null&&(x[0]=!!(16&vt)),A!=null&&(A[0]=lt),L!=null&&(L[0]=0),A=F[0],vt=H[0],$&&lt&&S==null){W=0;break}if(4>f){W=7;break}if(G&&$||!G&&!$&&!e(n,s[0],"ALPH")){f=[f],J.na=[J.na],J.P=[J.P],J.Sa=[J.Sa];t:{St=n,W=s,G=f;var _t=J.gb;bt=J.na,mt=J.P,Bt=J.Sa,qt=22,t(St!=null),t(G!=null),gt=W[0];var le=G[0];for(t(bt!=null),t(Bt!=null),bt[0]=null,mt[0]=null,Bt[0]=0;;){if(W[0]=gt,G[0]=le,8>le){W=7;break t}var ce=Pt(St,gt+4);if(4294967286<ce){W=3;break t}var oe=8+ce+1&-2;if(qt+=oe,0<_t&&qt>_t){W=3;break t}if(!e(St,gt,"VP8 ")||!e(St,gt,"VP8L")){W=0;break t}if(le[0]<oe){W=7;break t}e(St,gt,"ALPH")||(bt[0]=St,mt[0]=gt+8,Bt[0]=ce),gt+=oe,le-=oe}}if(f=f[0],J.na=J.na[0],J.P=J.P[0],J.Sa=J.Sa[0],W!=0)break}f=[f],J.Ja=[J.Ja],J.xa=[J.xa];t:if(_t=n,W=s,G=f,bt=J.gb[0],mt=J.Ja,Bt=J.xa,St=W[0],gt=!e(_t,St,"VP8 "),qt=!e(_t,St,"VP8L"),t(_t!=null),t(G!=null),t(mt!=null),t(Bt!=null),8>G[0])W=7;else{if(gt||qt){if(_t=Pt(_t,St+4),12<=bt&&_t>bt-12){W=3;break t}if(tt&&_t>G[0]-8){W=7;break t}mt[0]=_t,W[0]+=8,G[0]-=8,Bt[0]=qt}else Bt[0]=5<=G[0]&&_t[St+0]==47&&!(_t[St+4]>>5),mt[0]=G[0];W=0}if(f=f[0],J.Ja=J.Ja[0],J.xa=J.xa[0],s=s[0],W!=0)break;if(4294967286<J.Ja)return 3;if(L==null||lt||(L[0]=J.xa?2:1),A=[A],vt=[vt],J.xa){if(5>f){W=7;break}L=A,tt=vt,lt=x,n==null||5>f?n=0:5<=f&&n[s+0]==47&&!(n[s+4]>>5)?(G=[0],_t=[0],bt=[0],Z(mt=new N,n,s,f),zt(mt,G,_t,bt)?(L!=null&&(L[0]=G[0]),tt!=null&&(tt[0]=_t[0]),lt!=null&&(lt[0]=bt[0]),n=1):n=0):n=0}else{if(10>f){W=7;break}L=vt,n==null||10>f||!Aa(n,s+3,f-3)?n=0:(tt=n[s+0]|n[s+1]<<8|n[s+2]<<16,lt=16383&(n[s+7]<<8|n[s+6]),n=16383&(n[s+9]<<8|n[s+8]),1&tt||3<(tt>>1&7)||!(tt>>4&1)||tt>>5>=J.Ja||!lt||!n?n=0:(A&&(A[0]=lt),L&&(L[0]=n),n=1))}if(!n||(A=A[0],vt=vt[0],$&&(F[0]!=A||H[0]!=vt)))return 3;S!=null&&(S[0]=J,S.offset=s-S.w,t(4294967286>s-S.w),t(S.offset==S.ha-f));break}return W==0||W==7&&$&&S==null?(x!=null&&(x[0]|=J.na!=null&&0<J.na.length),g!=null&&(g[0]=A),b!=null&&(b[0]=vt),0):W}function ta(n,s,f){var g=s.width,b=s.height,x=0,A=0,L=g,S=b;if(s.Da=n!=null&&0<n.Da,s.Da&&(L=n.cd,S=n.bd,x=n.v,A=n.j,11>f||(x&=-2,A&=-2),0>x||0>A||0>=L||0>=S||x+L>g||A+S>b))return 0;if(s.v=x,s.j=A,s.va=x+L,s.o=A+S,s.U=L,s.T=S,s.da=n!=null&&0<n.da,s.da){if(!Qt(L,S,f=[n.ib],x=[n.hb]))return 0;s.ib=f[0],s.hb=x[0]}return s.ob=n!=null&&n.ob,s.Kb=n==null||!n.Sd,s.da&&(s.ob=s.ib<3*g/4&&s.hb<3*b/4,s.Kb=0),1}function ea(n){if(n==null)return 2;if(11>n.S){var s=n.f.RGBA;s.fb+=(n.height-1)*s.A,s.A=-s.A}else s=n.f.kb,n=n.height,s.O+=(n-1)*s.fa,s.fa=-s.fa,s.N+=(n-1>>1)*s.Ab,s.Ab=-s.Ab,s.W+=(n-1>>1)*s.Db,s.Db=-s.Db,s.F!=null&&(s.J+=(n-1)*s.lb,s.lb=-s.lb);return 0}function gi(n,s,f,g){if(g==null||0>=n||0>=s)return 2;if(f!=null){if(f.Da){var b=f.cd,x=f.bd,A=-2&f.v,L=-2&f.j;if(0>A||0>L||0>=b||0>=x||A+b>n||L+x>s)return 2;n=b,s=x}if(f.da){if(!Qt(n,s,b=[f.ib],x=[f.hb]))return 2;n=b[0],s=x[0]}}g.width=n,g.height=s;t:{var S=g.width,F=g.height;if(n=g.S,0>=S||0>=F||!(n>=Va&&13>n))n=2;else{if(0>=g.Rd&&g.sd==null){A=x=b=s=0;var H=(L=S*al[n])*F;if(11>n||(x=(F+1)/2*(s=(S+1)/2),n==12&&(A=(b=S)*F)),(F=a(H+2*x+A))==null){n=1;break t}g.sd=F,11>n?((S=g.f.RGBA).eb=F,S.fb=0,S.A=L,S.size=H):((S=g.f.kb).y=F,S.O=0,S.fa=L,S.Fd=H,S.f=F,S.N=0+H,S.Ab=s,S.Cd=x,S.ea=F,S.W=0+H+x,S.Db=s,S.Ed=x,n==12&&(S.F=F,S.J=0+H+2*x),S.Tc=A,S.lb=b)}if(s=1,b=g.S,x=g.width,A=g.height,b>=Va&&13>b)if(11>b)n=g.f.RGBA,s&=(L=Math.abs(n.A))*(A-1)+x<=n.size,s&=L>=x*al[b],s&=n.eb!=null;else{n=g.f.kb,L=(x+1)/2,H=(A+1)/2,S=Math.abs(n.fa),F=Math.abs(n.Ab);var $=Math.abs(n.Db),tt=Math.abs(n.lb),J=tt*(A-1)+x;s&=S*(A-1)+x<=n.Fd,s&=F*(H-1)+L<=n.Cd,s=(s&=$*(H-1)+L<=n.Ed)&S>=x&F>=L&$>=L,s&=n.y!=null,s&=n.f!=null,s&=n.ea!=null,b==12&&(s&=tt>=x,s&=J<=n.Tc,s&=n.F!=null)}else s=0;n=s?0:2}}return n!=0||f!=null&&f.fd&&(n=ea(g)),n}var Ge=64,mi=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],vi=24,bi=32,ra=8,sr=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];Lt("Predictor0","PredictorAdd0"),q.Predictor0=function(){return **********},q.Predictor1=function(n){return n},q.Predictor2=function(n,s,f){return s[f+0]},q.Predictor3=function(n,s,f){return s[f+1]},q.Predictor4=function(n,s,f){return s[f-1]},q.Predictor5=function(n,s,f){return kt(kt(n,s[f+1]),s[f+0])},q.Predictor6=function(n,s,f){return kt(n,s[f-1])},q.Predictor7=function(n,s,f){return kt(n,s[f+0])},q.Predictor8=function(n,s,f){return kt(s[f-1],s[f+0])},q.Predictor9=function(n,s,f){return kt(s[f+0],s[f+1])},q.Predictor10=function(n,s,f){return kt(kt(n,s[f-1]),kt(s[f+0],s[f+1]))},q.Predictor11=function(n,s,f){var g=s[f+0];return 0>=te(g>>24&255,n>>24&255,(s=s[f-1])>>24&255)+te(g>>16&255,n>>16&255,s>>16&255)+te(g>>8&255,n>>8&255,s>>8&255)+te(255&g,255&n,255&s)?g:n},q.Predictor12=function(n,s,f){var g=s[f+0];return(Et((n>>24&255)+(g>>24&255)-((s=s[f-1])>>24&255))<<24|Et((n>>16&255)+(g>>16&255)-(s>>16&255))<<16|Et((n>>8&255)+(g>>8&255)-(s>>8&255))<<8|Et((255&n)+(255&g)-(255&s)))>>>0},q.Predictor13=function(n,s,f){var g=s[f-1];return(Jt((n=kt(n,s[f+0]))>>24&255,g>>24&255)<<24|Jt(n>>16&255,g>>16&255)<<16|Jt(n>>8&255,g>>8&255)<<8|Jt(n>>0&255,g>>0&255))>>>0};var Qo=q.PredictorAdd0;q.PredictorAdd1=ee,Lt("Predictor2","PredictorAdd2"),Lt("Predictor3","PredictorAdd3"),Lt("Predictor4","PredictorAdd4"),Lt("Predictor5","PredictorAdd5"),Lt("Predictor6","PredictorAdd6"),Lt("Predictor7","PredictorAdd7"),Lt("Predictor8","PredictorAdd8"),Lt("Predictor9","PredictorAdd9"),Lt("Predictor10","PredictorAdd10"),Lt("Predictor11","PredictorAdd11"),Lt("Predictor12","PredictorAdd12"),Lt("Predictor13","PredictorAdd13");var na=q.PredictorAdd2;re("ColorIndexInverseTransform","MapARGB","32b",function(n){return n>>8&255},function(n){return n}),re("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(n){return n},function(n){return n>>8&255});var Ra,yr=q.ColorIndexInverseTransform,yi=q.MapARGB,qa=q.VP8LColorIndexInverseTransformAlpha,za=q.MapAlpha,wn=q.VP8LPredictorsAdd=[];wn.length=16,(q.VP8LPredictors=[]).length=16,(q.VP8LPredictorsAdd_C=[]).length=16,(q.VP8LPredictors_C=[]).length=16;var Hn,lr,rr,xn,sn,ln,wi,un,Ce,ia,nr,wr,xi,Ua,aa,Wn,Vn,An,Gn,Ai,Jn,Nn,oa,xr,Ar,ye,we,Fe,qe=a(511),hn=a(2041),sa=a(225),Ni=a(767),Ha=0,Zo=hn,Wa=sa,pr=Ni,Nr=qe,Va=0,Ga=1,Ys=2,Ja=3,Ya=4,$o=5,Ks=6,ts=7,es=8,Ka=9,rs=10,Fu=[2,3,7],Iu=[3,3,11],Xs=[280,256,256,256,40],Du=[0,1,1,1,0],Bu=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],Ou=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Mu=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],Tu=8,ns=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],is=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],la=null,Eu=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],Ru=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],Qs=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],qu=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],zu=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],Uu=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],Hu=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],Ln=[],Or=[],cn=[],Wu=1,Zs=2,Sn=[],Lr=[];ct("UpsampleRgbLinePair",ie,3),ct("UpsampleBgrLinePair",Ne,3),ct("UpsampleRgbaLinePair",Br,4),ct("UpsampleBgraLinePair",ze,4),ct("UpsampleArgbLinePair",Xe,4),ct("UpsampleRgba4444LinePair",Me,2),ct("UpsampleRgb565LinePair",Pe,2);var Vu=q.UpsampleRgbLinePair,Gu=q.UpsampleBgrLinePair,$s=q.UpsampleRgbaLinePair,tl=q.UpsampleBgraLinePair,el=q.UpsampleArgbLinePair,rl=q.UpsampleRgba4444LinePair,Ju=q.UpsampleRgb565LinePair,Xa=16,Qa=1<<Xa-1,ua=-227,as=482,Yu=6,nl=0,Ku=a(256),Xu=a(256),Qu=a(256),Zu=a(256),$u=a(as-ua),th=a(as-ua);Xr("YuvToRgbRow",ie,3),Xr("YuvToBgrRow",Ne,3),Xr("YuvToRgbaRow",Br,4),Xr("YuvToBgraRow",ze,4),Xr("YuvToArgbRow",Xe,4),Xr("YuvToRgba4444Row",Me,2),Xr("YuvToRgb565Row",Pe,2);var il=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],Za=[0,2,8],eh=[8,7,6,4,4,2,2,2,1,1,1,1],rh=1;this.WebPDecodeRGBA=function(n,s,f,g,b){var x=Ga,A=new fi,L=new _r;A.ba=L,L.S=x,L.width=[L.width],L.height=[L.height];var S=L.width,F=L.height,H=new rn;if(H==null||n==null)var $=2;else t(H!=null),$=pi(n,s,f,H.width,H.height,H.Pd,H.Qd,H.format,null);if($!=0?S=0:(S!=null&&(S[0]=H.width[0]),F!=null&&(F[0]=H.height[0]),S=1),S){L.width=L.width[0],L.height=L.height[0],g!=null&&(g[0]=L.width),b!=null&&(b[0]=L.height);t:{if(g=new ai,(b=new Yi).data=n,b.w=s,b.ha=f,b.kd=1,s=[0],t(b!=null),((n=pi(b.data,b.w,b.ha,null,null,null,s,null,b))==0||n==7)&&s[0]&&(n=4),(s=n)==0){if(t(A!=null),g.data=b.data,g.w=b.w+b.offset,g.ha=b.ha-b.offset,g.put=Hr,g.ac=Ve,g.bc=Wr,g.ma=A,b.xa){if((n=br())==null){A=1;break t}if(function(tt,J){var vt=[0],lt=[0],W=[0];e:for(;;){if(tt==null)return 0;if(J==null)return tt.a=2,0;if(tt.l=J,tt.a=0,Z(tt.m,J.data,J.w,J.ha),!zt(tt.m,vt,lt,W)){tt.a=3;break e}if(tt.xb=Zs,J.width=vt[0],J.height=lt[0],!Jr(vt[0],lt[0],1,tt,null))break e;return 1}return t(tt.a!=0),0}(n,g)){if(g=(s=gi(g.width,g.height,A.Oa,A.ba))==0){e:{g=n;r:for(;;){if(g==null){g=0;break e}if(t(g.s.yc!=null),t(g.s.Ya!=null),t(0<g.s.Wb),t((f=g.l)!=null),t((b=f.ma)!=null),g.xb!=0){if(g.ca=b.ba,g.tb=b.tb,t(g.ca!=null),!ta(b.Oa,f,Ja)){g.a=2;break r}if(!En(g,f.width)||f.da)break r;if((f.da||ue(g.ca.S))&&X(),11>g.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),g.ca.f.kb.F!=null&&X()),g.Pb&&0<g.s.ua&&g.s.vb.X==null&&!Ht(g.s.vb,g.s.Wa.Xa)){g.a=1;break r}g.xb=0}if(!Fr(g,g.V,g.Ba,g.c,g.i,f.o,$n))break r;b.Dc=g.Ma,g=1;break e}t(g.a!=0),g=0}g=!g}g&&(s=n.a)}else s=n.a}else{if((n=new Io)==null){A=1;break t}if(n.Fa=b.na,n.P=b.P,n.qc=b.Sa,Na(n,g)){if((s=gi(g.width,g.height,A.Oa,A.ba))==0){if(n.Aa=0,f=A.Oa,t((b=n)!=null),f!=null){if(0<(S=0>(S=f.Md)?0:100<S?255:255*S/100)){for(F=H=0;4>F;++F)12>($=b.pb[F]).lc&&($.ia=S*eh[0>$.lc?0:$.lc]>>3),H|=$.ia;H&&(alert("todo:VP8InitRandom"),b.ia=1)}b.Ga=f.Id,100<b.Ga?b.Ga=100:0>b.Ga&&(b.Ga=0)}Do(n,g)||(s=n.a)}}else s=n.a}s==0&&A.Oa!=null&&A.Oa.fd&&(s=ea(A.ba))}A=s}x=A!=0?null:11>x?L.f.RGBA.eb:L.f.kb.y}else x=null;return x};var al=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function d(q,rt){for(var pt="",_=0;_<4;_++)pt+=String.fromCharCode(q[rt++]);return pt}function m(q,rt){return(q[rt+0]<<0|q[rt+1]<<8|q[rt+2]<<16)>>>0}function v(q,rt){return(q[rt+0]<<0|q[rt+1]<<8|q[rt+2]<<16|q[rt+3]<<24)>>>0}new c;var w=[0],p=[0],C=[],k=new c,B=r,P=function(q,rt){var pt={},_=0,j=!1,V=0,R=0;if(pt.frames=[],!function(I,M,U,Y){for(var et=0;et<Y;et++)if(I[M+et]!=U.charCodeAt(et))return!0;return!1}(q,rt,"RIFF",4)){for(v(q,rt+=4),rt+=8;rt<q.length;){var ot=d(q,rt),nt=v(q,rt+=4);rt+=4;var ut=nt+(1&nt);switch(ot){case"VP8 ":case"VP8L":pt.frames[_]===void 0&&(pt.frames[_]={}),(dt=pt.frames[_]).src_off=j?R:rt-8,dt.src_size=V+nt+8,_++,j&&(j=!1,V=0,R=0);break;case"VP8X":(dt=pt.header={}).feature_flags=q[rt];var Z=rt+4;dt.canvas_width=1+m(q,Z),Z+=3,dt.canvas_height=1+m(q,Z),Z+=3;break;case"ALPH":j=!0,V=ut+8,R=rt-8;break;case"ANIM":(dt=pt.header).bgcolor=v(q,rt),Z=rt+4,dt.loop_count=(It=q)[(N=Z)+0]<<0|It[N+1]<<8,Z+=2;break;case"ANMF":var ht,dt;(dt=pt.frames[_]={}).offset_x=2*m(q,rt),rt+=3,dt.offset_y=2*m(q,rt),rt+=3,dt.width=1+m(q,rt),rt+=3,dt.height=1+m(q,rt),rt+=3,dt.duration=m(q,rt),rt+=3,ht=q[rt++],dt.dispose=1&ht,dt.blend=ht>>1&1}ot!="ANMF"&&(rt+=ut)}var It,N;return pt}}(B,0);P.response=B,P.rgbaoutput=!0,P.dataurl=!1;var O=P.header?P.header:null,E=P.frames?P.frames:null;if(O){O.loop_counter=O.loop_count,w=[O.canvas_height],p=[O.canvas_width];for(var K=0;K<E.length&&E[K].blend!=0;K++);}var st=E[0],yt=k.WebPDecodeRGBA(B,st.src_off,st.src_size,p,w);st.rgba=yt,st.imgwidth=p[0],st.imgheight=w[0];for(var Q=0;Q<p[0]*w[0]*4;Q++)C[Q]=yt[Q];return this.width=p,this.height=w,this.data=C,this}(function(r){var t=function(){return typeof Os=="function"},e=function(w,p,C,k){var B=4,P=l;switch(k){case r.image_compression.FAST:B=1,P=a;break;case r.image_compression.MEDIUM:B=6,P=h;break;case r.image_compression.SLOW:B=9,P=c}w=i(w,p,C,P);var O=Os(w,{level:B});return r.__addimage__.arrayBufferToBinaryString(O)},i=function(w,p,C,k){for(var B,P,O,E=w.length/p,K=new Uint8Array(w.length+E),st=m(),yt=0;yt<E;yt+=1){if(O=yt*p,B=w.subarray(O,O+p),k)K.set(k(B,C,P),O+yt);else{for(var Q,q=st.length,rt=[];Q<q;Q+=1)rt[Q]=st[Q](B,C,P);var pt=v(rt.concat());K.set(rt[pt],O+yt)}P=B}return K},o=function(w){var p=Array.apply([],w);return p.unshift(0),p},a=function(w,p){var C,k=[],B=w.length;k[0]=1;for(var P=0;P<B;P+=1)C=w[P-p]||0,k[P+1]=w[P]-C+256&255;return k},l=function(w,p,C){var k,B=[],P=w.length;B[0]=2;for(var O=0;O<P;O+=1)k=C&&C[O]||0,B[O+1]=w[O]-k+256&255;return B},h=function(w,p,C){var k,B,P=[],O=w.length;P[0]=3;for(var E=0;E<O;E+=1)k=w[E-p]||0,B=C&&C[E]||0,P[E+1]=w[E]+256-(k+B>>>1)&255;return P},c=function(w,p,C){var k,B,P,O,E=[],K=w.length;E[0]=4;for(var st=0;st<K;st+=1)k=w[st-p]||0,B=C&&C[st]||0,P=C&&C[st-p]||0,O=d(k,B,P),E[st+1]=w[st]-O+256&255;return E},d=function(w,p,C){if(w===p&&p===C)return w;var k=Math.abs(p-C),B=Math.abs(w-C),P=Math.abs(w+p-C-C);return k<=B&&k<=P?w:B<=P?p:C},m=function(){return[o,a,l,h,c]},v=function(w){var p=w.map(function(C){return C.reduce(function(k,B){return k+Math.abs(B)},0)});return p.indexOf(Math.min.apply(null,p))};r.processPNG=function(w,p,C,k){var B,P,O,E,K,st,yt,Q,q,rt,pt,_,j,V,R,ot=this.decode.FLATE_DECODE,nt="";if(this.__addimage__.isArrayBuffer(w)&&(w=new Uint8Array(w)),this.__addimage__.isArrayBufferView(w)){if(w=(O=new cc(w)).imgData,P=O.bits,B=O.colorSpace,K=O.colors,[4,6].indexOf(O.colorType)!==-1){if(O.bits===8){q=(Q=O.pixelBitlength==32?new Uint32Array(O.decodePixels().buffer):O.pixelBitlength==16?new Uint16Array(O.decodePixels().buffer):new Uint8Array(O.decodePixels().buffer)).length,pt=new Uint8Array(q*O.colors),rt=new Uint8Array(q);var ut,Z=O.pixelBitlength-O.bits;for(V=0,R=0;V<q;V++){for(j=Q[V],ut=0;ut<Z;)pt[R++]=j>>>ut&255,ut+=O.bits;rt[V]=j>>>ut&255}}if(O.bits===16){q=(Q=new Uint32Array(O.decodePixels().buffer)).length,pt=new Uint8Array(q*(32/O.pixelBitlength)*O.colors),rt=new Uint8Array(q*(32/O.pixelBitlength)),_=O.colors>1,V=0,R=0;for(var ht=0;V<q;)j=Q[V++],pt[R++]=j>>>0&255,_&&(pt[R++]=j>>>16&255,j=Q[V++],pt[R++]=j>>>0&255),rt[ht++]=j>>>16&255;P=8}k!==r.image_compression.NONE&&t()?(w=e(pt,O.width*O.colors,O.colors,k),yt=e(rt,O.width,1,k)):(w=pt,yt=rt,ot=void 0)}if(O.colorType===3&&(B=this.color_spaces.INDEXED,st=O.palette,O.transparency.indexed)){var dt=O.transparency.indexed,It=0;for(V=0,q=dt.length;V<q;++V)It+=dt[V];if((It/=255)===q-1&&dt.indexOf(0)!==-1)E=[dt.indexOf(0)];else if(It!==q){for(Q=O.decodePixels(),rt=new Uint8Array(Q.length),V=0,q=Q.length;V<q;V++)rt[V]=dt[Q[V]];yt=e(rt,O.width,1)}}var N=function(I){var M;switch(I){case r.image_compression.FAST:M=11;break;case r.image_compression.MEDIUM:M=13;break;case r.image_compression.SLOW:M=14;break;default:M=12}return M}(k);return ot===this.decode.FLATE_DECODE&&(nt="/Predictor "+N+" "),nt+="/Colors "+K+" /BitsPerComponent "+P+" /Columns "+O.width,(this.__addimage__.isArrayBuffer(w)||this.__addimage__.isArrayBufferView(w))&&(w=this.__addimage__.arrayBufferToBinaryString(w)),(yt&&this.__addimage__.isArrayBuffer(yt)||this.__addimage__.isArrayBufferView(yt))&&(yt=this.__addimage__.arrayBufferToBinaryString(yt)),{alias:C,data:w,index:p,filter:ot,decodeParameters:nt,transparency:E,palette:st,sMask:yt,predictor:N,width:O.width,height:O.height,bitsPerComponent:P,colorSpace:B}}}})(Ut.API),function(r){r.processGIF89A=function(t,e,i,o){var a=new fc(t),l=a.width,h=a.height,c=[];a.decodeAndBlitFrameRGBA(0,c);var d={data:c,width:l,height:h},m=new Ss(100).encode(d,100);return r.processJPEG.call(this,m,e,i,o)},r.processGIF87A=r.processGIF89A}(Ut.API),Er.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var r=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(r);for(var t=0;t<r;t++){var e=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0);this.palette[t]={red:o,green:i,blue:e,quad:a}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},Er.prototype.parseBGR=function(){this.pos=this.offset;try{var r="bit"+this.bitPP,t=this.width*this.height*4;this.data=new Uint8Array(t),this[r]()}catch(e){be.log("bit decode error:"+e)}},Er.prototype.bit1=function(){var r,t=Math.ceil(this.width/8),e=t%4;for(r=this.height-1;r>=0;r--){for(var i=this.bottom_up?r:this.height-1-r,o=0;o<t;o++)for(var a=this.datav.getUint8(this.pos++,!0),l=i*this.width*4+8*o*4,h=0;h<8&&8*o+h<this.width;h++){var c=this.palette[a>>7-h&1];this.data[l+4*h]=c.blue,this.data[l+4*h+1]=c.green,this.data[l+4*h+2]=c.red,this.data[l+4*h+3]=255}e!==0&&(this.pos+=4-e)}},Er.prototype.bit4=function(){for(var r=Math.ceil(this.width/2),t=r%4,e=this.height-1;e>=0;e--){for(var i=this.bottom_up?e:this.height-1-e,o=0;o<r;o++){var a=this.datav.getUint8(this.pos++,!0),l=i*this.width*4+2*o*4,h=a>>4,c=15&a,d=this.palette[h];if(this.data[l]=d.blue,this.data[l+1]=d.green,this.data[l+2]=d.red,this.data[l+3]=255,2*o+1>=this.width)break;d=this.palette[c],this.data[l+4]=d.blue,this.data[l+4+1]=d.green,this.data[l+4+2]=d.red,this.data[l+4+3]=255}t!==0&&(this.pos+=4-t)}},Er.prototype.bit8=function(){for(var r=this.width%4,t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,i=0;i<this.width;i++){var o=this.datav.getUint8(this.pos++,!0),a=e*this.width*4+4*i;if(o<this.palette.length){var l=this.palette[o];this.data[a]=l.red,this.data[a+1]=l.green,this.data[a+2]=l.blue,this.data[a+3]=255}else this.data[a]=255,this.data[a+1]=255,this.data[a+2]=255,this.data[a+3]=255}r!==0&&(this.pos+=4-r)}},Er.prototype.bit15=function(){for(var r=this.width%3,t=parseInt("11111",2),e=this.height-1;e>=0;e--){for(var i=this.bottom_up?e:this.height-1-e,o=0;o<this.width;o++){var a=this.datav.getUint16(this.pos,!0);this.pos+=2;var l=(a&t)/t*255|0,h=(a>>5&t)/t*255|0,c=(a>>10&t)/t*255|0,d=a>>15?255:0,m=i*this.width*4+4*o;this.data[m]=c,this.data[m+1]=h,this.data[m+2]=l,this.data[m+3]=d}this.pos+=r}},Er.prototype.bit16=function(){for(var r=this.width%3,t=parseInt("11111",2),e=parseInt("111111",2),i=this.height-1;i>=0;i--){for(var o=this.bottom_up?i:this.height-1-i,a=0;a<this.width;a++){var l=this.datav.getUint16(this.pos,!0);this.pos+=2;var h=(l&t)/t*255|0,c=(l>>5&e)/e*255|0,d=(l>>11)/t*255|0,m=o*this.width*4+4*a;this.data[m]=d,this.data[m+1]=c,this.data[m+2]=h,this.data[m+3]=255}this.pos+=r}},Er.prototype.bit24=function(){for(var r=this.height-1;r>=0;r--){for(var t=this.bottom_up?r:this.height-1-r,e=0;e<this.width;e++){var i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),l=t*this.width*4+4*e;this.data[l]=a,this.data[l+1]=o,this.data[l+2]=i,this.data[l+3]=255}this.pos+=this.width%4}},Er.prototype.bit32=function(){for(var r=this.height-1;r>=0;r--)for(var t=this.bottom_up?r:this.height-1-r,e=0;e<this.width;e++){var i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),l=this.datav.getUint8(this.pos++,!0),h=t*this.width*4+4*e;this.data[h]=a,this.data[h+1]=o,this.data[h+2]=i,this.data[h+3]=l}},Er.prototype.getData=function(){return this.data},function(r){r.processBMP=function(t,e,i,o){var a=new Er(t,!1),l=a.width,h=a.height,c={data:a.getData(),width:l,height:h},d=new Ss(100).encode(c,100);return r.processJPEG.call(this,d,e,i,o)}}(Ut.API),ql.prototype.getData=function(){return this.data},function(r){r.processWEBP=function(t,e,i,o){var a=new ql(t),l=a.width,h=a.height,c={data:a.getData(),width:l,height:h},d=new Ss(100).encode(c,100);return r.processJPEG.call(this,d,e,i,o)}}(Ut.API),Ut.API.processRGBA=function(r,t,e){for(var i=r.data,o=i.length,a=new Uint8Array(o/4*3),l=new Uint8Array(o/4),h=0,c=0,d=0;d<o;d+=4){var m=i[d],v=i[d+1],w=i[d+2],p=i[d+3];a[h++]=m,a[h++]=v,a[h++]=w,l[c++]=p}var C=this.__addimage__.arrayBufferToBinaryString(a);return{alpha:this.__addimage__.arrayBufferToBinaryString(l),data:C,index:t,alias:e,colorSpace:"DeviceRGB",bitsPerComponent:8,width:r.width,height:r.height}},Ut.API.setLanguage=function(r){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[r]!==void 0&&(this.internal.languageSettings.languageCode=r,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},ji=Ut.API,po=ji.getCharWidthsArray=function(r,t){var e,i,o=(t=t||{}).font||this.internal.getFont(),a=t.fontSize||this.internal.getFontSize(),l=t.charSpace||this.internal.getCharSpace(),h=t.widths?t.widths:o.metadata.Unicode.widths,c=h.fof?h.fof:1,d=t.kerning?t.kerning:o.metadata.Unicode.kerning,m=d.fof?d.fof:1,v=t.doKerning!==!1,w=0,p=r.length,C=0,k=h[0]||c,B=[];for(e=0;e<p;e++)i=r.charCodeAt(e),typeof o.metadata.widthOfString=="function"?B.push((o.metadata.widthOfGlyph(o.metadata.characterToGlyph(i))+l*(1e3/a)||0)/1e3):(w=v&&de(d[i])==="object"&&!isNaN(parseInt(d[i][C],10))?d[i][C]/m:0,B.push((h[i]||k)/c+w)),C=i;return B},Ml=ji.getStringUnitWidth=function(r,t){var e=(t=t||{}).fontSize||this.internal.getFontSize(),i=t.font||this.internal.getFont(),o=t.charSpace||this.internal.getCharSpace();return ji.processArabic&&(r=ji.processArabic(r)),typeof i.metadata.widthOfString=="function"?i.metadata.widthOfString(r,e,o)/e:po.apply(this,arguments).reduce(function(a,l){return a+l},0)},Tl=function(r,t,e,i){for(var o=[],a=0,l=r.length,h=0;a!==l&&h+t[a]<e;)h+=t[a],a++;o.push(r.slice(0,a));var c=a;for(h=0;a!==l;)h+t[a]>i&&(o.push(r.slice(c,a)),h=0,c=a),h+=t[a],a++;return c!==a&&o.push(r.slice(c,a)),o},El=function(r,t,e){e||(e={});var i,o,a,l,h,c,d,m=[],v=[m],w=e.textIndent||0,p=0,C=0,k=r.split(" "),B=po.apply(this,[" ",e])[0];if(c=e.lineIndent===-1?k[0].length+2:e.lineIndent||0){var P=Array(c).join(" "),O=[];k.map(function(K){(K=K.split(/\s*\n/)).length>1?O=O.concat(K.map(function(st,yt){return(yt&&st.length?`
`:"")+st})):O.push(K[0])}),k=O,c=Ml.apply(this,[P,e])}for(a=0,l=k.length;a<l;a++){var E=0;if(i=k[a],c&&i[0]==`
`&&(i=i.substr(1),E=1),w+p+(C=(o=po.apply(this,[i,e])).reduce(function(K,st){return K+st},0))>t||E){if(C>t){for(h=Tl.apply(this,[i,o,t-(w+p),t]),m.push(h.shift()),m=[h.pop()];h.length;)v.push([h.shift()]);C=o.slice(i.length-(m[0]?m[0].length:0)).reduce(function(K,st){return K+st},0)}else m=[i];v.push(m),w=C+c,p=B}else m.push(i),w+=p+C,p=B}return d=c?function(K,st){return(st?P:"")+K.join(" ")}:function(K){return K.join(" ")},v.map(d)},ji.splitTextToSize=function(r,t,e){var i,o=(e=e||{}).fontSize||this.internal.getFontSize(),a=(function(m){if(m.widths&&m.kerning)return{widths:m.widths,kerning:m.kerning};var v=this.internal.getFont(m.fontName,m.fontStyle);return v.metadata.Unicode?{widths:v.metadata.Unicode.widths||{0:1},kerning:v.metadata.Unicode.kerning||{}}:{font:v.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,e);i=Array.isArray(r)?r:String(r).split(/\r?\n/);var l=1*this.internal.scaleFactor*t/o;a.textIndent=e.textIndent?1*e.textIndent*this.internal.scaleFactor/o:0,a.lineIndent=e.lineIndent;var h,c,d=[];for(h=0,c=i.length;h<c;h++)d=d.concat(El.apply(this,[i[h],l,a]));return d},function(r){r.__fontmetrics__=r.__fontmetrics__||{};for(var t="klmnopqrstuvwxyz",e={},i={},o=0;o<t.length;o++)e[t[o]]="0123456789abcdef"[o],i["0123456789abcdef"[o]]=t[o];var a=function(v){return"0x"+parseInt(v,10).toString(16)},l=r.__fontmetrics__.compress=function(v){var w,p,C,k,B=["{"];for(var P in v){if(w=v[P],isNaN(parseInt(P,10))?p="'"+P+"'":(P=parseInt(P,10),p=(p=a(P).slice(2)).slice(0,-1)+i[p.slice(-1)]),typeof w=="number")w<0?(C=a(w).slice(3),k="-"):(C=a(w).slice(2),k=""),C=k+C.slice(0,-1)+i[C.slice(-1)];else{if(de(w)!=="object")throw new Error("Don't know what to do with value type "+de(w)+".");C=l(w)}B.push(p+C)}return B.push("}"),B.join("")},h=r.__fontmetrics__.uncompress=function(v){if(typeof v!="string")throw new Error("Invalid argument passed to uncompress.");for(var w,p,C,k,B={},P=1,O=B,E=[],K="",st="",yt=v.length-1,Q=1;Q<yt;Q+=1)(k=v[Q])=="'"?w?(C=w.join(""),w=void 0):w=[]:w?w.push(k):k=="{"?(E.push([O,C]),O={},C=void 0):k=="}"?((p=E.pop())[0][p[1]]=O,C=void 0,O=p[0]):k=="-"?P=-1:C===void 0?e.hasOwnProperty(k)?(K+=e[k],C=parseInt(K,16)*P,P=1,K=""):K+=k:e.hasOwnProperty(k)?(st+=e[k],O[C]=parseInt(st,16)*P,P=1,C=void 0,st=""):st+=k;return B},c={codePages:["WinAnsiEncoding"],WinAnsiEncoding:h("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},d={Unicode:{Courier:c,"Courier-Bold":c,"Courier-BoldOblique":c,"Courier-Oblique":c,Helvetica:c,"Helvetica-Bold":c,"Helvetica-BoldOblique":c,"Helvetica-Oblique":c,"Times-Roman":c,"Times-Bold":c,"Times-BoldItalic":c,"Times-Italic":c}},m={Unicode:{"Courier-Oblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":h("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":h("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:h("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:h("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":h("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":h("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};r.events.push(["addFont",function(v){var w=v.font,p=m.Unicode[w.postScriptName];p&&(w.metadata.Unicode={},w.metadata.Unicode.widths=p.widths,w.metadata.Unicode.kerning=p.kerning);var C=d.Unicode[w.postScriptName];C&&(w.metadata.Unicode.encoding=C,w.encoding=C.codePages[0])}])}(Ut.API),function(r){var t=function(e){for(var i=e.length,o=new Uint8Array(i),a=0;a<i;a++)o[a]=e.charCodeAt(a);return o};r.API.events.push(["addFont",function(e){var i=void 0,o=e.font,a=e.instance;if(!o.isStandardFont){if(a===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+o.postScriptName+"').");if(typeof(i=a.existsFileInVFS(o.postScriptName)===!1?a.loadFile(o.postScriptName):a.getFileFromVFS(o.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+o.postScriptName+"').");(function(l,h){h=/^\x00\x01\x00\x00/.test(h)?t(h):t(ma(h)),l.metadata=r.API.TTFFont.open(h),l.metadata.Unicode=l.metadata.Unicode||{encoding:{},kerning:{},widths:[]},l.metadata.glyIdsUsed=[0]})(o,i)}}])}(Ut),function(r){function t(){return(Wt.canvg?Promise.resolve(Wt.canvg):Cs(()=>import("./index.es-JPZNIsat.js"),__vite__mapDeps([0,1,2,3,4,5]))).catch(function(e){return Promise.reject(new Error("Could not load canvg: "+e))}).then(function(e){return e.default?e.default:e})}Ut.API.addSvgAsImage=function(e,i,o,a,l,h,c,d){if(isNaN(i)||isNaN(o))throw be.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(a)||isNaN(l))throw be.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var m=document.createElement("canvas");m.width=a,m.height=l;var v=m.getContext("2d");v.fillStyle="#fff",v.fillRect(0,0,m.width,m.height);var w={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},p=this;return t().then(function(C){return C.fromString(v,e,w)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(C){return C.render(w)}).then(function(){p.addImage(m.toDataURL("image/jpeg",1),i,o,a,l,c,d)})}}(),Ut.API.putTotalPages=function(r){var t,e=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(t=new RegExp(r,"g"),e=this.internal.getNumberOfPages()):(t=new RegExp(this.pdfEscape16(r,this.internal.getFont()),"g"),e=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var i=1;i<=this.internal.getNumberOfPages();i++)for(var o=0;o<this.internal.pages[i].length;o++)this.internal.pages[i][o]=this.internal.pages[i][o].replace(t,e);return this},Ut.API.viewerPreferences=function(r,t){var e;r=r||{},t=t||!1;var i,o,a,l={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},h=Object.keys(l),c=[],d=0,m=0,v=0;function w(C,k){var B,P=!1;for(B=0;B<C.length;B+=1)C[B]===k&&(P=!0);return P}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(l)),this.internal.viewerpreferences.isSubscribed=!1),e=this.internal.viewerpreferences.configuration,r==="reset"||t===!0){var p=h.length;for(v=0;v<p;v+=1)e[h[v]].value=e[h[v]].defaultValue,e[h[v]].explicitSet=!1}if(de(r)==="object"){for(o in r)if(a=r[o],w(h,o)&&a!==void 0){if(e[o].type==="boolean"&&typeof a=="boolean")e[o].value=a;else if(e[o].type==="name"&&w(e[o].valueSet,a))e[o].value=a;else if(e[o].type==="integer"&&Number.isInteger(a))e[o].value=a;else if(e[o].type==="array"){for(d=0;d<a.length;d+=1)if(i=!0,a[d].length===1&&typeof a[d][0]=="number")c.push(String(a[d]-1));else if(a[d].length>1){for(m=0;m<a[d].length;m+=1)typeof a[d][m]!="number"&&(i=!1);i===!0&&c.push([a[d][0]-1,a[d][1]-1].join(" "))}e[o].value="["+c.join(" ")+"]"}else e[o].value=e[o].defaultValue;e[o].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var C,k=[];for(C in e)e[C].explicitSet===!0&&(e[C].type==="name"?k.push("/"+C+" /"+e[C].value):k.push("/"+C+" "+e[C].value));k.length!==0&&this.internal.write(`/ViewerPreferences
<<
`+k.join(`
`)+`
>>`)}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=e,this},function(r){var t=function(){var i='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',o=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),a=unescape(encodeURIComponent(i)),l=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),h=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),c=unescape(encodeURIComponent("</x:xmpmeta>")),d=a.length+l.length+h.length+o.length+c.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+d+" >>"),this.internal.write("stream"),this.internal.write(o+a+l+h+c),this.internal.write("endstream"),this.internal.write("endobj")},e=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};r.addMetadata=function(i,o){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:i,namespaceuri:o||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",e),this.internal.events.subscribe("postPutResources",t)),this}}(Ut.API),function(r){var t=r.API,e=t.pdfEscape16=function(a,l){for(var h,c=l.metadata.Unicode.widths,d=["","0","00","000","0000"],m=[""],v=0,w=a.length;v<w;++v){if(h=l.metadata.characterToGlyph(a.charCodeAt(v)),l.metadata.glyIdsUsed.push(h),l.metadata.toUnicode[h]=a.charCodeAt(v),c.indexOf(h)==-1&&(c.push(h),c.push([parseInt(l.metadata.widthOfGlyph(h),10)])),h=="0")return m.join("");h=h.toString(16),m.push(d[4-h.length],h)}return m.join("")},i=function(a){var l,h,c,d,m,v,w;for(m=`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange`,c=[],v=0,w=(h=Object.keys(a).sort(function(p,C){return p-C})).length;v<w;v++)l=h[v],c.length>=100&&(m+=`
`+c.length+` beginbfchar
`+c.join(`
`)+`
endbfchar`,c=[]),a[l]!==void 0&&a[l]!==null&&typeof a[l].toString=="function"&&(d=("0000"+a[l].toString(16)).slice(-4),l=("0000"+(+l).toString(16)).slice(-4),c.push("<"+l+"><"+d+">"));return c.length&&(m+=`
`+c.length+` beginbfchar
`+c.join(`
`)+`
endbfchar
`),m+=`endcmap
CMapName currentdict /CMap defineresource pop
end
end`};t.events.push(["putFont",function(a){(function(l){var h=l.font,c=l.out,d=l.newObject,m=l.putStream;if(h.metadata instanceof r.API.TTFFont&&h.encoding==="Identity-H"){for(var v=h.metadata.Unicode.widths,w=h.metadata.subset.encode(h.metadata.glyIdsUsed,1),p="",C=0;C<w.length;C++)p+=String.fromCharCode(w[C]);var k=d();m({data:p,addLength1:!0,objectId:k}),c("endobj");var B=d();m({data:i(h.metadata.toUnicode),addLength1:!0,objectId:B}),c("endobj");var P=d();c("<<"),c("/Type /FontDescriptor"),c("/FontName /"+Fi(h.fontName)),c("/FontFile2 "+k+" 0 R"),c("/FontBBox "+r.API.PDFObject.convert(h.metadata.bbox)),c("/Flags "+h.metadata.flags),c("/StemV "+h.metadata.stemV),c("/ItalicAngle "+h.metadata.italicAngle),c("/Ascent "+h.metadata.ascender),c("/Descent "+h.metadata.decender),c("/CapHeight "+h.metadata.capHeight),c(">>"),c("endobj");var O=d();c("<<"),c("/Type /Font"),c("/BaseFont /"+Fi(h.fontName)),c("/FontDescriptor "+P+" 0 R"),c("/W "+r.API.PDFObject.convert(v)),c("/CIDToGIDMap /Identity"),c("/DW 1000"),c("/Subtype /CIDFontType2"),c("/CIDSystemInfo"),c("<<"),c("/Supplement 0"),c("/Registry (Adobe)"),c("/Ordering ("+h.encoding+")"),c(">>"),c(">>"),c("endobj"),h.objectNumber=d(),c("<<"),c("/Type /Font"),c("/Subtype /Type0"),c("/ToUnicode "+B+" 0 R"),c("/BaseFont /"+Fi(h.fontName)),c("/Encoding /"+h.encoding),c("/DescendantFonts ["+O+" 0 R]"),c(">>"),c("endobj"),h.isAlreadyPutted=!0}})(a)}]),t.events.push(["putFont",function(a){(function(l){var h=l.font,c=l.out,d=l.newObject,m=l.putStream;if(h.metadata instanceof r.API.TTFFont&&h.encoding==="WinAnsiEncoding"){for(var v=h.metadata.rawData,w="",p=0;p<v.length;p++)w+=String.fromCharCode(v[p]);var C=d();m({data:w,addLength1:!0,objectId:C}),c("endobj");var k=d();m({data:i(h.metadata.toUnicode),addLength1:!0,objectId:k}),c("endobj");var B=d();c("<<"),c("/Descent "+h.metadata.decender),c("/CapHeight "+h.metadata.capHeight),c("/StemV "+h.metadata.stemV),c("/Type /FontDescriptor"),c("/FontFile2 "+C+" 0 R"),c("/Flags 96"),c("/FontBBox "+r.API.PDFObject.convert(h.metadata.bbox)),c("/FontName /"+Fi(h.fontName)),c("/ItalicAngle "+h.metadata.italicAngle),c("/Ascent "+h.metadata.ascender),c(">>"),c("endobj"),h.objectNumber=d();for(var P=0;P<h.metadata.hmtx.widths.length;P++)h.metadata.hmtx.widths[P]=parseInt(h.metadata.hmtx.widths[P]*(1e3/h.metadata.head.unitsPerEm));c("<</Subtype/TrueType/Type/Font/ToUnicode "+k+" 0 R/BaseFont/"+Fi(h.fontName)+"/FontDescriptor "+B+" 0 R/Encoding/"+h.encoding+" /FirstChar 29 /LastChar 255 /Widths "+r.API.PDFObject.convert(h.metadata.hmtx.widths)+">>"),c("endobj"),h.isAlreadyPutted=!0}})(a)}]);var o=function(a){var l,h=a.text||"",c=a.x,d=a.y,m=a.options||{},v=a.mutex||{},w=v.pdfEscape,p=v.activeFontKey,C=v.fonts,k=p,B="",P=0,O="",E=C[k].encoding;if(C[k].encoding!=="Identity-H")return{text:h,x:c,y:d,options:m,mutex:v};for(O=h,k=p,Array.isArray(h)&&(O=h[0]),P=0;P<O.length;P+=1)C[k].metadata.hasOwnProperty("cmap")&&(l=C[k].metadata.cmap.unicode.codeMap[O[P].charCodeAt(0)]),l||O[P].charCodeAt(0)<256&&C[k].metadata.hasOwnProperty("Unicode")?B+=O[P]:B+="";var K="";return parseInt(k.slice(1))<14||E==="WinAnsiEncoding"?K=w(B,k).split("").map(function(st){return st.charCodeAt(0).toString(16)}).join(""):E==="Identity-H"&&(K=e(B,C[k])),v.isHex=!0,{text:K,x:c,y:d,options:m,mutex:v}};t.events.push(["postProcessText",function(a){var l=a.text||"",h=[],c={text:l,x:a.x,y:a.y,options:a.options,mutex:a.mutex};if(Array.isArray(l)){var d=0;for(d=0;d<l.length;d+=1)Array.isArray(l[d])&&l[d].length===3?h.push([o(Object.assign({},c,{text:l[d][0]})).text,l[d][1],l[d][2]]):h.push(o(Object.assign({},c,{text:l[d]})).text);a.text=h}else a.text=o(Object.assign({},c,{text:l})).text}])}(Ut),function(r){var t=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};r.existsFileInVFS=function(e){return t.call(this),this.internal.vFS[e]!==void 0},r.addFileToVFS=function(e,i){return t.call(this),this.internal.vFS[e]=i,this},r.getFileFromVFS=function(e){return t.call(this),this.internal.vFS[e]!==void 0?this.internal.vFS[e]:null}}(Ut.API),function(r){r.__bidiEngine__=r.prototype.__bidiEngine__=function(i){var o,a,l,h,c,d,m,v=t,w=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],p=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],C={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},k={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},B=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],P=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),O=!1,E=0;this.__bidiEngine__={};var K=function(_){var j=_.charCodeAt(),V=j>>8,R=k[V];return R!==void 0?v[256*R+(255&j)]:V===252||V===253?"AL":P.test(V)?"L":V===8?"R":"N"},st=function(_){for(var j,V=0;V<_.length;V++){if((j=K(_.charAt(V)))==="L")return!1;if(j==="R")return!0}return!1},yt=function(_,j,V,R){var ot,nt,ut,Z,ht=j[R];switch(ht){case"L":case"R":O=!1;break;case"N":case"AN":break;case"EN":O&&(ht="AN");break;case"AL":O=!0,ht="R";break;case"WS":ht="N";break;case"CS":R<1||R+1>=j.length||(ot=V[R-1])!=="EN"&&ot!=="AN"||(nt=j[R+1])!=="EN"&&nt!=="AN"?ht="N":O&&(nt="AN"),ht=nt===ot?nt:"N";break;case"ES":ht=(ot=R>0?V[R-1]:"B")==="EN"&&R+1<j.length&&j[R+1]==="EN"?"EN":"N";break;case"ET":if(R>0&&V[R-1]==="EN"){ht="EN";break}if(O){ht="N";break}for(ut=R+1,Z=j.length;ut<Z&&j[ut]==="ET";)ut++;ht=ut<Z&&j[ut]==="EN"?"EN":"N";break;case"NSM":if(l&&!h){for(Z=j.length,ut=R+1;ut<Z&&j[ut]==="NSM";)ut++;if(ut<Z){var dt=_[R],It=dt>=1425&&dt<=2303||dt===64286;if(ot=j[ut],It&&(ot==="R"||ot==="AL")){ht="R";break}}}ht=R<1||(ot=j[R-1])==="B"?"N":V[R-1];break;case"B":O=!1,o=!0,ht=E;break;case"S":a=!0,ht="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":O=!1;break;case"BN":ht="N"}return ht},Q=function(_,j,V){var R=_.split("");return V&&q(R,V,{hiLevel:E}),R.reverse(),j&&j.reverse(),R.join("")},q=function(_,j,V){var R,ot,nt,ut,Z,ht=-1,dt=_.length,It=0,N=[],I=E?p:w,M=[];for(O=!1,o=!1,a=!1,ot=0;ot<dt;ot++)M[ot]=K(_[ot]);for(nt=0;nt<dt;nt++){if(Z=It,N[nt]=yt(_,M,N,nt),R=240&(It=I[Z][C[N[nt]]]),It&=15,j[nt]=ut=I[It][5],R>0)if(R===16){for(ot=ht;ot<nt;ot++)j[ot]=1;ht=-1}else ht=-1;if(I[It][6])ht===-1&&(ht=nt);else if(ht>-1){for(ot=ht;ot<nt;ot++)j[ot]=ut;ht=-1}M[nt]==="B"&&(j[nt]=0),V.hiLevel|=ut}a&&function(U,Y,et){for(var it=0;it<et;it++)if(U[it]==="S"){Y[it]=E;for(var at=it-1;at>=0&&U[at]==="WS";at--)Y[at]=E}}(M,j,dt)},rt=function(_,j,V,R,ot){if(!(ot.hiLevel<_)){if(_===1&&E===1&&!o)return j.reverse(),void(V&&V.reverse());for(var nt,ut,Z,ht,dt=j.length,It=0;It<dt;){if(R[It]>=_){for(Z=It+1;Z<dt&&R[Z]>=_;)Z++;for(ht=It,ut=Z-1;ht<ut;ht++,ut--)nt=j[ht],j[ht]=j[ut],j[ut]=nt,V&&(nt=V[ht],V[ht]=V[ut],V[ut]=nt);It=Z}It++}}},pt=function(_,j,V){var R=_.split(""),ot={hiLevel:E};return V||(V=[]),q(R,V,ot),function(nt,ut,Z){if(Z.hiLevel!==0&&m)for(var ht,dt=0;dt<nt.length;dt++)ut[dt]===1&&(ht=B.indexOf(nt[dt]))>=0&&(nt[dt]=B[ht+1])}(R,V,ot),rt(2,R,j,V,ot),rt(1,R,j,V,ot),R.join("")};return this.__bidiEngine__.doBidiReorder=function(_,j,V){if(function(ot,nt){if(nt)for(var ut=0;ut<ot.length;ut++)nt[ut]=ut;h===void 0&&(h=st(ot)),d===void 0&&(d=st(ot))}(_,j),l||!c||d)if(l&&c&&h^d)E=h?1:0,_=Q(_,j,V);else if(!l&&c&&d)E=h?1:0,_=pt(_,j,V),_=Q(_,j);else if(!l||h||c||d){if(l&&!c&&h^d)_=Q(_,j),h?(E=0,_=pt(_,j,V)):(E=1,_=pt(_,j,V),_=Q(_,j));else if(l&&h&&!c&&d)E=1,_=pt(_,j,V),_=Q(_,j);else if(!l&&!c&&h^d){var R=m;h?(E=1,_=pt(_,j,V),E=0,m=!1,_=pt(_,j,V),m=R):(E=0,_=pt(_,j,V),_=Q(_,j),E=1,m=!1,_=pt(_,j,V),m=R,_=Q(_,j))}}else E=0,_=pt(_,j,V);else E=h?1:0,_=pt(_,j,V);return _},this.__bidiEngine__.setOptions=function(_){_&&(l=_.isInputVisual,c=_.isOutputVisual,h=_.isInputRtl,d=_.isOutputRtl,m=_.isSymmetricSwapping)},this.__bidiEngine__.setOptions(i),this.__bidiEngine__};var t=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],e=new r.__bidiEngine__({isInputVisual:!0});r.API.events.push(["postProcessText",function(i){var o=i.text;i.x,i.y;var a=i.options||{};i.mutex,a.lang;var l=[];if(a.isInputVisual=typeof a.isInputVisual!="boolean"||a.isInputVisual,e.setOptions(a),Object.prototype.toString.call(o)==="[object Array]"){var h=0;for(l=[],h=0;h<o.length;h+=1)Object.prototype.toString.call(o[h])==="[object Array]"?l.push([e.doBidiReorder(o[h][0]),o[h][1],o[h][2]]):l.push([e.doBidiReorder(o[h])]);i.text=l}else i.text=e.doBidiReorder(o);e.setOptions({isInputVisual:!0})}])}(Ut),Ut.API.TTFFont=function(){function r(t){var e;if(this.rawData=t,e=this.contents=new On(t),this.contents.pos=4,e.readString(4)==="ttcf")throw new Error("TTCF not supported.");e.pos=0,this.parse(),this.subset=new _c(this),this.registerTTF()}return r.open=function(t){return new r(t)},r.prototype.parse=function(){return this.directory=new dc(this.contents),this.head=new gc(this),this.name=new wc(this),this.cmap=new pu(this),this.toUnicode={},this.hhea=new mc(this),this.maxp=new xc(this),this.hmtx=new Ac(this),this.post=new bc(this),this.os2=new vc(this),this.loca=new Pc(this),this.glyf=new Nc(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},r.prototype.registerTTF=function(){var t,e,i,o,a;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var l,h,c,d;for(d=[],l=0,h=(c=this.bbox).length;l<h;l++)t=c[l],d.push(Math.round(t*this.scaleFactor));return d}).call(this),this.stemV=0,this.post.exists?(i=255&(o=this.post.italic_angle),(32768&(e=o>>16))!=0&&(e=-(1+(65535^e))),this.italicAngle=+(e+"."+i)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(a=this.familyClass)===1||a===2||a===3||a===4||a===5||a===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},r.prototype.characterToGlyph=function(t){var e;return((e=this.cmap.unicode)!=null?e.codeMap[t]:void 0)||0},r.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},r.prototype.widthOfString=function(t,e,i){var o,a,l,h;for(l=0,a=0,h=(t=""+t).length;0<=h?a<h:a>h;a=0<=h?++a:--a)o=t.charCodeAt(a),l+=this.widthOfGlyph(this.characterToGlyph(o))+i*(1e3/e)||0;return l*(e/1e3)},r.prototype.lineHeight=function(t,e){var i;return e==null&&(e=!1),i=e?this.lineGap:0,(this.ascender+i-this.decender)/1e3*t},r}();var zr,On=function(){function r(t){this.data=t??[],this.pos=0,this.length=this.data.length}return r.prototype.readByte=function(){return this.data[this.pos++]},r.prototype.writeByte=function(t){return this.data[this.pos++]=t},r.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},r.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},r.prototype.readInt32=function(){var t;return(t=this.readUInt32())>=2147483648?t-4294967296:t},r.prototype.writeInt32=function(t){return t<0&&(t+=4294967296),this.writeUInt32(t)},r.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},r.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},r.prototype.readInt16=function(){var t;return(t=this.readUInt16())>=32768?t-65536:t},r.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},r.prototype.readString=function(t){var e,i;for(i=[],e=0;0<=t?e<t:e>t;e=0<=t?++e:--e)i[e]=String.fromCharCode(this.readByte());return i.join("")},r.prototype.writeString=function(t){var e,i,o;for(o=[],e=0,i=t.length;0<=i?e<i:e>i;e=0<=i?++e:--e)o.push(this.writeByte(t.charCodeAt(e)));return o},r.prototype.readShort=function(){return this.readInt16()},r.prototype.writeShort=function(t){return this.writeInt16(t)},r.prototype.readLongLong=function(){var t,e,i,o,a,l,h,c;return t=this.readByte(),e=this.readByte(),i=this.readByte(),o=this.readByte(),a=this.readByte(),l=this.readByte(),h=this.readByte(),c=this.readByte(),128&t?-1*(72057594037927940*(255^t)+281474976710656*(255^e)+1099511627776*(255^i)+4294967296*(255^o)+16777216*(255^a)+65536*(255^l)+256*(255^h)+(255^c)+1):72057594037927940*t+281474976710656*e+1099511627776*i+4294967296*o+16777216*a+65536*l+256*h+c},r.prototype.writeLongLong=function(t){var e,i;return e=Math.floor(t/4294967296),i=**********&t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(i>>24&255),this.writeByte(i>>16&255),this.writeByte(i>>8&255),this.writeByte(255&i)},r.prototype.readInt=function(){return this.readInt32()},r.prototype.writeInt=function(t){return this.writeInt32(t)},r.prototype.read=function(t){var e,i;for(e=[],i=0;0<=t?i<t:i>t;i=0<=t?++i:--i)e.push(this.readByte());return e},r.prototype.write=function(t){var e,i,o,a;for(a=[],i=0,o=t.length;i<o;i++)e=t[i],a.push(this.writeByte(e));return a},r}(),dc=function(){var r;function t(e){var i,o,a;for(this.scalarType=e.readInt(),this.tableCount=e.readShort(),this.searchRange=e.readShort(),this.entrySelector=e.readShort(),this.rangeShift=e.readShort(),this.tables={},o=0,a=this.tableCount;0<=a?o<a:o>a;o=0<=a?++o:--o)i={tag:e.readString(4),checksum:e.readInt(),offset:e.readInt(),length:e.readInt()},this.tables[i.tag]=i}return t.prototype.encode=function(e){var i,o,a,l,h,c,d,m,v,w,p,C,k;for(k in p=Object.keys(e).length,c=Math.log(2),v=16*Math.floor(Math.log(p)/c),l=Math.floor(v/c),m=16*p-v,(o=new On).writeInt(this.scalarType),o.writeShort(p),o.writeShort(v),o.writeShort(l),o.writeShort(m),a=16*p,d=o.pos+a,h=null,C=[],e)for(w=e[k],o.writeString(k),o.writeInt(r(w)),o.writeInt(d),o.writeInt(w.length),C=C.concat(w),k==="head"&&(h=d),d+=w.length;d%4;)C.push(0),d++;return o.write(C),i=2981146554-r(o.data),o.pos=h+8,o.writeUInt32(i),o.data},r=function(e){var i,o,a,l;for(e=gu.call(e);e.length%4;)e.push(0);for(a=new On(e),o=0,i=0,l=e.length;i<l;i=i+=4)o+=a.readUInt32();return **********&o},t}(),pc={}.hasOwnProperty,en=function(r,t){for(var e in t)pc.call(t,e)&&(r[e]=t[e]);function i(){this.constructor=r}return i.prototype=t.prototype,r.prototype=new i,r.__super__=t.prototype,r};zr=function(){function r(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}return r.prototype.parse=function(){},r.prototype.encode=function(){},r.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},r}();var gc=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="head",t.prototype.parse=function(e){return e.pos=this.offset,this.version=e.readInt(),this.revision=e.readInt(),this.checkSumAdjustment=e.readInt(),this.magicNumber=e.readInt(),this.flags=e.readShort(),this.unitsPerEm=e.readShort(),this.created=e.readLongLong(),this.modified=e.readLongLong(),this.xMin=e.readShort(),this.yMin=e.readShort(),this.xMax=e.readShort(),this.yMax=e.readShort(),this.macStyle=e.readShort(),this.lowestRecPPEM=e.readShort(),this.fontDirectionHint=e.readShort(),this.indexToLocFormat=e.readShort(),this.glyphDataFormat=e.readShort()},t.prototype.encode=function(e){var i;return(i=new On).writeInt(this.version),i.writeInt(this.revision),i.writeInt(this.checkSumAdjustment),i.writeInt(this.magicNumber),i.writeShort(this.flags),i.writeShort(this.unitsPerEm),i.writeLongLong(this.created),i.writeLongLong(this.modified),i.writeShort(this.xMin),i.writeShort(this.yMin),i.writeShort(this.xMax),i.writeShort(this.yMax),i.writeShort(this.macStyle),i.writeShort(this.lowestRecPPEM),i.writeShort(this.fontDirectionHint),i.writeShort(e),i.writeShort(this.glyphDataFormat),i.data},t}(),zl=function(){function r(t,e){var i,o,a,l,h,c,d,m,v,w,p,C,k,B,P,O,E;switch(this.platformID=t.readUInt16(),this.encodingID=t.readShort(),this.offset=e+t.readInt(),v=t.pos,t.pos=this.offset,this.format=t.readUInt16(),this.length=t.readUInt16(),this.language=t.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(c=0;c<256;++c)this.codeMap[c]=t.readByte();break;case 4:for(p=t.readUInt16(),w=p/2,t.pos+=6,a=function(){var K,st;for(st=[],c=K=0;0<=w?K<w:K>w;c=0<=w?++K:--K)st.push(t.readUInt16());return st}(),t.pos+=2,k=function(){var K,st;for(st=[],c=K=0;0<=w?K<w:K>w;c=0<=w?++K:--K)st.push(t.readUInt16());return st}(),d=function(){var K,st;for(st=[],c=K=0;0<=w?K<w:K>w;c=0<=w?++K:--K)st.push(t.readUInt16());return st}(),m=function(){var K,st;for(st=[],c=K=0;0<=w?K<w:K>w;c=0<=w?++K:--K)st.push(t.readUInt16());return st}(),o=(this.length-t.pos+this.offset)/2,h=function(){var K,st;for(st=[],c=K=0;0<=o?K<o:K>o;c=0<=o?++K:--K)st.push(t.readUInt16());return st}(),c=P=0,E=a.length;P<E;c=++P)for(B=a[c],i=O=C=k[c];C<=B?O<=B:O>=B;i=C<=B?++O:--O)m[c]===0?l=i+d[c]:(l=h[m[c]/2+(i-C)-(w-c)]||0)!==0&&(l+=d[c]),this.codeMap[i]=65535&l}t.pos=v}return r.encode=function(t,e){var i,o,a,l,h,c,d,m,v,w,p,C,k,B,P,O,E,K,st,yt,Q,q,rt,pt,_,j,V,R,ot,nt,ut,Z,ht,dt,It,N,I,M,U,Y,et,it,at,Nt,At,jt;switch(R=new On,l=Object.keys(t).sort(function(Pt,Ht){return Pt-Ht}),e){case"macroman":for(k=0,B=function(){var Pt=[];for(C=0;C<256;++C)Pt.push(0);return Pt}(),O={0:0},a={},ot=0,ht=l.length;ot<ht;ot++)O[at=t[o=l[ot]]]==null&&(O[at]=++k),a[o]={old:t[o],new:O[t[o]]},B[o]=O[t[o]];return R.writeUInt16(1),R.writeUInt16(0),R.writeUInt32(12),R.writeUInt16(0),R.writeUInt16(262),R.writeUInt16(0),R.write(B),{charMap:a,subtable:R.data,maxGlyphID:k+1};case"unicode":for(j=[],v=[],E=0,O={},i={},P=d=null,nt=0,dt=l.length;nt<dt;nt++)O[st=t[o=l[nt]]]==null&&(O[st]=++E),i[o]={old:st,new:O[st]},h=O[st]-o,P!=null&&h===d||(P&&v.push(P),j.push(o),d=h),P=o;for(P&&v.push(P),v.push(65535),j.push(65535),pt=2*(rt=j.length),q=2*Math.pow(Math.log(rt)/Math.LN2,2),w=Math.log(q/2)/Math.LN2,Q=2*rt-q,c=[],yt=[],p=[],C=ut=0,It=j.length;ut<It;C=++ut){if(_=j[C],m=v[C],_===65535){c.push(0),yt.push(0);break}if(_-(V=i[_].new)>=32768)for(c.push(0),yt.push(2*(p.length+rt-C)),o=Z=_;_<=m?Z<=m:Z>=m;o=_<=m?++Z:--Z)p.push(i[o].new);else c.push(V-_),yt.push(0)}for(R.writeUInt16(3),R.writeUInt16(1),R.writeUInt32(12),R.writeUInt16(4),R.writeUInt16(16+8*rt+2*p.length),R.writeUInt16(0),R.writeUInt16(pt),R.writeUInt16(q),R.writeUInt16(w),R.writeUInt16(Q),et=0,N=v.length;et<N;et++)o=v[et],R.writeUInt16(o);for(R.writeUInt16(0),it=0,I=j.length;it<I;it++)o=j[it],R.writeUInt16(o);for(Nt=0,M=c.length;Nt<M;Nt++)h=c[Nt],R.writeUInt16(h);for(At=0,U=yt.length;At<U;At++)K=yt[At],R.writeUInt16(K);for(jt=0,Y=p.length;jt<Y;jt++)k=p[jt],R.writeUInt16(k);return{charMap:i,subtable:R.data,maxGlyphID:E+1}}},r}(),pu=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="cmap",t.prototype.parse=function(e){var i,o,a;for(e.pos=this.offset,this.version=e.readUInt16(),a=e.readUInt16(),this.tables=[],this.unicode=null,o=0;0<=a?o<a:o>a;o=0<=a?++o:--o)i=new zl(e,this.offset),this.tables.push(i),i.isUnicode&&this.unicode==null&&(this.unicode=i);return!0},t.encode=function(e,i){var o,a;return i==null&&(i="macroman"),o=zl.encode(e,i),(a=new On).writeUInt16(0),a.writeUInt16(1),o.table=a.data.concat(o.subtable),o},t}(),mc=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="hhea",t.prototype.parse=function(e){return e.pos=this.offset,this.version=e.readInt(),this.ascender=e.readShort(),this.decender=e.readShort(),this.lineGap=e.readShort(),this.advanceWidthMax=e.readShort(),this.minLeftSideBearing=e.readShort(),this.minRightSideBearing=e.readShort(),this.xMaxExtent=e.readShort(),this.caretSlopeRise=e.readShort(),this.caretSlopeRun=e.readShort(),this.caretOffset=e.readShort(),e.pos+=8,this.metricDataFormat=e.readShort(),this.numberOfMetrics=e.readUInt16()},t}(),vc=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="OS/2",t.prototype.parse=function(e){if(e.pos=this.offset,this.version=e.readUInt16(),this.averageCharWidth=e.readShort(),this.weightClass=e.readUInt16(),this.widthClass=e.readUInt16(),this.type=e.readShort(),this.ySubscriptXSize=e.readShort(),this.ySubscriptYSize=e.readShort(),this.ySubscriptXOffset=e.readShort(),this.ySubscriptYOffset=e.readShort(),this.ySuperscriptXSize=e.readShort(),this.ySuperscriptYSize=e.readShort(),this.ySuperscriptXOffset=e.readShort(),this.ySuperscriptYOffset=e.readShort(),this.yStrikeoutSize=e.readShort(),this.yStrikeoutPosition=e.readShort(),this.familyClass=e.readShort(),this.panose=function(){var i,o;for(o=[],i=0;i<10;++i)o.push(e.readByte());return o}(),this.charRange=function(){var i,o;for(o=[],i=0;i<4;++i)o.push(e.readInt());return o}(),this.vendorID=e.readString(4),this.selection=e.readShort(),this.firstCharIndex=e.readShort(),this.lastCharIndex=e.readShort(),this.version>0&&(this.ascent=e.readShort(),this.descent=e.readShort(),this.lineGap=e.readShort(),this.winAscent=e.readShort(),this.winDescent=e.readShort(),this.codePageRange=function(){var i,o;for(o=[],i=0;i<2;i=++i)o.push(e.readInt());return o}(),this.version>1))return this.xHeight=e.readShort(),this.capHeight=e.readShort(),this.defaultChar=e.readShort(),this.breakChar=e.readShort(),this.maxContext=e.readShort()},t}(),bc=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="post",t.prototype.parse=function(e){var i,o,a;switch(e.pos=this.offset,this.format=e.readInt(),this.italicAngle=e.readInt(),this.underlinePosition=e.readShort(),this.underlineThickness=e.readShort(),this.isFixedPitch=e.readInt(),this.minMemType42=e.readInt(),this.maxMemType42=e.readInt(),this.minMemType1=e.readInt(),this.maxMemType1=e.readInt(),this.format){case 65536:break;case 131072:var l;for(o=e.readUInt16(),this.glyphNameIndex=[],l=0;0<=o?l<o:l>o;l=0<=o?++l:--l)this.glyphNameIndex.push(e.readUInt16());for(this.names=[],a=[];e.pos<this.offset+this.length;)i=e.readByte(),a.push(this.names.push(e.readString(i)));return a;case 151552:return o=e.readUInt16(),this.offsets=e.read(o);case 196608:break;case 262144:return this.map=(function(){var h,c,d;for(d=[],l=h=0,c=this.file.maxp.numGlyphs;0<=c?h<c:h>c;l=0<=c?++h:--h)d.push(e.readUInt32());return d}).call(this)}},t}(),yc=function(r,t){this.raw=r,this.length=r.length,this.platformID=t.platformID,this.encodingID=t.encodingID,this.languageID=t.languageID},wc=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="name",t.prototype.parse=function(e){var i,o,a,l,h,c,d,m,v,w,p;for(e.pos=this.offset,e.readShort(),i=e.readShort(),c=e.readShort(),o=[],l=0;0<=i?l<i:l>i;l=0<=i?++l:--l)o.push({platformID:e.readShort(),encodingID:e.readShort(),languageID:e.readShort(),nameID:e.readShort(),length:e.readShort(),offset:this.offset+c+e.readShort()});for(d={},l=v=0,w=o.length;v<w;l=++v)a=o[l],e.pos=a.offset,m=e.readString(a.length),h=new yc(m,a),d[p=a.nameID]==null&&(d[p]=[]),d[a.nameID].push(h);this.strings=d,this.copyright=d[0],this.fontFamily=d[1],this.fontSubfamily=d[2],this.uniqueSubfamily=d[3],this.fontName=d[4],this.version=d[5];try{this.postscriptName=d[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch{this.postscriptName=d[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=d[7],this.manufacturer=d[8],this.designer=d[9],this.description=d[10],this.vendorUrl=d[11],this.designerUrl=d[12],this.license=d[13],this.licenseUrl=d[14],this.preferredFamily=d[15],this.preferredSubfamily=d[17],this.compatibleFull=d[18],this.sampleText=d[19]},t}(),xc=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="maxp",t.prototype.parse=function(e){return e.pos=this.offset,this.version=e.readInt(),this.numGlyphs=e.readUInt16(),this.maxPoints=e.readUInt16(),this.maxContours=e.readUInt16(),this.maxCompositePoints=e.readUInt16(),this.maxComponentContours=e.readUInt16(),this.maxZones=e.readUInt16(),this.maxTwilightPoints=e.readUInt16(),this.maxStorage=e.readUInt16(),this.maxFunctionDefs=e.readUInt16(),this.maxInstructionDefs=e.readUInt16(),this.maxStackElements=e.readUInt16(),this.maxSizeOfInstructions=e.readUInt16(),this.maxComponentElements=e.readUInt16(),this.maxComponentDepth=e.readUInt16()},t}(),Ac=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="hmtx",t.prototype.parse=function(e){var i,o,a,l,h,c,d;for(e.pos=this.offset,this.metrics=[],i=0,c=this.file.hhea.numberOfMetrics;0<=c?i<c:i>c;i=0<=c?++i:--i)this.metrics.push({advance:e.readUInt16(),lsb:e.readInt16()});for(a=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var m,v;for(v=[],i=m=0;0<=a?m<a:m>a;i=0<=a?++m:--m)v.push(e.readInt16());return v}(),this.widths=(function(){var m,v,w,p;for(p=[],m=0,v=(w=this.metrics).length;m<v;m++)l=w[m],p.push(l.advance);return p}).call(this),o=this.widths[this.widths.length-1],d=[],i=h=0;0<=a?h<a:h>a;i=0<=a?++h:--h)d.push(this.widths.push(o));return d},t.prototype.forGlyph=function(e){return e in this.metrics?this.metrics[e]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[e-this.metrics.length]}},t}(),gu=[].slice,Nc=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="glyf",t.prototype.parse=function(){return this.cache={}},t.prototype.glyphFor=function(e){var i,o,a,l,h,c,d,m,v,w;return e in this.cache?this.cache[e]:(l=this.file.loca,i=this.file.contents,o=l.indexOf(e),(a=l.lengthOf(e))===0?this.cache[e]=null:(i.pos=this.offset+o,h=(c=new On(i.read(a))).readShort(),m=c.readShort(),w=c.readShort(),d=c.readShort(),v=c.readShort(),this.cache[e]=h===-1?new Sc(c,m,w,d,v):new Lc(c,h,m,w,d,v),this.cache[e]))},t.prototype.encode=function(e,i,o){var a,l,h,c,d;for(h=[],l=[],c=0,d=i.length;c<d;c++)a=e[i[c]],l.push(h.length),a&&(h=h.concat(a.encode(o)));return l.push(h.length),{table:h,offsets:l}},t}(),Lc=function(){function r(t,e,i,o,a,l){this.raw=t,this.numberOfContours=e,this.xMin=i,this.yMin=o,this.xMax=a,this.yMax=l,this.compound=!1}return r.prototype.encode=function(){return this.raw.data},r}(),Sc=function(){function r(t,e,i,o,a){var l,h;for(this.raw=t,this.xMin=e,this.yMin=i,this.xMax=o,this.yMax=a,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],l=this.raw;h=l.readShort(),this.glyphOffsets.push(l.pos),this.glyphIDs.push(l.readUInt16()),32&h;)l.pos+=1&h?4:2,128&h?l.pos+=8:64&h?l.pos+=4:8&h&&(l.pos+=2)}return r.prototype.encode=function(){var t,e,i;for(e=new On(gu.call(this.raw.data)),t=0,i=this.glyphIDs.length;t<i;++t)e.pos=this.glyphOffsets[t];return e.data},r}(),Pc=function(r){function t(){return t.__super__.constructor.apply(this,arguments)}return en(t,zr),t.prototype.tag="loca",t.prototype.parse=function(e){var i,o;return e.pos=this.offset,i=this.file.head.indexToLocFormat,this.offsets=i===0?(function(){var a,l;for(l=[],o=0,a=this.length;o<a;o+=2)l.push(2*e.readUInt16());return l}).call(this):(function(){var a,l;for(l=[],o=0,a=this.length;o<a;o+=4)l.push(e.readUInt32());return l}).call(this)},t.prototype.indexOf=function(e){return this.offsets[e]},t.prototype.lengthOf=function(e){return this.offsets[e+1]-this.offsets[e]},t.prototype.encode=function(e,i){for(var o=new Uint32Array(this.offsets.length),a=0,l=0,h=0;h<o.length;++h)if(o[h]=a,l<i.length&&i[l]==h){++l,o[h]=a;var c=this.offsets[h],d=this.offsets[h+1]-c;d>0&&(a+=d)}for(var m=new Array(4*o.length),v=0;v<o.length;++v)m[4*v+3]=255&o[v],m[4*v+2]=(65280&o[v])>>8,m[4*v+1]=(16711680&o[v])>>16,m[4*v]=(**********&o[v])>>24;return m},t}(),_c=function(){function r(t){this.font=t,this.subset={},this.unicodes={},this.next=33}return r.prototype.generateCmap=function(){var t,e,i,o,a;for(e in o=this.font.cmap.tables[0].codeMap,t={},a=this.subset)i=a[e],t[e]=o[i];return t},r.prototype.glyphsFor=function(t){var e,i,o,a,l,h,c;for(o={},l=0,h=t.length;l<h;l++)o[a=t[l]]=this.font.glyf.glyphFor(a);for(a in e=[],o)(i=o[a])!=null&&i.compound&&e.push.apply(e,i.glyphIDs);if(e.length>0)for(a in c=this.glyphsFor(e))i=c[a],o[a]=i;return o},r.prototype.encode=function(t,e){var i,o,a,l,h,c,d,m,v,w,p,C,k,B,P;for(o in i=pu.encode(this.generateCmap(),"unicode"),l=this.glyphsFor(t),p={0:0},P=i.charMap)p[(c=P[o]).old]=c.new;for(C in w=i.maxGlyphID,l)C in p||(p[C]=w++);return m=function(O){var E,K;for(E in K={},O)K[O[E]]=E;return K}(p),v=Object.keys(m).sort(function(O,E){return O-E}),k=function(){var O,E,K;for(K=[],O=0,E=v.length;O<E;O++)h=v[O],K.push(m[h]);return K}(),a=this.font.glyf.encode(l,k,p),d=this.font.loca.encode(a.offsets,k),B={cmap:this.font.cmap.raw(),glyf:a.table,loca:d,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(e)},this.font.os2.exists&&(B["OS/2"]=this.font.os2.raw()),this.font.directory.encode(B)},r}();Ut.API.PDFObject=function(){var r;function t(){}return r=function(e,i){return(Array(i+1).join("0")+e).slice(-i)},t.convert=function(e){var i,o,a,l;if(Array.isArray(e))return"["+function(){var h,c,d;for(d=[],h=0,c=e.length;h<c;h++)i=e[h],d.push(t.convert(i));return d}().join(" ")+"]";if(typeof e=="string")return"/"+e;if(e!=null&&e.isString)return"("+e+")";if(e instanceof Date)return"(D:"+r(e.getUTCFullYear(),4)+r(e.getUTCMonth(),2)+r(e.getUTCDate(),2)+r(e.getUTCHours(),2)+r(e.getUTCMinutes(),2)+r(e.getUTCSeconds(),2)+"Z)";if({}.toString.call(e)==="[object Object]"){for(o in a=["<<"],e)l=e[o],a.push("/"+o+" "+t.convert(l));return a.push(">>"),a.join(`
`)}return""+e},t}();function mu(r,t,e,i,o){i=i||{};var a=1.15,l=o.internal.scaleFactor,h=o.internal.getFontSize()/l,c=o.getLineHeightFactor?o.getLineHeightFactor():a,d=h*c,m=/\r\n|\r|\n/g,v="",w=1;if((i.valign==="middle"||i.valign==="bottom"||i.halign==="center"||i.halign==="right")&&(v=typeof r=="string"?r.split(m):r,w=v.length||1),e+=h*(2-a),i.valign==="middle"?e-=w/2*d:i.valign==="bottom"&&(e-=w*d),i.halign==="center"||i.halign==="right"){var p=h;if(i.halign==="center"&&(p*=.5),v&&w>=1){for(var C=0;C<v.length;C++)o.text(v[C],t-o.getStringUnitWidth(v[C])*p,e),e+=d;return o}t-=o.getStringUnitWidth(r)*p}return i.halign==="justify"?o.text(r,t,e,{maxWidth:i.maxWidth||100,align:"justify"}):o.text(r,t,e),o}var Ul={},Ti=function(){function r(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return r.setDefaults=function(t,e){e===void 0&&(e=null),e?e.__autoTableDocumentDefaults=t:Ul=t},r.unifyColor=function(t){return Array.isArray(t)?t:typeof t=="number"?[t,t,t]:typeof t=="string"?[t]:null},r.prototype.applyStyles=function(t,e){var i,o,a;e===void 0&&(e=!1),t.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(t.fontStyle);var l=this.jsPDFDocument.internal.getFont(),h=l.fontStyle,c=l.fontName;if(t.font&&(c=t.font),t.fontStyle){h=t.fontStyle;var d=this.getFontList()[c];d&&d.indexOf(h)===-1&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(d[0]),h=d[0])}if(this.jsPDFDocument.setFont(c,h),t.fontSize&&this.jsPDFDocument.setFontSize(t.fontSize),!e){var m=r.unifyColor(t.fillColor);m&&(i=this.jsPDFDocument).setFillColor.apply(i,m),m=r.unifyColor(t.textColor),m&&(o=this.jsPDFDocument).setTextColor.apply(o,m),m=r.unifyColor(t.lineColor),m&&(a=this.jsPDFDocument).setDrawColor.apply(a,m),typeof t.lineWidth=="number"&&this.jsPDFDocument.setLineWidth(t.lineWidth)}},r.prototype.splitTextToSize=function(t,e,i){return this.jsPDFDocument.splitTextToSize(t,e,i)},r.prototype.rect=function(t,e,i,o,a){return this.jsPDFDocument.rect(t,e,i,o,a)},r.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},r.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},r.prototype.getDocument=function(){return this.jsPDFDocument},r.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},r.prototype.addPage=function(){return this.jsPDFDocument.addPage()},r.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},r.prototype.getGlobalOptions=function(){return Ul||{}},r.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},r.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return t.width==null&&(t={width:t.getWidth(),height:t.getHeight()}),t},r.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},r.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},r.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},r.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},r}(),Us=function(r,t){return Us=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(e[o]=i[o])},Us(r,t)};function vu(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Us(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var bu=function(r){vu(t,r);function t(e){var i=r.call(this)||this;return i._element=e,i}return t}(Array);function kc(r){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/r,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}}function jc(r){var t={striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}};return t[r]}function xo(r,t,e){e.applyStyles(t,!0);var i=Array.isArray(r)?r:[r],o=i.map(function(a){return e.getTextWidth(a)}).reduce(function(a,l){return Math.max(a,l)},0);return o}function yu(r,t,e,i){var o=t.settings.tableLineWidth,a=t.settings.tableLineColor;r.applyStyles({lineWidth:o,lineColor:a});var l=wu(o,!1);l&&r.rect(e.x,e.y,t.getWidth(r.pageSize().width),i.y-e.y,l)}function wu(r,t){var e=r>0,i=t||t===0;return e&&i?"DF":e?"S":i?"F":null}function Po(r,t){var e,i,o,a;if(r=r||t,Array.isArray(r)){if(r.length>=4)return{top:r[0],right:r[1],bottom:r[2],left:r[3]};if(r.length===3)return{top:r[0],right:r[1],bottom:r[2],left:r[1]};if(r.length===2)return{top:r[0],right:r[1],bottom:r[0],left:r[1]};r.length===1?r=r[0]:r=t}return typeof r=="object"?(typeof r.vertical=="number"&&(r.top=r.vertical,r.bottom=r.vertical),typeof r.horizontal=="number"&&(r.right=r.horizontal,r.left=r.horizontal),{left:(e=r.left)!==null&&e!==void 0?e:t,top:(i=r.top)!==null&&i!==void 0?i:t,right:(o=r.right)!==null&&o!==void 0?o:t,bottom:(a=r.bottom)!==null&&a!==void 0?a:t}):(typeof r!="number"&&(r=t),{top:r,right:r,bottom:r,left:r})}function xu(r,t){var e=Po(t.settings.margin,0);return r.pageSize().width-(e.left+e.right)}function Cc(r,t,e,i,o){var a={},l=1.3333333333333333,h=Ps(t,function(E){return o.getComputedStyle(E).backgroundColor});h!=null&&(a.fillColor=h);var c=Ps(t,function(E){return o.getComputedStyle(E).color});c!=null&&(a.textColor=c);var d=Ic(i,e);d&&(a.cellPadding=d);var m="borderTopColor",v=l*e,w=i.borderTopWidth;if(i.borderBottomWidth===w&&i.borderRightWidth===w&&i.borderLeftWidth===w){var p=(parseFloat(w)||0)/v;p&&(a.lineWidth=p)}else a.lineWidth={top:(parseFloat(i.borderTopWidth)||0)/v,right:(parseFloat(i.borderRightWidth)||0)/v,bottom:(parseFloat(i.borderBottomWidth)||0)/v,left:(parseFloat(i.borderLeftWidth)||0)/v},a.lineWidth.top||(a.lineWidth.right?m="borderRightColor":a.lineWidth.bottom?m="borderBottomColor":a.lineWidth.left&&(m="borderLeftColor"));var C=Ps(t,function(E){return o.getComputedStyle(E)[m]});C!=null&&(a.lineColor=C);var k=["left","right","center","justify"];k.indexOf(i.textAlign)!==-1&&(a.halign=i.textAlign),k=["middle","bottom","top"],k.indexOf(i.verticalAlign)!==-1&&(a.valign=i.verticalAlign);var B=parseInt(i.fontSize||"");isNaN(B)||(a.fontSize=B/l);var P=Fc(i);P&&(a.fontStyle=P);var O=(i.fontFamily||"").toLowerCase();return r.indexOf(O)!==-1&&(a.font=O),a}function Fc(r){var t="";return(r.fontWeight==="bold"||r.fontWeight==="bolder"||parseInt(r.fontWeight)>=700)&&(t="bold"),(r.fontStyle==="italic"||r.fontStyle==="oblique")&&(t+="italic"),t}function Ps(r,t){var e=Au(r,t);if(!e)return null;var i=e.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!i||!Array.isArray(i))return null;var o=[parseInt(i[1]),parseInt(i[2]),parseInt(i[3])],a=parseInt(i[4]);return a===0||isNaN(o[0])||isNaN(o[1])||isNaN(o[2])?null:o}function Au(r,t){var e=t(r);return e==="rgba(0, 0, 0, 0)"||e==="transparent"||e==="initial"||e==="inherit"?r.parentElement==null?null:Au(r.parentElement,t):e}function Ic(r,t){var e=[r.paddingTop,r.paddingRight,r.paddingBottom,r.paddingLeft],i=96/(72/t),o=(parseInt(r.lineHeight)-parseInt(r.fontSize))/t/2,a=e.map(function(h){return parseInt(h||"0")/i}),l=Po(a,0);return o>l.top&&(l.top=o),o>l.bottom&&(l.bottom=o),l}function Nu(r,t,e,i,o){var a,l;i===void 0&&(i=!1),o===void 0&&(o=!1);var h;typeof t=="string"?h=e.document.querySelector(t):h=t;var c=Object.keys(r.getFontList()),d=r.scaleFactor(),m=[],v=[],w=[];if(!h)return console.error("Html table could not be found with input: ",t),{head:m,body:v,foot:w};for(var p=0;p<h.rows.length;p++){var C=h.rows[p],k=(l=(a=C==null?void 0:C.parentElement)===null||a===void 0?void 0:a.tagName)===null||l===void 0?void 0:l.toLowerCase(),B=Dc(c,d,e,C,i,o);B&&(k==="thead"?m.push(B):k==="tfoot"?w.push(B):v.push(B))}return{head:m,body:v,foot:w}}function Dc(r,t,e,i,o,a){for(var l=new bu(i),h=0;h<i.cells.length;h++){var c=i.cells[h],d=e.getComputedStyle(c);if(o||d.display!=="none"){var m=void 0;a&&(m=Cc(r,c,t,d,e)),l.push({rowSpan:c.rowSpan,colSpan:c.colSpan,styles:m,_element:c,content:Bc(c)})}}var v=e.getComputedStyle(i);if(l.length>0&&(o||v.display!=="none"))return l}function Bc(r){var t=r.cloneNode(!0);return t.innerHTML=t.innerHTML.replace(/\n/g,"").replace(/ +/g," "),t.innerHTML=t.innerHTML.split(/<br.*?>/).map(function(e){return e.trim()}).join(`
`),t.innerText||t.textContent||""}function Oc(r,t,e){for(var i=0,o=[r,t,e];i<o.length;i++){var a=o[i];a&&typeof a!="object"&&console.error("The options parameter should be of type object, is: "+typeof a),a.startY&&typeof a.startY!="number"&&(console.error("Invalid value for startY option",a.startY),delete a.startY)}}function gn(r,t,e,i,o){if(r==null)throw new TypeError("Cannot convert undefined or null to object");for(var a=Object(r),l=1;l<arguments.length;l++){var h=arguments[l];if(h!=null)for(var c in h)Object.prototype.hasOwnProperty.call(h,c)&&(a[c]=h[c])}return a}function Lu(r,t){var e=new Ti(r),i=e.getDocumentOptions(),o=e.getGlobalOptions();Oc(o,i,t);var a=gn({},o,i,t),l;typeof window<"u"&&(l=window);var h=Mc(o,i,t),c=Tc(o,i,t),d=Ec(e,a),m=qc(e,a,l);return{id:t.tableId,content:m,hooks:c,styles:h,settings:d}}function Mc(r,t,e){for(var i={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},o=function(c){if(c==="columnStyles"){var d=r[c],m=t[c],v=e[c];i.columnStyles=gn({},d,m,v)}else{var w=[r,t,e],p=w.map(function(C){return C[c]||{}});i[c]=gn({},p[0],p[1],p[2])}},a=0,l=Object.keys(i);a<l.length;a++){var h=l[a];o(h)}return i}function Tc(r,t,e){for(var i=[r,t,e],o={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},a=0,l=i;a<l.length;a++){var h=l[a];h.didParseCell&&o.didParseCell.push(h.didParseCell),h.willDrawCell&&o.willDrawCell.push(h.willDrawCell),h.didDrawCell&&o.didDrawCell.push(h.didDrawCell),h.willDrawPage&&o.willDrawPage.push(h.willDrawPage),h.didDrawPage&&o.didDrawPage.push(h.didDrawPage)}return o}function Ec(r,t){var e,i,o,a,l,h,c,d,m,v,w,p,C=Po(t.margin,40/r.scaleFactor()),k=(e=Rc(r,t.startY))!==null&&e!==void 0?e:C.top,B;t.showFoot===!0?B="everyPage":t.showFoot===!1?B="never":B=(i=t.showFoot)!==null&&i!==void 0?i:"everyPage";var P;t.showHead===!0?P="everyPage":t.showHead===!1?P="never":P=(o=t.showHead)!==null&&o!==void 0?o:"everyPage";var O=(a=t.useCss)!==null&&a!==void 0?a:!1,E=t.theme||(O?"plain":"striped"),K=!!t.horizontalPageBreak,st=(l=t.horizontalPageBreakRepeat)!==null&&l!==void 0?l:null;return{includeHiddenHtml:(h=t.includeHiddenHtml)!==null&&h!==void 0?h:!1,useCss:O,theme:E,startY:k,margin:C,pageBreak:(c=t.pageBreak)!==null&&c!==void 0?c:"auto",rowPageBreak:(d=t.rowPageBreak)!==null&&d!==void 0?d:"auto",tableWidth:(m=t.tableWidth)!==null&&m!==void 0?m:"auto",showHead:P,showFoot:B,tableLineWidth:(v=t.tableLineWidth)!==null&&v!==void 0?v:0,tableLineColor:(w=t.tableLineColor)!==null&&w!==void 0?w:200,horizontalPageBreak:K,horizontalPageBreakRepeat:st,horizontalPageBreakBehaviour:(p=t.horizontalPageBreakBehaviour)!==null&&p!==void 0?p:"afterAllRows"}}function Rc(r,t){var e=r.getLastAutoTable(),i=r.scaleFactor(),o=r.pageNumber(),a=!1;if(e&&e.startPageNumber){var l=e.startPageNumber+e.pageNumber-1;a=l===o}return typeof t=="number"?t:(t==null||t===!1)&&a&&(e==null?void 0:e.finalY)!=null?e.finalY+20/i:null}function qc(r,t,e){var i=t.head||[],o=t.body||[],a=t.foot||[];if(t.html){var l=t.includeHiddenHtml;if(e){var h=Nu(r,t.html,e,l,t.useCss)||{};i=h.head||i,o=h.body||i,a=h.foot||i}else console.error("Cannot parse html in non browser environment")}var c=t.columns||zc(i,o,a);return{columns:c,head:i,body:o,foot:a}}function zc(r,t,e){var i=r[0]||t[0]||e[0]||[],o=[];return Object.keys(i).filter(function(a){return a!=="_element"}).forEach(function(a){var l=1,h;Array.isArray(i)?h=i[parseInt(a)]:h=i[a],typeof h=="object"&&!Array.isArray(h)&&(l=(h==null?void 0:h.colSpan)||1);for(var c=0;c<l;c++){var d=void 0;Array.isArray(i)?d=o.length:d=a+(c>0?"_".concat(c):"");var m={dataKey:d};o.push(m)}}),o}var Hs=function(){function r(t,e,i){this.table=e,this.pageNumber=e.pageNumber,this.settings=e.settings,this.cursor=i,this.doc=t.getDocument()}return r}(),Uc=function(r){vu(t,r);function t(e,i,o,a,l,h){var c=r.call(this,e,i,h)||this;return c.cell=o,c.row=a,c.column=l,c.section=a.section,c}return t}(Hs),Hc=function(){function r(t,e){this.pageNumber=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=e.columns,this.head=e.head,this.body=e.body,this.foot=e.foot}return r.prototype.getHeadHeight=function(t){return this.head.reduce(function(e,i){return e+i.getMaxCellHeight(t)},0)},r.prototype.getFootHeight=function(t){return this.foot.reduce(function(e,i){return e+i.getMaxCellHeight(t)},0)},r.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},r.prototype.callCellHooks=function(t,e,i,o,a,l){for(var h=0,c=e;h<c.length;h++){var d=c[h],m=new Uc(t,this,i,o,a,l),v=d(m)===!1;if(i.text=Array.isArray(i.text)?i.text:[i.text],v)return!1}return!0},r.prototype.callEndPageHooks=function(t,e){t.applyStyles(t.userStyles);for(var i=0,o=this.hooks.didDrawPage;i<o.length;i++){var a=o[i];a(new Hs(t,this,e))}},r.prototype.callWillDrawPageHooks=function(t,e){for(var i=0,o=this.hooks.willDrawPage;i<o.length;i++){var a=o[i];a(new Hs(t,this,e))}},r.prototype.getWidth=function(t){if(typeof this.settings.tableWidth=="number")return this.settings.tableWidth;if(this.settings.tableWidth==="wrap"){var e=this.columns.reduce(function(o,a){return o+a.wrappedWidth},0);return e}else{var i=this.settings.margin;return t-i.left-i.right}},r}(),Su=function(){function r(t,e,i,o,a){a===void 0&&(a=!1),this.height=0,this.raw=t,t instanceof bu&&(this.raw=t._element,this.element=t._element),this.index=e,this.section=i,this.cells=o,this.spansMultiplePages=a}return r.prototype.getMaxCellHeight=function(t){var e=this;return t.reduce(function(i,o){var a;return Math.max(i,((a=e.cells[o.index])===null||a===void 0?void 0:a.height)||0)},0)},r.prototype.hasRowSpan=function(t){var e=this;return t.filter(function(i){var o=e.cells[i.index];return o?o.rowSpan>1:!1}).length>0},r.prototype.canEntireRowFit=function(t,e){return this.getMaxCellHeight(e)<=t},r.prototype.getMinimumRowHeight=function(t,e){var i=this;return t.reduce(function(o,a){var l=i.cells[a.index];if(!l)return 0;var h=e.getLineHeight(l.styles.fontSize),c=l.padding("vertical"),d=c+h;return d>o?d:o},0)},r}(),Pu=function(){function r(t,e,i){var o;this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=e,this.section=i,this.raw=t;var a=t;t!=null&&typeof t=="object"&&!Array.isArray(t)?(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,a=(o=t.content)!==null&&o!==void 0?o:t,t._element&&(this.raw=t._element)):(this.rowSpan=1,this.colSpan=1);var l=a!=null?""+a:"",h=/\r\n|\r|\n/g;this.text=l.split(h)}return r.prototype.getTextPos=function(){var t;if(this.styles.valign==="top")t=this.y+this.padding("top");else if(this.styles.valign==="bottom")t=this.y+this.height-this.padding("bottom");else{var e=this.height-this.padding("vertical");t=this.y+e/2+this.padding("top")}var i;if(this.styles.halign==="right")i=this.x+this.width-this.padding("right");else if(this.styles.halign==="center"){var o=this.width-this.padding("horizontal");i=this.x+o/2+this.padding("left")}else i=this.x+this.padding("left");return{x:i,y:t}},r.prototype.getContentHeight=function(t,e){e===void 0&&(e=1.15);var i=Array.isArray(this.text)?this.text.length:1,o=this.styles.fontSize/t*e,a=i*o+this.padding("vertical");return Math.max(a,this.styles.minCellHeight)},r.prototype.padding=function(t){var e=Po(this.styles.cellPadding,0);return t==="vertical"?e.top+e.bottom:t==="horizontal"?e.left+e.right:e[t]},r}(),Wc=function(){function r(t,e,i){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=e,this.index=i}return r.prototype.getMaxCustomCellWidth=function(t){for(var e=0,i=0,o=t.allRows();i<o.length;i++){var a=o[i],l=a.cells[this.index];l&&typeof l.styles.cellWidth=="number"&&(e=Math.max(e,l.styles.cellWidth))}return e},r}();function Vc(r,t){Gc(r,t);var e=[],i=0;t.columns.forEach(function(a){var l=a.getMaxCustomCellWidth(t);l?a.width=l:(a.width=a.wrappedWidth,e.push(a)),i+=a.width});var o=t.getWidth(r.pageSize().width)-i;o&&(o=Ws(e,o,function(a){return Math.max(a.minReadableWidth,a.minWidth)})),o&&(o=Ws(e,o,function(a){return a.minWidth})),o=Math.abs(o),!t.settings.horizontalPageBreak&&o>.1/r.scaleFactor()&&(o=o<1?o:Math.round(o),console.warn("Of the table content, ".concat(o," units width could not fit page"))),Yc(t),Kc(t,r),Jc(t)}function Gc(r,t){var e=r.scaleFactor(),i=t.settings.horizontalPageBreak,o=xu(r,t);t.allRows().forEach(function(a){for(var l=0,h=t.columns;l<h.length;l++){var c=h[l],d=a.cells[c.index];if(d){var m=t.hooks.didParseCell;t.callCellHooks(r,m,d,a,c,null);var v=d.padding("horizontal");d.contentWidth=xo(d.text,d.styles,r)+v;var w=xo(d.text.join(" ").split(/[^\S\u00A0]+/),d.styles,r);if(d.minReadableWidth=w+d.padding("horizontal"),typeof d.styles.cellWidth=="number")d.minWidth=d.styles.cellWidth,d.wrappedWidth=d.styles.cellWidth;else if(d.styles.cellWidth==="wrap"||i===!0)d.contentWidth>o?(d.minWidth=o,d.wrappedWidth=o):(d.minWidth=d.contentWidth,d.wrappedWidth=d.contentWidth);else{var p=10/e;d.minWidth=d.styles.minCellWidth||p,d.wrappedWidth=d.contentWidth,d.minWidth>d.wrappedWidth&&(d.wrappedWidth=d.minWidth)}}}}),t.allRows().forEach(function(a){for(var l=0,h=t.columns;l<h.length;l++){var c=h[l],d=a.cells[c.index];if(d&&d.colSpan===1)c.wrappedWidth=Math.max(c.wrappedWidth,d.wrappedWidth),c.minWidth=Math.max(c.minWidth,d.minWidth),c.minReadableWidth=Math.max(c.minReadableWidth,d.minReadableWidth);else{var m=t.styles.columnStyles[c.dataKey]||t.styles.columnStyles[c.index]||{},v=m.cellWidth||m.minCellWidth;v&&typeof v=="number"&&(c.minWidth=v,c.wrappedWidth=v)}d&&(d.colSpan>1&&!c.minWidth&&(c.minWidth=d.minWidth),d.colSpan>1&&!c.wrappedWidth&&(c.wrappedWidth=d.minWidth))}})}function Ws(r,t,e){for(var i=t,o=r.reduce(function(p,C){return p+C.wrappedWidth},0),a=0;a<r.length;a++){var l=r[a],h=l.wrappedWidth/o,c=i*h,d=l.width+c,m=e(l),v=d<m?m:d;t-=v-l.width,l.width=v}if(t=Math.round(t*1e10)/1e10,t){var w=r.filter(function(p){return t<0?p.width>e(p):!0});w.length&&(t=Ws(w,t,e))}return t}function Jc(r){for(var t={},e=1,i=r.allRows(),o=0;o<i.length;o++)for(var a=i[o],l=0,h=r.columns;l<h.length;l++){var c=h[l],d=t[c.index];if(e>1)e--,delete a.cells[c.index];else if(d)d.cell.height+=a.height,e=d.cell.colSpan,delete a.cells[c.index],d.left--,d.left<=1&&delete t[c.index];else{var m=a.cells[c.index];if(!m)continue;if(m.height=a.height,m.rowSpan>1){var v=i.length-o,w=m.rowSpan>v?v:m.rowSpan;t[c.index]={cell:m,left:w,row:a}}}}}function Yc(r){for(var t=r.allRows(),e=0;e<t.length;e++)for(var i=t[e],o=null,a=0,l=0,h=0;h<r.columns.length;h++){var c=r.columns[h];if(l-=1,l>1&&r.columns[h+1])a+=c.width,delete i.cells[c.index];else if(o){var d=o;delete i.cells[c.index],o=null,d.width=c.width+a}else{var d=i.cells[c.index];if(!d)continue;if(l=d.colSpan,a=0,d.colSpan>1){o=d,a+=c.width;continue}d.width=c.width+a}}}function Kc(r,t){for(var e={count:0,height:0},i=0,o=r.allRows();i<o.length;i++){for(var a=o[i],l=0,h=r.columns;l<h.length;l++){var c=h[l],d=a.cells[c.index];if(d){t.applyStyles(d.styles,!0);var m=d.width-d.padding("horizontal");if(d.styles.overflow==="linebreak")d.text=t.splitTextToSize(d.text,m+1/t.scaleFactor(),{fontSize:d.styles.fontSize});else if(d.styles.overflow==="ellipsize")d.text=Hl(d.text,m,d.styles,t,"...");else if(d.styles.overflow==="hidden")d.text=Hl(d.text,m,d.styles,t,"");else if(typeof d.styles.overflow=="function"){var v=d.styles.overflow(d.text,m);typeof v=="string"?d.text=[v]:d.text=v}d.contentHeight=d.getContentHeight(t.scaleFactor(),t.getLineHeightFactor());var w=d.contentHeight/d.rowSpan;d.rowSpan>1&&e.count*e.height<w*d.rowSpan?e={height:w,count:d.rowSpan}:e&&e.count>0&&e.height>w&&(w=e.height),w>a.height&&(a.height=w)}}e.count--}}function Hl(r,t,e,i,o){return r.map(function(a){return Xc(a,t,e,i,o)})}function Xc(r,t,e,i,o){var a=1e4*i.scaleFactor();if(t=Math.ceil(t*a)/a,t>=xo(r,e,i))return r;for(;t<xo(r+o,e,i)&&!(r.length<=1);)r=r.substring(0,r.length-1);return r.trim()+o}function _u(r,t){var e=new Ti(r),i=Qc(t,e.scaleFactor()),o=new Hc(t,i);return Vc(e,o),e.applyStyles(e.userStyles),o}function Qc(r,t){var e=r.content,i=$c(e.columns);if(e.head.length===0){var o=Wl(i,"head");o&&e.head.push(o)}if(e.foot.length===0){var o=Wl(i,"foot");o&&e.foot.push(o)}var a=r.settings.theme,l=r.styles;return{columns:i,head:_s("head",e.head,i,l,a,t),body:_s("body",e.body,i,l,a,t),foot:_s("foot",e.foot,i,l,a,t)}}function _s(r,t,e,i,o,a){var l={},h=t.map(function(c,d){for(var m=0,v={},w=0,p=0,C=0,k=e;C<k.length;C++){var B=k[C];if(l[B.index]==null||l[B.index].left===0)if(p===0){var P=void 0;Array.isArray(c)?P=c[B.index-w-m]:P=c[B.dataKey];var O={};typeof P=="object"&&!Array.isArray(P)&&(O=(P==null?void 0:P.styles)||{});var E=tf(r,B,d,o,i,a,O),K=new Pu(P,E,r);v[B.dataKey]=K,v[B.index]=K,p=K.colSpan-1,l[B.index]={left:K.rowSpan-1,times:p}}else p--,w++;else l[B.index].left--,p=l[B.index].times,m++}return new Su(c,d,r,v)});return h}function Wl(r,t){var e={};return r.forEach(function(i){if(i.raw!=null){var o=Zc(t,i.raw);o!=null&&(e[i.dataKey]=o)}}),Object.keys(e).length>0?e:null}function Zc(r,t){if(r==="head"){if(typeof t=="object")return t.header||null;if(typeof t=="string"||typeof t=="number")return t}else if(r==="foot"&&typeof t=="object")return t.footer;return null}function $c(r){return r.map(function(t,e){var i,o;return typeof t=="object"?o=(i=t.dataKey)!==null&&i!==void 0?i:e:o=e,new Wc(o,t,e)})}function tf(r,t,e,i,o,a,l){var h=jc(i),c;r==="head"?c=o.headStyles:r==="body"?c=o.bodyStyles:r==="foot"&&(c=o.footStyles);var d=gn({},h.table,h[r],o.styles,c),m=o.columnStyles[t.dataKey]||o.columnStyles[t.index]||{},v=r==="body"?m:{},w=r==="body"&&e%2===0?gn({},h.alternateRow,o.alternateRowStyles):{},p=kc(a),C=gn({},p,d,w,v);return gn(C,l)}function ef(r,t,e){var i;e===void 0&&(e={});var o=xu(r,t),a=new Map,l=[],h=[],c=[];Array.isArray(t.settings.horizontalPageBreakRepeat)?c=t.settings.horizontalPageBreakRepeat:(typeof t.settings.horizontalPageBreakRepeat=="string"||typeof t.settings.horizontalPageBreakRepeat=="number")&&(c=[t.settings.horizontalPageBreakRepeat]),c.forEach(function(w){var p=t.columns.find(function(C){return C.dataKey===w||C.index===w});p&&!a.has(p.index)&&(a.set(p.index,!0),l.push(p.index),h.push(t.columns[p.index]),o-=p.wrappedWidth)});for(var d=!0,m=(i=e==null?void 0:e.start)!==null&&i!==void 0?i:0;m<t.columns.length;){if(a.has(m)){m++;continue}var v=t.columns[m].wrappedWidth;if(d||o>=v)d=!1,l.push(m),h.push(t.columns[m]),o-=v;else break;m++}return{colIndexes:l,columns:h,lastIndex:m-1}}function rf(r,t){for(var e=[],i=0;i<t.columns.length;i++){var o=ef(r,t,{start:i});o.columns.length&&(e.push(o),i=o.lastIndex)}return e}function ku(r,t){var e=t.settings,i=e.startY,o=e.margin,a={x:o.left,y:i},l=t.getHeadHeight(t.columns)+t.getFootHeight(t.columns),h=i+o.bottom+l;if(e.pageBreak==="avoid"){var c=t.body,d=c.reduce(function(w,p){return w+p.height},0);h+=d}var m=new Ti(r);(e.pageBreak==="always"||e.startY!=null&&h>m.pageSize().height)&&(Cu(m),a.y=o.top),t.callWillDrawPageHooks(m,a);var v=gn({},a);t.startPageNumber=m.pageNumber(),e.horizontalPageBreak?nf(m,t,v,a):(m.applyStyles(m.userStyles),(e.showHead==="firstPage"||e.showHead==="everyPage")&&t.head.forEach(function(w){return mn(m,t,w,a,t.columns)}),m.applyStyles(m.userStyles),t.body.forEach(function(w,p){var C=p===t.body.length-1;Ao(m,t,w,C,v,a,t.columns)}),m.applyStyles(m.userStyles),(e.showFoot==="lastPage"||e.showFoot==="everyPage")&&t.foot.forEach(function(w){return mn(m,t,w,a,t.columns)})),yu(m,t,v,a),t.callEndPageHooks(m,a),t.finalY=a.y,r.lastAutoTable=t,m.applyStyles(m.userStyles)}function nf(r,t,e,i){var o=rf(r,t),a=t.settings;if(a.horizontalPageBreakBehaviour==="afterAllRows")o.forEach(function(d,m){r.applyStyles(r.userStyles),m>0?va(r,t,e,i,d.columns,!0):Vl(r,t,i,d.columns),af(r,t,e,i,d.columns),ks(r,t,i,d.columns)});else for(var l=-1,h=o[0],c=function(){var d=l;if(h){r.applyStyles(r.userStyles);var m=h.columns;l>=0?va(r,t,e,i,m,!0):Vl(r,t,i,m),d=Gl(r,t,l+1,i,m),ks(r,t,i,m)}var v=d-l;o.slice(1).forEach(function(w){r.applyStyles(r.userStyles),va(r,t,e,i,w.columns,!0),Gl(r,t,l+1,i,w.columns,v),ks(r,t,i,w.columns)}),l=d};l<t.body.length-1;)c()}function Vl(r,t,e,i){var o=t.settings;r.applyStyles(r.userStyles),(o.showHead==="firstPage"||o.showHead==="everyPage")&&t.head.forEach(function(a){return mn(r,t,a,e,i)})}function af(r,t,e,i,o){r.applyStyles(r.userStyles),t.body.forEach(function(a,l){var h=l===t.body.length-1;Ao(r,t,a,h,e,i,o)})}function Gl(r,t,e,i,o,a){r.applyStyles(r.userStyles),a=a??t.body.length;var l=Math.min(e+a,t.body.length),h=-1;return t.body.slice(e,l).forEach(function(c,d){var m=e+d===t.body.length-1,v=ju(r,t,m,i);c.canEntireRowFit(v,o)&&(mn(r,t,c,i,o),h=e+d)}),h}function ks(r,t,e,i){var o=t.settings;r.applyStyles(r.userStyles),(o.showFoot==="lastPage"||o.showFoot==="everyPage")&&t.foot.forEach(function(a){return mn(r,t,a,e,i)})}function of(r,t,e){var i=e.getLineHeight(r.styles.fontSize),o=r.padding("vertical"),a=Math.floor((t-o)/i);return Math.max(0,a)}function sf(r,t,e,i){var o={};r.spansMultiplePages=!0,r.height=0;for(var a=0,l=0,h=e.columns;l<h.length;l++){var c=h[l],d=r.cells[c.index];if(d){Array.isArray(d.text)||(d.text=[d.text]);var m=new Pu(d.raw,d.styles,d.section);m=gn(m,d),m.text=[];var v=of(d,t,i);d.text.length>v&&(m.text=d.text.splice(v,d.text.length));var w=i.scaleFactor(),p=i.getLineHeightFactor();d.contentHeight=d.getContentHeight(w,p),d.contentHeight>=t&&(d.contentHeight=t,m.styles.minCellHeight-=t),d.contentHeight>r.height&&(r.height=d.contentHeight),m.contentHeight=m.getContentHeight(w,p),m.contentHeight>a&&(a=m.contentHeight),o[c.index]=m}}var C=new Su(r.raw,-1,r.section,o,!0);C.height=a;for(var k=0,B=e.columns;k<B.length;k++){var c=B[k],m=C.cells[c.index];m&&(m.height=C.height);var d=r.cells[c.index];d&&(d.height=r.height)}return C}function lf(r,t,e,i){var o=r.pageSize().height,a=i.settings.margin,l=a.top+a.bottom,h=o-l;t.section==="body"&&(h-=i.getHeadHeight(i.columns)+i.getFootHeight(i.columns));var c=t.getMinimumRowHeight(i.columns,r),d=c<e;if(c>h)return console.error("Will not be able to print row ".concat(t.index," correctly since it's minimum height is larger than page height")),!0;if(!d)return!1;var m=t.hasRowSpan(i.columns),v=t.getMaxCellHeight(i.columns)>h;return v?(m&&console.error("The content of row ".concat(t.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!(m||i.settings.rowPageBreak==="avoid")}function Ao(r,t,e,i,o,a,l){var h=ju(r,t,i,a);if(e.canEntireRowFit(h,l))mn(r,t,e,a,l);else if(lf(r,e,h,t)){var c=sf(e,h,t,r);mn(r,t,e,a,l),va(r,t,o,a,l),Ao(r,t,c,i,o,a,l)}else va(r,t,o,a,l),Ao(r,t,e,i,o,a,l)}function mn(r,t,e,i,o){i.x=t.settings.margin.left;for(var a=0,l=o;a<l.length;a++){var h=l[a],c=e.cells[h.index];if(!c){i.x+=h.width;continue}r.applyStyles(c.styles),c.x=i.x,c.y=i.y;var d=t.callCellHooks(r,t.hooks.willDrawCell,c,e,h,i);if(d===!1){i.x+=h.width;continue}uf(r,c,i);var m=c.getTextPos();mu(c.text,m.x,m.y,{halign:c.styles.halign,valign:c.styles.valign,maxWidth:Math.ceil(c.width-c.padding("left")-c.padding("right"))},r.getDocument()),t.callCellHooks(r,t.hooks.didDrawCell,c,e,h,i),i.x+=h.width}i.y+=e.height}function uf(r,t,e){var i=t.styles;if(r.getDocument().setFillColor(r.getDocument().getFillColor()),typeof i.lineWidth=="number"){var o=wu(i.lineWidth,i.fillColor);o&&r.rect(t.x,e.y,t.width,t.height,o)}else typeof i.lineWidth=="object"&&(i.fillColor&&r.rect(t.x,e.y,t.width,t.height,"F"),hf(r,t,e,i.lineWidth))}function hf(r,t,e,i){var o,a,l,h;i.top&&(o=e.x,a=e.y,l=e.x+t.width,h=e.y,i.right&&(l+=.5*i.right),i.left&&(o-=.5*i.left),c(i.top,o,a,l,h)),i.bottom&&(o=e.x,a=e.y+t.height,l=e.x+t.width,h=e.y+t.height,i.right&&(l+=.5*i.right),i.left&&(o-=.5*i.left),c(i.bottom,o,a,l,h)),i.left&&(o=e.x,a=e.y,l=e.x,h=e.y+t.height,i.top&&(a-=.5*i.top),i.bottom&&(h+=.5*i.bottom),c(i.left,o,a,l,h)),i.right&&(o=e.x+t.width,a=e.y,l=e.x+t.width,h=e.y+t.height,i.top&&(a-=.5*i.top),i.bottom&&(h+=.5*i.bottom),c(i.right,o,a,l,h));function c(d,m,v,w,p){r.getDocument().setLineWidth(d),r.getDocument().line(m,v,w,p,"S")}}function ju(r,t,e,i){var o=t.settings.margin.bottom,a=t.settings.showFoot;return(a==="everyPage"||a==="lastPage"&&e)&&(o+=t.getFootHeight(t.columns)),r.pageSize().height-i.y-o}function va(r,t,e,i,o,a){o===void 0&&(o=[]),a===void 0&&(a=!1),r.applyStyles(r.userStyles),t.settings.showFoot==="everyPage"&&!a&&t.foot.forEach(function(h){return mn(r,t,h,i,o)}),t.callEndPageHooks(r,i);var l=t.settings.margin;yu(r,t,e,i),Cu(r),t.pageNumber++,i.x=l.left,i.y=l.top,e.y=l.top,t.callWillDrawPageHooks(r,i),t.settings.showHead==="everyPage"&&(t.head.forEach(function(h){return mn(r,t,h,i,o)}),r.applyStyles(r.userStyles))}function Cu(r){var t=r.pageNumber();r.setPage(t+1);var e=r.pageNumber();return e===t?(r.addPage(),!0):!1}function cf(r){r.API.autoTable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var i=t[0],o=Lu(this,i),a=_u(this,o);return ku(this,a),this},r.API.lastAutoTable=!1,r.API.autoTableText=function(t,e,i,o){mu(t,e,i,o,this)},r.API.autoTableSetDefaults=function(t){return Ti.setDefaults(t,this),this},r.autoTableSetDefaults=function(t,e){Ti.setDefaults(t,e)},r.API.autoTableHtmlToJson=function(t,e){var i;if(e===void 0&&(e=!1),typeof window>"u")return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var o=new Ti(this),a=Nu(o,t,window,e,!1),l=a.head,h=a.body,c=((i=l[0])===null||i===void 0?void 0:i.map(function(d){return d.content}))||[];return{columns:c,rows:h,data:h}}}var js;function ff(r,t){var e=Lu(r,t),i=_u(r,e);ku(r,i)}try{if(typeof window<"u"&&window){var Jl=window,Yl=Jl.jsPDF||((js=Jl.jspdf)===null||js===void 0?void 0:js.jsPDF);Yl&&cf(Yl)}}catch(r){console.error("Could not apply autoTable plugin",r)}const df=()=>{const r=ph(),t=gh(),e=mh(),[i,o]=gs.useState(!1),{order:a,downloadUrl:l}=vh(E=>E.order),h=E=>{const K={visa:jh,mastercard:xl,amex:kh,discover:_h,diners:Ph,jcb:Sh,unionpay:Lh,unknown:ms};return E?K[E.toLowerCase()]||ms:ms},c=(E,K)=>K?`**** **** **** ${K}`:"**** **** **** 1234",d=E=>{const K={visa:"Visa",mastercard:"Mastercard",amex:"American Express",discover:"Discover",diners:"Diners Club",jcb:"JCB",unionpay:"UnionPay",unknown:"Card"};return E&&K[E.toLowerCase()]||"Card"},v=(()=>{var E,K,st,yt,Q,q,rt,pt,_,j,V,R,ot,nt,ut;return(E=t.state)!=null&&E.orderData?t.state.orderData:a?{orderId:`#${((K=a._id)==null?void 0:K.slice(-8))||"12345678"}`,date:uo(a.createdAt||Date.now(),{year:"numeric",month:"long",day:"numeric"}),time:uo(a.createdAt||Date.now(),{hour:"2-digit",minute:"2-digit",timeZoneName:"short"}),items:1,totalAmount:`$${a.amount||"20.00"}`,customerDetails:{name:(st=a.buyer)!=null&&st.firstName&&((yt=a.buyer)!=null&&yt.lastName)?`${a.buyer.firstName} ${a.buyer.lastName}`:"John Smith",email:((Q=a.buyer)==null?void 0:Q.email)||"<EMAIL>",phone:((q=a.buyer)==null?void 0:q.mobile)||((rt=a.buyer)==null?void 0:rt.phone)||"Not provided"},paymentDetails:{method:d((pt=a.cardDetails)==null?void 0:pt.cardType)||"Mastercard",cardNumber:c((_=a.cardDetails)==null?void 0:_.cardType,(j=a.cardDetails)==null?void 0:j.lastFourDigits),cardType:((V=a.cardDetails)==null?void 0:V.cardType)||"mastercard",cardLogo:h((R=a.cardDetails)==null?void 0:R.cardType)},itemInfo:{title:((ot=a.content)==null?void 0:ot.title)||"Frank Martin - Drills and Coaching Philosophies to Developing Toughness in Your Players",category:((nt=a.content)==null?void 0:nt.category)||"Basketball Coaching Core",image:((ut=a.content)==null?void 0:ut.thumbnail)||"https://via.placeholder.com/80x80/f0f0f0/666666?text=Product"}}:{orderId:"#12345678",date:"20 May 2025",time:"4:50PM",items:1,totalAmount:"$20.00",customerDetails:{name:"John Smith",email:"<EMAIL>",phone:"Not provided"},paymentDetails:{method:"Mastercard",cardNumber:"**** **** **** 1234",cardType:"mastercard",cardLogo:xl},itemInfo:{title:"Frank Martin ",category:"Basketball Coaching Core",image:"data:image/jpeg;base64,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"}}})(),w=v.paymentDetails.cardType,p=h(w),C=d(w);gs.useEffect(()=>{window.scrollTo(0,0);const K=new URLSearchParams(t.search).get("orderId");K&&!a&&e(bh(K))},[e,t.search,a]),gs.useEffect(()=>{if(l){const E=document.createElement("a");E.href=l,E.download=`order-${v.orderId}-content`,document.body.appendChild(E),E.click(),document.body.removeChild(E),o(!1)}},[l,v.orderId]);const k=E=>{const K=new Ut,st=30,yt=10;K.addImage(Nh,"PNG",(K.internal.pageSize.width-st)/2,yt,st,st);let Q=yt+st+10;K.setFontSize(18),K.text("Order Receipt",K.internal.pageSize.width/2,Q,{align:"center"}),Q+=8,K.setFontSize(10),K.text(`Date: ${E.date} | Time: ${E.time}`,K.internal.pageSize.width/2,Q,{align:"center"});const q=(V,R,ot)=>{K.setFontSize(12),K.text(V,14,ot);const nt=Object.entries(R).map(([Z,ht])=>[Z,ht]);let ut;return ff(K,{startY:ot+5,head:[],body:nt,theme:"grid",styles:{fontSize:10,cellPadding:3},columnStyles:{0:{fontStyle:"bold",cellWidth:60},1:{cellWidth:100}},margin:{left:14,right:14},headStyles:{fillColor:[240,240,240],textColor:[0,0,0]},didDrawPage:function(Z){ut=Z.cursor.y}}),ut};Q+=10;const rt={"Order ID":E.orderId,"Total Amount":E.totalAmount,Items:E.items,date:uo(a.createdAt||Date.now(),{year:"numeric",month:"long",day:"numeric"}),time:uo(a.createdAt||Date.now(),{hour:"2-digit",minute:"2-digit",timeZoneName:"short"})};Q=q("Order Details",rt,Q),Q+=10;const pt={Name:E.customerDetails.name,Email:E.customerDetails.email,Phone:E.customerDetails.phone};Q=q("Customer Information",pt,Q),Q+=10;const _={"Payment Method":E.paymentDetails.method,"Card Number":E.paymentDetails.cardNumber};Q=q("Payment Information",_,Q),Q+=10;const j={Title:E.itemInfo.title,Category:E.itemInfo.category};return Q=q("Item Details",j,Q),Q+=15,K.setFontSize(10),K.text("Thank you for your purchase!",K.internal.pageSize.width/2,Q,{align:"center"}),K.text("For any questions, <NAME_EMAIL>",K.internal.pageSize.width/2,Q+5,{align:"center"}),K},B=E=>{k(E).save(`receipt-${E.orderId.replace("#","")}.pdf`)},P=async()=>{if(!i){o(!0);try{B(v)}catch(E){console.error("Download failed:",E)}finally{o(!1)}}},O=()=>{r("/")};return Tt.jsx("div",{className:"thank-you-page",children:Tt.jsxs("div",{className:"thank-you-container max-container",children:[Tt.jsxs("div",{className:"success-header",children:[Tt.jsx("div",{className:"success-icon",children:Tt.jsx("img",{src:Ch,alt:"thankyou"})}),Tt.jsx("h1",{className:"success-title",children:"Congratulation for your order!"}),Tt.jsx("p",{className:"success-message",children:"We will update you for the delivery status soon via Email or SMS."})]}),Tt.jsxs("div",{className:"order-info-card",children:[Tt.jsx("h2",{className:"order-info-title",children:"Order Information"}),Tt.jsxs("div",{className:"order-details-grid",children:[Tt.jsxs("div",{className:"order-details-grid-container",children:[Tt.jsxs("div",{className:"order-detail-item",children:[Tt.jsx("span",{className:"detail-label",children:"Order Id:"}),Tt.jsx("span",{className:"detail-value",children:v.orderId})]}),Tt.jsxs("div",{className:"order-detail-item",children:[Tt.jsx("span",{className:"detail-label",children:"Items:"}),Tt.jsx("span",{className:"detail-value",children:v.items})]})]}),Tt.jsx("div",{className:"vertical-line"}),Tt.jsxs("div",{className:"order-details-grid-container",children:[Tt.jsxs("div",{className:"order-detail-item",children:[Tt.jsx("span",{className:"detail-label",children:"Date:"}),Tt.jsxs("span",{className:"detail-value",children:[v.date," | ",v.time]})]}),Tt.jsxs("div",{className:"order-detail-item",children:[Tt.jsx("span",{className:"detail-label",children:"Total Amount:"}),Tt.jsx("span",{className:"detail-value",children:v.totalAmount})]})]})]}),Tt.jsxs("div",{className:"details-section",children:[Tt.jsxs("div",{className:"customer-details",children:[Tt.jsx("h3",{className:"section-title",children:"Customer Details"}),Tt.jsxs("div",{className:"detail-group",children:[Tt.jsxs("div",{className:"detail-row",children:[Tt.jsx("span",{className:"detail-label",children:"Name:"}),Tt.jsx("span",{className:"detail-value",children:v.customerDetails.name})]}),Tt.jsxs("div",{className:"detail-row",children:[Tt.jsx("span",{className:"detail-label",children:"Email Address:"}),Tt.jsx("span",{className:"detail-value",children:v.customerDetails.email})]}),Tt.jsxs("div",{className:"detail-row",children:[Tt.jsx("span",{className:"detail-label",children:"Phone Number:"}),Tt.jsx("span",{className:"detail-value",children:v.customerDetails.phone})]})]})]}),Tt.jsx("div",{className:"vertical-line"}),Tt.jsxs("div",{className:"payment-details",children:[Tt.jsx("h3",{className:"section-title",children:"Payment Details"}),Tt.jsxs("div",{className:"payment-method",children:[Tt.jsx("img",{src:p,alt:C,className:"payment-logo"}),Tt.jsx("span",{className:"card-number",children:v.paymentDetails.cardNumber})]})]})]}),Tt.jsxs("div",{className:"item-info-section",children:[Tt.jsx("h3",{className:"section-title",children:"Item Info"}),Tt.jsxs("div",{className:"item-info-content",children:[Tt.jsx("div",{className:"item-image",children:Tt.jsx("img",{src:`${yh}${v.itemInfo.image}`,alt:v.itemInfo.title,className:"product-image"})}),Tt.jsxs("div",{className:"item-details",children:[Tt.jsx("h4",{className:"item-title",children:v.itemInfo.title}),Tt.jsxs("p",{className:"item-category",children:["By ",v.itemInfo.category]})]})]})]})]}),Tt.jsxs("div",{className:"action-buttons",children:[Tt.jsxs("button",{className:" download-btn btn-outline",onClick:P,disabled:i,children:[i?Tt.jsx(wh,{className:"btn-icon spinning"}):Tt.jsx(xh,{className:"btn-icon"}),i?"Downloading...":"Download"]}),Tt.jsxs("button",{className:"btn homepage-btn",onClick:O,children:[Tt.jsx(Ah,{className:"btn-icon"}),"Go to Homepage"]})]})]})})},bf=Object.freeze(Object.defineProperty({__proto__:null,default:df},Symbol.toStringTag,{value:"Module"}));export{bf as T,de as _};
