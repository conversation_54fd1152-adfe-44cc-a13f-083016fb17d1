import{E as r,r as c,j as e}from"./index-oGr_1a_A.js";const m=()=>{const[t]=r();return c.useEffect(()=>{const o=t.get("stripe_success"),s=t.get("stripe_refresh"),i=t.get("account_id");if(t.get("context")==="onboarding"){const n={type:"STRIPE_ONBOARDING_RESULT",success:!!o,refresh:!!s,accountId:i,timestamp:Date.now()};if(window.opener)try{window.opener.postMessage(n,window.location.origin),setTimeout(()=>{window.close()},1e3)}catch{setTimeout(()=>{window.close()},1e3)}else setTimeout(()=>{window.close()},1e3)}else window.location.href=`/seller/payment-settings${window.location.search}`},[t]),e.jsx("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",padding:"2rem",fontFamily:"Arial, sans-serif",backgroundColor:"#f8f9fa"},children:e.jsxs("div",{style:{textAlign:"center",backgroundColor:"white",padding:"2rem",borderRadius:"8px",boxShadow:"0 2px 10px rgba(0,0,0,0.1)",maxWidth:"400px"},children:[e.jsx("div",{style:{fontSize:"48px",marginBottom:"1rem"},children:"✅"}),e.jsx("h2",{style:{color:"#28a745",marginBottom:"1rem"},children:"Success!"}),e.jsx("p",{style:{color:"#6c757d",lineHeight:"1.5"},children:"Your Stripe account setup is complete. This window will close automatically."}),e.jsx("div",{style:{marginTop:"1.5rem",fontSize:"14px",color:"#999"},children:"Closing in 1 second..."})]})})};export{m as default};
