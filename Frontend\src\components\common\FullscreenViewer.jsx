import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Document, Page } from 'react-pdf';
import './FullscreenViewer.css';

const FullscreenViewer = ({ 
  isOpen, 
  onClose, 
  contentType, 
  content, 
  videoControls = true,
  pdfScale = 1.0
}) => {
  const [numPages, setNumPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [scale, setScale] = useState(pdfScale);
  const containerRef = useRef(null);

  useEffect(() => {
    const handleEscKey = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    const handleWheel = (e) => {
      if (contentType === 'pdf') {
        if (e.ctrlKey) {
          e.preventDefault();
          setScale(prevScale => {
            const newScale = e.deltaY < 0 ? prevScale * 1.1 : prevScale / 1.1;
            return Math.max(0.5, Math.min(3, newScale));
          });
        }
      }
    };

    document.addEventListener('keydown', handleEscKey);
    if (containerRef.current) {
      containerRef.current.addEventListener('wheel', handleWheel, { passive: false });
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      if (containerRef.current) {
        containerRef.current.removeEventListener('wheel', handleWheel);
      }
    };
  }, [isOpen, onClose, contentType]);

  const handleDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
  };

  const changePage = (offset) => {
    setCurrentPage(prevPage => {
      const newPage = prevPage + offset;
      return Math.max(1, Math.min(numPages, newPage));
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fullscreen-viewer-overlay" onClick={onClose}>
      <div 
        className="fullscreen-viewer-container" 
        onClick={e => e.stopPropagation()}
        ref={containerRef}
      >
        <button className="fullscreen-close-button" onClick={onClose}>
          ×
        </button>

        {contentType === 'pdf' ? (
          <div className="pdf-container">
            <Document
              file={content}
              onLoadSuccess={handleDocumentLoadSuccess}
              loading={<div className="loading">Loading PDF...</div>}
            >
              <Page
                pageNumber={currentPage}
                scale={scale}
                loading={<div className="loading">Loading page...</div>}
              />
            </Document>
            {numPages > 1 && (
              <div className="pdf-controls">
                <button 
                  onClick={() => changePage(-1)}
                  disabled={currentPage <= 1}
                >
                  Previous
                </button>
                <span>Page {currentPage} of {numPages}</span>
                <button 
                  onClick={() => changePage(1)}
                  disabled={currentPage >= numPages}
                >
                  Next
                </button>
              </div>
            )}
            <div className="zoom-controls">
              <button onClick={() => setScale(s => Math.max(0.5, s / 1.1))}>-</button>
              <span>{Math.round(scale * 100)}%</span>
              <button onClick={() => setScale(s => Math.min(3, s * 1.1))}>+</button>
            </div>
          </div>
        ) : contentType === 'video' ? (
          <div className="video-container">
            <video
              src={content}
              controls={videoControls}
              autoPlay
              playsInline
              className="fullscreen-video"
            />
          </div>
        ) : null}
      </div>
    </div>
  );
};

FullscreenViewer.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  contentType: PropTypes.oneOf(['pdf', 'video']).isRequired,
  content: PropTypes.string.isRequired,
  videoControls: PropTypes.bool,
  pdfScale: PropTypes.number
};

export default FullscreenViewer; 