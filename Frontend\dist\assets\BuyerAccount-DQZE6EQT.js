import{a as F,j as e,aO as _e,aP as Ce,b as _,r as c,aQ as ie,t as G,c as le,aR as U,w as Be,aS as we,aM as fe,aT as Se,aU as Pe,ar as De,u as J,T as xe,U as ye,X as ke,as as Q,a3 as V,N as Ie,a4 as pe,aV as z,aW as Ee,aX as X,a1 as Y,aY as Fe,aZ as H,a_ as ce,a8 as O,J as je,a$ as Ae,b0 as Ne,aH as L,b1 as Re,a9 as $,aK as K,b2 as T,L as Me,aL as I,b3 as $e,b4 as qe,b5 as Oe,b6 as Le,b7 as Ue,aE as Te,b8 as oe,b9 as M,aI as Ye,ba as Ve,bb as ze,bc as We,bd as de,be as Ge,bf as ue,bg as He,bh as Ke,bi as Qe,bj as me,bk as ge,bl as Z,bm as Xe,$ as Je,i as Ze,bn as es}from"./index-oGr_1a_A.js";import{B as ss}from"./BuyerAccount-Bk8hk-pT.js";import{S as E,B as he}from"./BuyerAccountDashboard-B1zfmQa6.js";import{T as A,L as be}from"./LoadingSkeleton-DmDWH340.js";import{P as ee}from"./Pagination-Chk7MbK3.js";import{T as W}from"./Table-67eaNy4J.js";import{a as se}from"./dateValidation-YzamylQ0.js";import{s as as}from"./stripe-DT3_Ek51.js";/* empty css                        */import"./index-auB_fJid.js";import"./timezoneUtils-Dky5bF8c.js";const ts=({children:s})=>{const r=F(),n=()=>{r(Ce()),window.location.reload()};return e.jsx(_e,{title:"Buyer Dashboard Error",message:"Something went wrong with the buyer dashboard. Please try again.",onRetry:n,retryText:"Reload Dashboard",showDetails:!1,children:s})},rs=()=>{const s=F(),{user:r,isLoading:n,isSuccess:f,isError:h,error:m}=_(i=>i.auth),[o,l]=c.useState({firstName:"",lastName:"",email:"",mobile:"",profileImage:""}),[g,x]=c.useState(!0),[v,k]=c.useState(null),[D,N]=c.useState(null);c.useEffect(()=>{s(ie())},[s]),c.useEffect(()=>{r&&(l({firstName:r.firstName||"",lastName:r.lastName||"",email:r.email||"",mobile:r.mobile||"",profileImage:r.profileImage||""}),C(!1),x(!1))},[r]);const[B,b]=c.useState(!1),[d,C]=c.useState(!1);c.useEffect(()=>{B&&f&&!n&&(G.success("Profile updated successfully!"),s(le()),b(!1),s(ie())),B&&h&&m&&(G.error(m.message||"Failed to update profile"),s(le()),b(!1))},[f,h,m,n,s,B]);const w=i=>{const{name:u,value:j}=i.target;l({...o,[u]:j})},S=async i=>{i.preventDefault(),b(!0);try{let u=o.profileImage;v&&(u=(await s(Se(v)).unwrap()).data.fileUrl);const j={firstName:o.firstName,lastName:o.lastName,profileImage:u};s(Pe(j))}catch{G.error("Failed to upload image or update profile"),b(!1)}},t=i=>{const u=i.target.files[0];if(u){k(u),C(!1);const j=new FileReader;j.onloadend=()=>{N(j.result)},j.readAsDataURL(u)}},p=()=>{C(!0)};return e.jsx("div",{className:"BuyerProfile",children:e.jsx(E,{icon:e.jsx(U,{className:"BuyerSidebar__icon"}),title:"My Profile",children:e.jsxs("div",{className:"profile_border_container",children:[e.jsxs("div",{className:"BuyerProfile__container",children:[e.jsxs("div",{className:"BuyerProfile__left-section",children:[e.jsxs("div",{className:"BuyerProfile__form-row",children:[e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(U,{})}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:o.firstName,onChange:w,placeholder:"First Name",required:!0,className:"BuyerProfile__input"})]})}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(U,{})}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:o.lastName,onChange:w,placeholder:"Last Name",required:!0,className:"BuyerProfile__input"})]})})]}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(Be,{})}),e.jsx("input",{type:"email",id:"email",name:"email",value:o.email,placeholder:"Email Address",className:"BuyerProfile__input BuyerProfile__input--readonly",readOnly:!0,disabled:!0})]})}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(we,{})}),e.jsx("input",{type:"tel",id:"mobile",name:"mobile",value:o.mobile,placeholder:"Mobile Number",className:"BuyerProfile__input BuyerProfile__input--readonly",readOnly:!0,disabled:!0})]})})]}),e.jsx("div",{className:"BuyerProfile__right-section",children:e.jsxs("div",{className:"BuyerProfile__image-container",children:[e.jsx("h3",{className:"BuyerProfile__image-title",children:"Profile Image"}),e.jsx("div",{className:"BuyerProfile__image",children:D||o.profileImage&&!d?e.jsx("img",{src:D||fe(o.profileImage),alt:"Profile",onError:p}):e.jsx("div",{className:"BuyerProfile__placeholder",children:o.firstName&&o.lastName?`${o.firstName.charAt(0)}${o.lastName.charAt(0)}`:e.jsx(U,{className:"BuyerProfile__user-icon"})})}),e.jsx("button",{className:"BuyerProfile__upload-btn",onClick:()=>document.getElementById("profile-image-upload").click(),children:"Upload Photo"}),e.jsx("input",{type:"file",id:"profile-image-upload",accept:"image/*",onChange:t,style:{display:"none"}})]})})]}),e.jsx("div",{className:"BuyerProfile__buttons mt-30",children:e.jsx("button",{type:"button",className:"BuyerProfile__save-btn",onClick:S,disabled:B||n,children:B||n?"Updating...":"Update & Save"})})]})})})},ns=s=>{if(!s||s===0)return"Unknown size";const r=["Bytes","KB","MB","GB","TB"],n=Math.floor(Math.log(s)/Math.log(1024));return n===0?`${s} ${r[n]}`:`${(s/Math.pow(1024,n)).toFixed(1)} ${r[n]}`},is=()=>{const s=F(),r=_(De),n=J(),f=_(xe),h=_(ye),[m,o]=c.useState(new Set),l=_(d=>ke(d,"downloads")),[g,x]=c.useState(1),[v]=c.useState(10);c.useEffect(()=>{s(Q({page:g,limit:v}))},[s,g,v]);const k=d=>{x(d),window.scrollTo(0,0)},D=()=>{s(pe("downloads")),s(Q())},N=d=>{if(!d)return"";const C=Math.floor(d/3600),w=Math.floor(d%3600/60),S=d%60;return C>0?`${C}:${w.toString().padStart(2,"0")}:${S.toString().padStart(2,"0")}`:`${w}:${S.toString().padStart(2,"0")}`},B=[{key:"no",label:"No.",className:"no"},{key:"orderId",label:"Order Id",className:"order-id"},{key:"content",label:"Content",className:"content"},{key:"date",label:"Purchase Date",className:"date"},{key:"amount",label:"Amount",className:"amount"},{key:"downloads",label:"Downloads",className:"downloads"},{key:"actions",label:"Actions",className:"actions"}],b=(d,C)=>{var t,p,i;m.has(d._id);const w=d.thumbnailUrl?fe(d.thumbnailUrl):null,S=se(d.downloadDate);return[e.jsx("td",{className:"no",children:C+1},"no"),e.jsxs("td",{className:"order-id",children:["#",((t=d.orderId)==null?void 0:t.slice(-8))||((p=d._id)==null?void 0:p.slice(-8))]},"orderId"),e.jsx("td",{className:"content",children:e.jsxs("div",{className:"content-item",children:[e.jsx("div",{className:"content-image",children:e.jsx("img",{src:w||"https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:d.title,style:{width:"40px",height:"40px",objectFit:"cover",borderRadius:"6px"}})}),e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:d.title}),e.jsxs("div",{className:"content-coach",children:["By ",d.coach]}),e.jsxs("div",{className:"content-meta",children:[e.jsx("span",{className:"file-type",children:d.fileType}),d.fileSize&&e.jsxs("span",{className:"file-size",children:[" ","• ",ns(d.fileSize)]}),d.duration&&e.jsxs("span",{className:"duration",children:[" ","• ",N(d.duration)]})]})]})]})},"content"),e.jsx("td",{className:"date",children:S},"date"),e.jsxs("td",{className:"amount",children:["$",((i=d.amount)==null?void 0:i.toFixed(2))||"0.00"]},"amount"),e.jsx("td",{className:"downloads",children:e.jsxs("span",{className:"download-count",children:[d.downloadCount||0," times"]})},"downloads"),e.jsx("td",{className:"actions",children:e.jsx("button",{className:"action-btn view-btn",onClick:()=>n(`/buyer/download-details/${d._id}`),title:"View details",children:e.jsx(z,{})})},"actions")]};return e.jsx(E,{title:"My Downloads",icon:e.jsx(Ie,{className:"BuyerSidebar__icon"}),children:h.downloads?e.jsx(V,{error:h.downloads,onRetry:D,title:"Failed to load downloads"}):f.downloads?e.jsxs("div",{className:"loading-container",children:[e.jsx(A,{columns:7}),e.jsx(A,{columns:7}),e.jsx(A,{columns:7})]}):r.length>0?e.jsxs(e.Fragment,{children:[e.jsx(W,{columns:B,data:r,renderRow:b,className:"BuyerDownloads__table",emptyMessage:"You have no downloads yet."}),l&&l.totalPages>1&&e.jsx("div",{className:"BuyerDownloads__pagination",children:e.jsx(ee,{currentPage:l.currentPage,totalPages:l.totalPages,onPageChange:k})})]}):e.jsxs("div",{className:"BuyerDownloads__empty",children:[e.jsx("h3",{children:"No downloads yet"}),e.jsx("p",{children:"Your purchased content will appear here once you make your first purchase."})]})})},ls=()=>{const s=F(),r=_(Ee),n=_(xe),f=_(ye);c.useEffect(()=>{s(X())},[s]);const h=()=>{s(pe("requests")),s(X())},m=[{key:"no",label:"No.",className:"no"},{key:"requestId",label:"Request Id",className:"request-id"},{key:"video",label:"Videos/Documents",className:"video"},{key:"date",label:"Date",className:"date"},{key:"requestedAmount",label:"Requested Amount",className:"requested-amount"},{key:"status",label:"Status",className:"status"},{key:"action",label:"Action",className:"action"}],o=(l,g)=>e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"table-cell no",children:g+1}),e.jsxs("div",{className:"table-cell request-id",children:["#REQUEST",l.id]}),e.jsx("div",{className:"table-cell video",children:e.jsxs("div",{className:"content-item",children:[e.jsx("div",{className:"content-image",children:e.jsx("img",{src:"https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:l.title})}),e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:l.title}),e.jsx("div",{className:"content-coach",children:"By Coach"})]})]})}),e.jsxs("div",{className:"table-cell date",children:[l.date," | 4:30PM"]}),e.jsx("div",{className:"table-cell requested-amount",children:"$22.00"}),e.jsx("div",{className:"table-cell status",children:e.jsx("span",{className:`status-badge ${l.status}`,children:l.status.charAt(0).toUpperCase()+l.status.slice(1)})}),e.jsx("div",{className:"table-cell action",children:e.jsxs("div",{className:"action-buttons",children:[e.jsx("button",{className:"view-button",children:e.jsx(z,{})}),e.jsx("button",{className:"retry-btn",onClick:h,title:"Retry loading requests",children:e.jsx(Y,{})})]})})]});return e.jsx("div",{className:"BuyerRequests",children:e.jsx(E,{icon:e.jsx(Fe,{className:"BuyerSidebar__icon"}),title:"My Requests",action:f.requests&&e.jsx("button",{className:"retry-btn",onClick:h,title:"Retry loading requests",children:e.jsx(Y,{})}),children:f.requests?e.jsx(V,{error:f.requests,onRetry:h,title:"Failed to load requests"}):n.requests?e.jsxs("div",{className:"loading-container",children:[e.jsx(A,{columns:7}),e.jsx(A,{columns:7}),e.jsx(A,{columns:7})]}):r.length>0?e.jsx(W,{columns:m,data:r,renderRow:o,variant:"grid",gridTemplate:"0.5fr 1fr 3fr 1.5fr 1.5fr 1fr 0.5fr",className:"BuyerRequests__table",emptyMessage:"You have no requests yet."}):e.jsxs("div",{className:"BuyerRequests__empty",children:[e.jsx("h3",{children:"No requests yet"}),e.jsx("p",{children:"You haven't made any custom content requests yet. Start by requesting specific training content from coaches."})]})})})},cs=()=>{var ae,te;const s=F(),r=J(),{userBids:n,isLoading:f,isError:h,error:m,pagination:o}=_(a=>a.bid),[l,g]=c.useState(null),[x,v]=c.useState(!1),[k,D]=c.useState(!1),[N,B]=c.useState(1),[b]=c.useState(10);c.useEffect(()=>{s(H({page:N,limit:b}))},[s,N,b]);const d=a=>{B(a),window.scrollTo(0,0)},C=()=>{s(H())},w=async a=>{var y;D(!0);try{const P=await s(Re(a._id)).unwrap();(y=P.data)!=null&&y.reactivatedBid?$.success(`Bid cancelled and your previous $${P.data.reactivatedBid.amount} bid is now active`):$.success("Bid cancelled successfully"),v(!1),g(null),s(H())}catch(P){$.error(P.message||"Failed to cancel bid")}finally{D(!1)}},S=a=>{a.order&&a.order._id?r(`/checkout/${a.order._id}`):r("/buyer/checkout",{state:{type:"bid",bidId:a._id,amount:a.amount,content:a.content}})},t=a=>{g(a),v(!0)},p=()=>{v(!1),g(null)},i=a=>{switch(a){case"Active":return"active";case"Won":return"won";case"Lost":return"lost";case"Cancelled":return"status-cancelled";case"Outbid":return"status-outbid";default:return"status-default"}},u=a=>se(a),j=a=>{if(!(a!=null&&a.auctionDetails))return!1;const y=new Date,P=a.auctionDetails.auctionStartDate?new Date(a.auctionDetails.auctionStartDate):null,q=a.auctionDetails.auctionEndDate?new Date(a.auctionDetails.auctionEndDate):null;return(!P||y>=P)&&(!q||y<=q)},R=[{key:"no",label:"No.",className:"no",render:(a,y)=>{const P=typeof y=="number"&&!isNaN(y)?y:0;return(N-1)*b+P+1}},{key:"bidId",label:"Bid Id",className:"bid-id",render:a=>{var y;return`#${(y=a._id)==null?void 0:y.slice(-6)}`}},{key:"content",label:"Videos/Documents",className:"video",render:a=>{var y,P,q,re,ne;return e.jsxs("div",{className:"content-item",children:[e.jsx("div",{className:"content-image",children:e.jsx("img",{src:`${je}${(y=a.content)==null?void 0:y.thumbnailUrl}`,alt:((P=a.content)==null?void 0:P.title)||"Content"})}),e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:((q=a.content)==null?void 0:q.title)||"Unknown Content"}),e.jsxs("div",{className:"content-coach",children:["By ",((ne=(re=a.content)==null?void 0:re.seller)==null?void 0:ne.firstName)||"Unknown"]})]})]})}},{key:"date",label:"Date",className:"date",render:a=>u(a.createdAt)},{key:"bidAmount",label:"Bid Amount",className:"bid-amount",render:a=>{var y;return`$${((y=a.amount)==null?void 0:y.toFixed(2))||"0.00"}`}},{key:"status",label:"Status",className:"status",render:a=>e.jsx("span",{className:`status-badge ${i(a.status)}`,children:a.status})},{key:"auctionStatus",label:"Auction",className:"auction-status",render:a=>e.jsxs("span",{className:`auction-indicator auction-statuscss ${j(a.content)?"active":"ended"}`,children:[e.jsx(Ae,{})," ",j(a.content)?"Active":"Ended"]})},{key:"action",label:"Action",className:"action",render:a=>e.jsx("div",{className:"action-buttons",children:a.status==="Won"?a.order&&a.order.paymentStatus==="Completed"?e.jsx("button",{className:"btn-paid",disabled:!0,children:e.jsx(Ne,{})}):a.order&&(a.order.paymentStatus==="Expired"||a.order.status==="Expired")?e.jsxs("button",{className:"btn-expired",disabled:!0,children:[e.jsx(O,{})," Expired"]}):e.jsxs("a",{className:"btn-pay",onClick:()=>S(a),children:[e.jsx(L,{})," Pay Now"]}):a.status==="Lost"?e.jsxs("button",{className:"btn-lost",disabled:!0,children:[e.jsx(O,{})," Lost"]}):a.status==="Active"?e.jsx("a",{className:"btn-cancel",onClick:()=>t(a),children:e.jsx(O,{})}):e.jsx("button",{className:"btn-view",onClick:()=>{var y;return r(`/buyer/details/${(y=a.content)==null?void 0:y._id}`)},children:e.jsx(z,{})})})}];return e.jsx(E,{icon:e.jsx(ce,{className:"BuyerSidebar__icon"}),title:"My Bids",children:e.jsxs("div",{className:"BuyerBids",children:[f&&!(n!=null&&n.length)?e.jsx(be,{}):h?e.jsx(V,{error:m,onRetry:C,message:"Failed to load bids"}):n!=null&&n.length?e.jsxs(e.Fragment,{children:[e.jsx(W,{data:n,columns:R,isLoading:f,LoadingComponent:A}),o&&e.jsx("div",{className:"BuyerBids__pagination",children:e.jsx(ee,{currentPage:N,totalPages:o.totalPages,onPageChange:d})})]}):e.jsxs("div",{className:"BuyerBids__empty",children:[e.jsx(ce,{}),e.jsx("h3",{children:"No Bids Yet"}),e.jsx("p",{children:"You haven't placed any bids yet."})]}),x&&l&&e.jsx("div",{className:"cancel-modal-overlay",onClick:p,children:e.jsxs("div",{className:"cancel-modal",onClick:a=>a.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:"Cancel Bid"}),e.jsx("button",{className:"close-btn",onClick:p,children:e.jsx(O,{})})]}),e.jsxs("div",{className:"modal-content",children:[e.jsxs("div",{className:"bid-details",children:[e.jsx("h4",{children:"Bid Details"}),e.jsxs("p",{children:[e.jsx("strong",{children:"Content:"})," ",(ae=l.content)==null?void 0:ae.title]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Bid Amount:"})," $",(te=l.amount)==null?void 0:te.toFixed(2)]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Date:"})," ",u(l.createdAt)]})]}),e.jsx("div",{className:"warning-message",children:e.jsx("p",{children:"Are you sure you want to cancel this bid? This action cannot be undone. If you have a previous bid on this content, it may be reactivated."})}),e.jsxs("div",{className:"modal-actions",children:[e.jsx("button",{className:"keepbid",onClick:p,disabled:k,children:"No, Keep Bid"}),e.jsx("button",{className:"btn-primary",onClick:()=>w(l),disabled:k,children:k?e.jsxs(e.Fragment,{children:[e.jsx(Y,{className:"spin"})," Cancelling..."]}):"Yes, Cancel Bid"})]})]})]})})]})})},os=()=>{const s=F(),r=J(),{buyerOffers:n,isLoading:f,isError:h,error:m,pagination:o}=_(t=>t.offer),[l,g]=c.useState(null),[x,v]=c.useState(1);c.useEffect(()=>{s(K({page:x,limit:10}))},[s,x]);const k=t=>{v(t),window.scrollTo(0,0)},D=async t=>{if(window.confirm("Are you sure you want to cancel this offer?")){g(t);try{await s($e(t)).unwrap(),$.success("Offer cancelled successfully"),s(K({page:x,limit:10}))}catch(p){$.error(p.message||"Failed to cancel offer")}finally{g(null)}}},N=()=>{s(K({page:x,limit:10}))},B=t=>{t.orderId&&t.orderId._id?r(`/checkout/${t.orderId._id}`):$.error("Order not found. Please contact support.")},b=t=>{s(I("offers")),r(`/buyer/account/offers/${t}`)},d=t=>{const p={Pending:"status-pending",Accepted:"status-accepted",Rejected:"status-rejected",Cancelled:"status-cancelled",Expired:"status-expired"};return e.jsx("span",{className:`status-badge ${p[t]||""}`,children:t})},C=t=>se(t),w=t=>`$${parseFloat(t).toFixed(2)}`,S=[{label:"Content",key:"content",render:t=>{var p,i,u;return e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-thumbnail",children:(p=t.content)!=null&&p.thumbnailUrl?e.jsx("img",{src:je+t.content.thumbnailUrl,alt:t.content.title}):e.jsx("div",{className:"no-thumbnail",children:e.jsx(T,{})})}),e.jsxs("div",{className:"content-details",children:[e.jsx("h4",{className:"content-title",children:((i=t.content)==null?void 0:i.title)||"Untitled"}),e.jsx("p",{className:"content-sport",children:((u=t.content)==null?void 0:u.sport)||"N/A"})]})]})}},{label:"Seller",key:"seller",render:t=>{var p,i;return e.jsx("div",{className:"seller-info",children:e.jsxs("span",{className:"seller-name",children:[(p=t.seller)==null?void 0:p.firstName," ",(i=t.seller)==null?void 0:i.lastName]})})}},{label:"Offer Amount",key:"amount",render:t=>e.jsx("span",{className:"offer-amount",children:w(t.amount)})},{label:"Status",key:"status",render:t=>d(t.status)},{label:"Date",key:"createdAt",render:t=>e.jsx("span",{className:"offer-date",children:C(t.createdAt)})},{label:"Actions",key:"actions",render:t=>e.jsxs("div",{className:"action-buttons",children:[e.jsx("button",{className:"view-btn",onClick:()=>b(t._id),title:"View Offer Details",children:e.jsx(z,{})}),t.status==="Accepted"?t.orderId&&t.orderId.paymentStatus==="Completed"?e.jsx("button",{className:"btn-paid",disabled:!0,children:e.jsx(Ne,{})}):t.orderId&&(t.orderId.paymentStatus==="Expired"||t.orderId.status==="Expired")?null:e.jsxs("button",{className:"btn-pay",onClick:()=>B(t),children:[e.jsx(L,{})," Pay Now"]}):t.status==="Pending"?e.jsx("button",{className:"cancel-btnmaindiv",onClick:()=>D(t._id),disabled:l===t._id,title:"Cancel Offer",children:l===t._id?e.jsx(Y,{className:"spinning"}):e.jsx(O,{})}):null]})}];return h?e.jsx(E,{icon:e.jsx(T,{className:"BuyerSidebar__icon"}),title:"My Offers",children:e.jsx(V,{error:m,onRetry:N,title:"Failed to load offers"})}):e.jsx(E,{icon:e.jsx(T,{className:"BuyerSidebar__icon"}),title:"My Offers",children:e.jsx("div",{className:"BuyerOffers",children:f?e.jsx(be,{type:"table",rows:5}):n&&n.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"offers-summary",children:e.jsxs("p",{children:["Showing ",n.length," of ",o.totalItems," offer",o.totalItems!==1?"s":""]})}),e.jsx(W,{columns:S,data:n,className:"offers-table"}),e.jsx(ee,{currentPage:o.currentPage,totalPages:o.totalPages,onPageChange:k})]}):e.jsxs("div",{className:"no-offers",children:[e.jsx(T,{className:"no-offers-icon"}),e.jsx("h3",{children:"No Offers Yet"}),e.jsx("p",{children:"You haven't made any offers yet. Browse content and make your first offer!"}),e.jsx(Me,{to:"/content",className:"btn-primary",children:"Browse Content"})]})})})},ds=()=>{const s=F(),r=_(qe),n=_(Oe),f=_(Le),h=_(Ue),[m,o]=c.useState({nameOnCard:"",cardNumber:"",expiryDate:"",cvv:""}),[l,g]=c.useState(!1),[x,v]=c.useState(null),[k,D]=c.useState(null),[N,B]=c.useState(null),b=c.useRef(null);c.useEffect(()=>(n==="add"&&(async()=>{try{const u=await as;if(!u)throw new Error("Stripe failed to load");v(u);const j=u.elements();D(j);const R=j.create("card",{style:{base:{color:"#424770",fontFamily:'"Helvetica Neue", Helvetica, sans-serif',fontSmoothing:"antialiased",fontSize:"16px","::placeholder":{color:"#aab7c4"}},invalid:{color:"#9e2146",iconColor:"#9e2146"}},hidePostalCode:!0});B(R)}catch(u){console.error("Error initializing Stripe:",u),M("Failed to load payment form. Please refresh the page.")}})(),()=>{N&&N.unmount()}),[n]),c.useEffect(()=>{N&&b.current&&n==="add"&&N.mount(b.current)},[N,n]),c.useEffect(()=>(s(Te()),()=>{s(oe())}),[s]),c.useEffect(()=>{h&&(M(h.message||"Failed to load cards"),s(oe()))},[h,s]);const d=()=>{n==="add"&&o({nameOnCard:"",cardNumber:"",expiryDate:"",cvv:""}),s(de(n==="list"?"add":"list"))},C=async i=>{if(window.confirm("Are you sure you want to remove this card?"))try{await s(He(i)).unwrap(),ue("Card removed successfully")}catch(u){M(u.message||"Failed to remove card")}},w=async i=>{try{await s(Ge(i)).unwrap(),ue("Default card updated")}catch(u){M(u.message||"Failed to update default card")}},S=i=>{o(u=>({...u,nameOnCard:i}))},t=async i=>{if(i.preventDefault(),!m.nameOnCard.trim()){M("Please enter cardholder name");return}if(!x||!N){M("Payment form not ready. Please try again.");return}g(!0);const u=Ke("Adding your card...");try{const{error:j,paymentMethod:R}=await x.createPaymentMethod({type:"card",card:N,billing_details:{name:m.nameOnCard.trim()}});if(j)throw new Error(j.message);await s(Qe({paymentMethodId:R.id,isDefault:r.length===0})).unwrap(),me(u,"Card added successfully!","success"),o({nameOnCard:"",cardNumber:"",expiryDate:"",cvv:""}),s(de("list"))}catch(j){console.error("Error adding card:",j),me(u,j.message||"Failed to add card","error")}finally{g(!1)}},p=i=>{const u={visa:"https://js.stripe.com/v3/fingerprinted/img/visa-********************************.svg",mastercard:"https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg",amex:"https://js.stripe.com/v3/fingerprinted/img/amex-a49b82f46c5cd6a96a6ef9a6d86c802a.svg",discover:"https://js.stripe.com/v3/fingerprinted/img/discover-ac52cd46f89fa40a29a0bfded4bdb5cf.svg",diners:"https://js.stripe.com/v3/fingerprinted/img/diners-fbcbd024c13e67d8c3b7b24ace822024.svg",jcb:"https://js.stripe.com/v3/fingerprinted/img/jcb-271fd06e6e0a86955c6d560bdb81fb77.svg",unionpay:"https://js.stripe.com/v3/fingerprinted/img/unionpay-8a10aefc7295216c338ba4e1224627a1.svg"};return u[i]||u.mastercard};return f?e.jsx("div",{className:"BuyerCards",children:e.jsx(E,{icon:e.jsx(L,{className:"BuyerSidebar__icon"}),title:"My Cards",children:e.jsx("div",{className:"buyercardsbordercontainer",children:e.jsx("div",{className:"BuyerCards__loading",children:"Loading cards..."})})})}):e.jsx("div",{className:"BuyerCards",children:e.jsx(E,{icon:e.jsx(L,{className:"BuyerSidebar__icon"}),title:"My Cards",children:e.jsx("div",{className:"buyercardsbordercontainer",children:n==="list"?e.jsxs("div",{className:"BuyerCards__list-view",children:[e.jsxs("div",{className:"BuyerCards__header",children:[e.jsx("h3",{className:"BuyerCards__subtitle",children:"Saved Cards"}),e.jsxs("button",{className:"BuyerCards__add-btn",onClick:d,children:[e.jsx(Ye,{})," Add New Card"]})]}),e.jsx("div",{className:"BuyerCards__cards-list",children:r.length>0?r.map(i=>e.jsx("div",{className:"BuyerCards__card-item",children:e.jsxs("div",{className:"BuyerCards__card-content",children:[e.jsxs("div",{className:"BuyerCards__card-info",children:[e.jsx("div",{className:"BuyerCards__card-logo",children:e.jsx("img",{src:p(i.cardType),alt:i.cardType})}),e.jsxs("div",{className:"BuyerCards__card-details",children:[e.jsxs("div",{className:"BuyerCards__card-number",children:["•••• •••• •••• ",i.lastFourDigits]}),e.jsx("div",{className:"BuyerCards__card-name",children:i.cardholderName}),e.jsxs("div",{className:"BuyerCards__card-expiry",children:["Expires"," ",i.expiryMonth.toString().padStart(2,"0"),"/",i.expiryYear.toString().slice(-2)]})]})]}),e.jsxs("div",{className:"BuyerCards__card-actions",children:[e.jsx("button",{className:`BuyerCards__default-btn ${i.isDefault?"active":""}`,onClick:()=>!i.isDefault&&w(i._id),disabled:i.isDefault,title:i.isDefault?"Default card":"Set as default",children:i.isDefault?e.jsx(Ve,{}):e.jsx(ze,{})}),e.jsx("button",{className:"BuyerCards__delete-btn",onClick:()=>C(i._id),"aria-label":"Delete card",children:e.jsx(We,{className:"delete-icon"})})]})]})},i._id)):e.jsxs("div",{className:"BuyerCards__empty-state",children:[e.jsx(L,{className:"BuyerCards__empty-icon"}),e.jsx("p",{children:"You have no saved payment methods yet."}),e.jsx("button",{className:"BuyerCards__add-first-btn",onClick:d,children:"Add Your First Card"})]})})]}):e.jsxs("div",{className:"BuyerCards__add-view",children:[e.jsx("div",{className:"BuyerCards__header",children:e.jsx("h3",{className:"BuyerCards__subtitle",children:"Add New Card"})}),e.jsx("div",{className:"BuyerCards__form",children:e.jsxs("form",{onSubmit:t,children:[e.jsx("div",{className:"BuyerCards__form-row",children:e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--full",children:e.jsx("div",{className:"BuyerCards__input-container",children:e.jsx("input",{type:"text",id:"nameOnCard",name:"nameOnCard",value:m.nameOnCard,onChange:i=>S(i.target.value),placeholder:"Name on card",required:!0,className:"BuyerCards__input",disabled:l})})})}),e.jsx("div",{className:"BuyerCards__form-row",children:e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--full",children:e.jsx("div",{className:"BuyerCards__input-container",children:e.jsx("div",{ref:b,className:"BuyerCards__stripe-element"})})})}),e.jsxs("div",{className:"BuyerCards__form-actions",children:[e.jsx("button",{type:"submit",className:"BuyerCards__submit-btn btn-primary",disabled:l||!x,children:l?"Adding Card...":"Add Card"}),e.jsx("button",{className:"BuyerCards__cancel-btn ",onClick:d,disabled:l,children:"Cancel"})]})]})})]})})})})},ve=async(s,r={})=>{const{includeStats:n=!0,includeDownloads:f=!0,includeRequests:h=!0,includeBids:m=!0,includeStrategies:o=!0,includeNotifications:l=!0,strategiesParams:g={}}=r,x=[];n&&x.push(s(ge())),f&&x.push(s(Q())),h&&x.push(s(X())),m&&x.push(s(Xe())),o&&x.push(s(Je(g))),l&&x.push(s(Z()));try{await Promise.allSettled(x),console.log("Buyer dashboard data refreshed successfully")}catch(v){console.error("Error refreshing buyer dashboard data:",v)}},us=(s,r={})=>{const{statsInterval:n=5*60*1e3,notificationsInterval:f=30*1e3,generalInterval:h=2*60*1e3}=r,m={};return n>0&&(m.stats=setInterval(()=>{s(ge())},n)),f>0&&(m.notifications=setInterval(()=>{s(Z())},f)),h>0&&(m.general=setInterval(()=>{ve(s,{includeStats:!1,includeNotifications:!1,includeStrategies:!1})},h)),{cleanup:()=>{Object.values(m).forEach(o=>{o&&clearInterval(o)})},intervals:m}},ms=(s,r={})=>{const n=()=>{document.hidden||ve(s,{includeStats:!0,includeNotifications:!0,includeDownloads:!1,includeRequests:!1,includeBids:!1,includeStrategies:!1,...r})},f=()=>{s(Z())};return document.addEventListener("visibilitychange",n),window.addEventListener("focus",f),()=>{document.removeEventListener("visibilitychange",n),window.removeEventListener("focus",f)}},Cs=({children:s})=>{const r=F(),n=Ze(),f=_(es),h=c.useRef(null),m=c.useRef(null);c.useEffect(()=>{const l=n.pathname;l.includes("/dashboard")?r(I("dashboard")):l.includes("/profile")?r(I("profile")):l.includes("/downloads")?r(I("downloads")):l.includes("/requests")?r(I("requests")):l.includes("/bids")?r(I("bids")):l.includes("/offers")?r(I("offers")):l.includes("/cards")?r(I("cards")):r(I("dashboard"))},[n.pathname,r]),c.useEffect(()=>(h.current=us(r,{statsInterval:5*60*1e3,notificationsInterval:30*1e3,generalInterval:2*60*1e3}),m.current=ms(r),()=>{h.current&&h.current.cleanup(),m.current&&m.current()}),[r]);const o=()=>{if(s)return s;switch(f){case"dashboard":return e.jsx(he,{});case"profile":return e.jsx(rs,{});case"downloads":return e.jsx(is,{});case"requests":return e.jsx(ls,{});case"bids":return e.jsx(cs,{});case"offers":return e.jsx(os,{});case"cards":return e.jsx(ds,{});default:return e.jsx(he,{})}};return e.jsx(ts,{children:e.jsx("div",{className:"BuyerAccount",children:e.jsxs("div",{className:"container max-container",children:[e.jsx("div",{className:"sidebar",children:e.jsx(ss,{})}),e.jsx("div",{className:"contentArea",children:o()})]})})})};export{Cs as default};
