import{G as se,a as te,i as ae,r as d,b as g,S as ie,T as ce,U as re,V as ne,W as le,X as oe,Y as de,m as T,Z as he,$ as G,j as s,a0 as pe,a1 as me,a2 as ue,a3 as xe,J as ge,a4 as fe,a5 as je,a6 as ve}from"./index-oGr_1a_A.js";import{S as ye}from"./StrategyCard-SJbf1zrE.js";import{S as be}from"./LoadingSkeleton-DmDWH340.js";import{P as Ne}from"./Pagination-Chk7MbK3.js";/* empty css                        */import"./index-auB_fJid.js";function Se(r){return se({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M337.509 305.372h-17.501l-6.571-5.486c20.791-25.232 33.922-57.054 33.922-93.257C347.358 127.632 283.896 64 205.135 64 127.452 64 64 127.632 64 206.629s63.452 142.628 142.225 142.628c35.011 0 67.831-13.167 92.991-34.008l6.561 5.487v17.551L415.18 448 448 415.086 337.509 305.372zm-131.284 0c-54.702 0-98.463-43.887-98.463-98.743 0-54.858 43.761-98.742 98.463-98.742 54.7 0 98.462 43.884 98.462 98.742 0 54.856-43.762 98.743-98.462 98.743z"},child:[]}]})(r)}const Fe=()=>{var $,B,I,U,_;const r=te(),P=ae(),[C,k]=d.useState(!1),j=g(ie),w=g(ce),R=g(re),u=g(e=>ne(e,"strategies")),v=g(e=>le(e,"strategies")),y=g(e=>oe(e,"strategies")),n=g(de),z=()=>new URLSearchParams(P.search).get("sport")||u.sport,[b,W]=d.useState(y.page),[x,F]=d.useState(z()),[N,H]=d.useState(u.sortBy),J=[{id:"Fixed",label:"Buy Now Content"},{id:"Auction",label:"Bid Now Content"}],V=n.sports||[],X=n.contentTypes||[],Y=n.difficultyLevels||[],[a,S]=d.useState(u.priceRange),[Z,p]=d.useState(!1),[E,m]=d.useState(!1),[c,M]=d.useState({saleType:u.saleType.reduce((e,t)=>({...e,[t]:!0}),{}),contentType:u.contentType.reduce((e,t)=>({...e,[t]:!0}),{}),difficulty:u.difficulty.reduce((e,t)=>({...e,[t]:!0}),{})});d.useEffect(()=>{const t=new URLSearchParams(P.search).get("sport");t&&(F(t),r(T({section:"strategies",filters:{sport:t}})))},[P.search,r]),d.useEffect(()=>{r(he())},[r]),d.useEffect(()=>{var e;if((e=n.priceRange)!=null&&e.maxPrice&&n.priceRange.maxPrice>0){const t=n.priceRange.maxPrice;(a[1]===5e3||a[1]===1e3)&&S([0,t])}},[($=n.priceRange)==null?void 0:$.maxPrice]),d.useEffect(()=>{var f;if(E)return;const e=Object.keys(c.saleType).filter(i=>c.saleType[i]),t=Object.keys(c.contentType).filter(i=>c.contentType[i]),l=Object.keys(c.difficulty).filter(i=>c.difficulty[i]),h=((f=n.priceRange)==null?void 0:f.maxPrice)||5e3,o={page:b,limit:9,sport:x==="All"?void 0:x,saleType:e.length>0?e:void 0,contentType:t.length>0?t:void 0,difficulty:l.length>0?l:void 0,priceMin:a[0]>0?a[0]:void 0,priceMax:a[1]<h?a[1]:void 0,sortBy:N,search:v||void 0};Object.keys(o).forEach(i=>{(o[i]===void 0||Array.isArray(o[i])&&o[i].length===0)&&delete o[i]}),r(G(o))},[r,b,x,c,a,N,v,E,(B=n.priceRange)==null?void 0:B.maxPrice]);const O=()=>{var f;r(fe("strategies"));const e=Object.keys(c.saleType).filter(i=>c.saleType[i]),t=Object.keys(c.contentType).filter(i=>c.contentType[i]),l=Object.keys(c.difficulty).filter(i=>c.difficulty[i]),h=((f=n.priceRange)==null?void 0:f.maxPrice)||5e3,o={page:b,limit:9,sport:x==="All"?void 0:x,saleType:e.length>0?e:void 0,contentType:t.length>0?t:void 0,difficulty:l.length>0?l:void 0,priceMin:a[0]>0?a[0]:void 0,priceMax:a[1]<h?a[1]:void 0,sortBy:N,search:v||void 0};Object.keys(o).forEach(i=>{(o[i]===void 0||Array.isArray(o[i])&&o[i].length===0)&&delete o[i]}),r(G(o))},L=()=>{k(!C)},q=e=>{e.target.classList.contains("filter-overlay")&&k(!1)};d.useEffect(()=>{const e=t=>{t.key==="Escape"&&k(!1)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[]);const A=(e,t,l)=>{M(h=>({...h,[e]:{...h[e],[t]:l}}))},K=e=>{const t=e.target.value;r(je({section:"strategies",searchTerm:t}))},Q=e=>{const t=e.target.value;H(t),r(T({section:"strategies",filters:{...u,sortBy:t}}))},ee=e=>{W(e),r(ve({section:"strategies",pagination:{...y,page:e}}))},D=()=>{var h;const e={saleType:{},contentType:{},difficulty:{}},l=[0,((h=n.priceRange)==null?void 0:h.maxPrice)||5e3];M(e),S(l),F("All"),r(T({section:"strategies",filters:{sport:"All",saleType:[],contentType:[],difficulty:[],priceRange:l,sortBy:"newest"}}))};return s.jsx("div",{className:"buyer-dashboard",children:s.jsxs("div",{className:" max-container",children:[C&&s.jsx("div",{className:"filter-overlay",onClick:q}),s.jsxs("div",{className:`filter-section ${C?"drawer-open":""}`,children:[s.jsx("button",{className:"close-drawer-btn",onClick:L,children:s.jsx(pe,{})}),s.jsxs("div",{className:"filter-header",children:[s.jsx("h3",{children:"Filter By"}),s.jsx("button",{className:"clear-all",onClick:D,children:"Clear All"})]}),s.jsxs("div",{className:"filter-group",children:[s.jsx("h4",{children:"Sport"}),s.jsx("div",{className:"sport-select-wrapper",children:s.jsxs("select",{className:"sport-select",value:x,onChange:e=>{const t=e.target.value;F(t),r(T({section:"strategies",filters:{sport:t}}))},children:[s.jsx("option",{value:"All",children:"All Sports"}),V.map(e=>s.jsx("option",{value:e,children:e},e))]})})]}),s.jsxs("div",{className:"filter-group",children:[s.jsx("h4",{children:"Sale Type"}),s.jsx("div",{className:"checkbox-group",children:J.map(e=>s.jsxs("div",{className:"checkbox-item",children:[s.jsx("input",{type:"checkbox",id:`saleType-${e.id}`,checked:c.saleType[e.id]||!1,onChange:t=>A("saleType",e.id,t.target.checked)}),s.jsx("label",{htmlFor:`saleType-${e.id}`,children:e.label})]},e.id))})]}),s.jsxs("div",{className:"filter-group",children:[s.jsx("h4",{children:"Content Type"}),s.jsx("div",{className:"checkbox-group",children:X.map(e=>s.jsxs("div",{className:"checkbox-item",children:[s.jsx("input",{type:"checkbox",id:`contentType-${e}`,checked:c.contentType[e]||!1,onChange:t=>A("contentType",e,t.target.checked)}),s.jsx("label",{htmlFor:`contentType-${e}`,children:e})]},e))})]}),s.jsxs("div",{className:"filter-group",children:[s.jsx("h4",{children:"Difficulty Level"}),s.jsx("div",{className:"checkbox-group",children:Y.map(e=>s.jsxs("div",{className:"checkbox-item",children:[s.jsx("input",{type:"checkbox",id:`difficulty-${e}`,checked:c.difficulty[e]||!1,onChange:t=>A("difficulty",e,t.target.checked)}),s.jsx("label",{htmlFor:`difficulty-${e}`,children:e})]},e))})]}),s.jsxs("div",{className:"filter-group",children:[s.jsx("h4",{children:"Price"}),s.jsxs("div",{className:"price-range",children:[s.jsxs("div",{className:`price-slider-container ${Z?"active":""}`,style:{"--min-value":a[0],"--max-value":a[1],"--max-price":((I=n.priceRange)==null?void 0:I.maxPrice)||5e3},children:[s.jsx("input",{type:"range",min:"0",max:((U=n.priceRange)==null?void 0:U.maxPrice)||5e3,step:"1",value:a[0],onChange:e=>{const t=parseInt(e.target.value);t<a[1]-1&&S([t,a[1]])},onMouseDown:()=>{p(!0),m(!0)},onMouseUp:()=>{p(!1),m(!1)},onTouchStart:()=>{p(!0),m(!0)},onTouchEnd:()=>{p(!1),m(!1)},className:"price-slider min-slider"}),s.jsx("input",{type:"range",min:"0",max:((_=n.priceRange)==null?void 0:_.maxPrice)||5e3,step:"1",value:a[1],onChange:e=>{const t=parseInt(e.target.value);t>a[0]+1&&S([a[0],t])},onMouseDown:()=>{p(!0),m(!0)},onMouseUp:()=>{p(!1),m(!1)},onTouchStart:()=>{p(!0),m(!0)},onTouchEnd:()=>{p(!1),m(!1)},className:"price-slider max-slider"})]}),s.jsxs("div",{className:"price-labels",children:[s.jsxs("span",{children:["$",a[0].toLocaleString()]}),s.jsxs("span",{children:["$",a[1].toLocaleString()]})]})]})]})]}),s.jsxs("div",{className:"content-section",children:[s.jsxs("div",{className:"content-header",children:[s.jsxs("div",{className:"content-title-section",children:[s.jsxs("h2",{className:"content-title",children:["Sports Content",s.jsx("span",{children:w.strategies?"(Loading...)":`(${y.total||j.length} Contents Found)`})]}),R.strategies&&s.jsx("button",{className:"retry-btn",onClick:O,title:"Retry loading content",children:s.jsx(me,{})})]}),s.jsxs("div",{className:"search-sort",children:[s.jsxs("div",{className:"search-container",children:[s.jsx("input",{type:"text",placeholder:"Search...",value:v,onChange:K,className:"search-input"}),s.jsx("button",{className:"search-button",children:s.jsx(Se,{})})]}),s.jsx("div",{className:"sort-container",children:s.jsxs("select",{id:"sort",className:"sort-select",value:N,onChange:Q,children:[s.jsx("option",{value:"newest",children:"Newest first"}),s.jsx("option",{value:"oldest",children:"Oldest first"}),s.jsx("option",{value:"price_desc",children:"Price high to low"}),s.jsx("option",{value:"price_asc",children:"Price low to high"}),s.jsx("option",{value:"rating",children:"Highest rated"}),s.jsx("option",{value:"rating_asc",children:"Lowest rated"})]})})]})]}),s.jsxs("button",{className:"filter-toggle-btn",onClick:L,children:["Filter ",s.jsx(ue,{})]}),R.strategies?s.jsx(xe,{error:R.strategies,onRetry:O,title:"Failed to load content",className:"strategies-error"}):w.strategies?s.jsx(be,{count:9}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"strategy-grid",children:j.length>0?j.map(e=>{var t,l,h;return s.jsx(ye,{id:e._id||e.id,image:e.thumbnailUrl?e.thumbnailUrl.startsWith("http")?e.thumbnailUrl:`${ge}${e.thumbnailUrl}`:"https://via.placeholder.com/300x200?text=No+Image",title:e.title,coach:(t=e.seller)!=null&&t.firstName&&((l=e.seller)!=null&&l.lastName)?`${e.seller.firstName} ${e.seller.lastName}`:e.coachName||"Unknown Coach",price:e.saleType==="Auction"&&((h=e.auctionDetails)!=null&&h.basePrice)?e.auctionDetails.basePrice:e.price||0,contentType:e.contentType,saleType:e.saleType,auctionDetails:e.auctionDetails},e._id||e.id)}):s.jsxs("div",{className:"empty-state",children:[s.jsx("h3",{children:"No content found"}),s.jsx("p",{children:x!=="All"||Object.values(c).some(e=>Object.values(e).some(t=>t))?"Try adjusting your filters or search terms.":"No content available at the moment."}),s.jsx("button",{className:"clear-filters-btn",onClick:D,children:"Clear All Filters"})]})}),j.length>0&&s.jsx(Ne,{currentPage:b,totalPages:Math.ceil(y.total/9),onPageChange:ee,isLoading:w.strategies})]})]})]})})};export{Fe as default};
