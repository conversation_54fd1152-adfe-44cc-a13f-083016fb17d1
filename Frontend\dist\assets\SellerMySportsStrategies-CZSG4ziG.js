import{a as E,u as F,b as $,r as c,bU as x,a9 as r,j as t,au as M,aV as V,bV as L,bc as U,bW as _,bX as Y,bY as O}from"./index-oGr_1a_A.js";import{S as f}from"./SellerLayout-Du2JNoeS.js";import{T as R}from"./Table-67eaNy4J.js";import{P as B}from"./Pagination-Chk7MbK3.js";import{a as W}from"./dateValidation-YzamylQ0.js";import{C as X}from"./ConfirmationModal-D5BYMx4l.js";import"./index-auB_fJid.js";import"./timezoneUtils-Dky5bF8c.js";const Z=()=>{const l=E(),h=F(),{sellerContent:m,isLoading:o,error:d,pagination:s}=$(e=>e.content),[p,u]=c.useState(!1),[i,g]=c.useState(null);c.useEffect(()=>{l(x({page:s.page,limit:s.limit}))},[l,s.page]),c.useEffect(()=>{d&&r.error(d.message||"Failed to load strategies")},[d]);const j=async e=>{try{await l(O(e)).unwrap(),r.success("Strategy status updated successfully")}catch(a){r.error(a.message||"Failed to update strategy status")}},b=e=>{h(`/seller/strategy-details/${e}`)},D=e=>{h(`/seller/strategy-details/${e}/edit`)},S=e=>{g(e),u(!0)},N=async()=>{if(i)try{await l(Y(i._id)).unwrap(),r.success("Strategy deleted successfully"),u(!1),g(null),l(x({page:s.page,limit:s.limit}))}catch(e){r.error(e.message||"Failed to delete strategy")}},v=()=>{u(!1),g(null)},C=e=>W(e),w=e=>{var n;let a;return e.saleType==="Auction"&&((n=e.auctionDetails)!=null&&n.basePrice)?a=e.auctionDetails.basePrice:a=e.price,typeof a=="number"?`$${a.toFixed(2)}`:"$0.00"},k=e=>{var y;if(e.saleType!=="Auction")return!1;const a=new Date,n=(y=e.auctionDetails)!=null&&y.auctionStartDate?new Date(e.auctionDetails.auctionStartDate):null;return n&&a>=n},P=[{key:"no",label:"No.",className:"no"},{key:"content",label:"Videos/Documents",render:e=>t.jsxs("div",{className:"video-doc",children:[t.jsx("div",{className:"video-thumbnail",children:e.thumbnailUrl?t.jsx("img",{src:M(e.thumbnailUrl),alt:e.title}):t.jsx("div",{className:"placeholder-thumb",children:e.contentType==="Video"?"📹":"📄"})}),t.jsx("span",{className:"video-title",children:e.title})]})},{key:"date",label:"Date",render:e=>C(e.createdAt)},{key:"price",label:"Price",render:e=>w(e)},{key:"status",label:"Status",render:e=>t.jsxs("label",{className:"switch",children:[t.jsx("input",{type:"checkbox",checked:e.isActive===1,onChange:()=>j(e._id),disabled:o}),t.jsx("span",{className:"slider round"})]})},{key:"action",label:"Action",render:e=>{const a=k(e);return t.jsxs("div",{className:"action-icon-container",children:[t.jsx(V,{className:"action-icon eyeicon",onClick:()=>b(e._id),title:"View Details"}),t.jsx(L,{className:`edit-icon ${a?"disabled":""}`,onClick:a?void 0:()=>D(e._id),title:a?"You can't edit the strategy once the auction has started.":"Edit Strategy",style:{cursor:a?"not-allowed":"pointer",opacity:a?.5:1}}),t.jsx(U,{className:" delete-icon",onClick:()=>S(e),title:"Delete Strategy"})]})}}],T=e=>e.map((a,n)=>({...a,no:n+1})),A=e=>{l(_(e))};return o?t.jsx(f,{children:t.jsx("div",{className:"video-status-container",children:t.jsxs("div",{className:"loading-container",children:[t.jsx("div",{className:"loading-spinner"}),t.jsx("p",{children:"Loading strategies..."})]})})}):t.jsxs(f,{children:[t.jsx("div",{className:"video-status-container",children:m.length===0?t.jsxs("div",{className:"empty-state",children:[t.jsx("h3",{children:"No strategies found"}),t.jsx("p",{children:`You haven't created any strategies yet. Click "Add New Strategy" to get started.`})]}):t.jsxs(t.Fragment,{children:[t.jsx(R,{columns:P,data:T(m),isLoading:o}),t.jsx(B,{currentPage:s.page,totalPages:s.totalPages,onPageChange:A,isLoading:o})]})}),p&&i&&t.jsx(X,{isOpen:p,onConfirm:N,onClose:v,title:"Delete Strategy",message:`Are you sure you want to delete "${i==null?void 0:i.title}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",type:"danger"})]})};export{Z as default};
