.fullscreen-viewer__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.fullscreen-viewer__container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.fullscreen-viewer__container.fullscreen {
  max-width: 100%;
  height: 100%;
  border-radius: 0;
}

.fullscreen-viewer__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color);
}

.fullscreen-viewer__title {
  margin: 0;
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fullscreen-viewer__controls {
  display: flex;
  gap: 8px;
}

.fullscreen-viewer__control-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  color: var(--text-color);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.fullscreen-viewer__control-btn:hover {
  background-color: var(--bg-gray);
  color: var(--primary-color);
}

.fullscreen-viewer__content {
  flex: 1;
  overflow: auto;
  position: relative;
  background-color: var(--bg-gray);
  -webkit-overflow-scrolling: touch;
}

/* PDF Viewer Styles */
.fullscreen-viewer__pdf {
  height: 100% !important;
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 20px;
}

/* Video Player Styles */
.fullscreen-viewer__video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: black;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .fullscreen-viewer__overlay {
    padding: 0;
  }

  .fullscreen-viewer__container {
    height: 100%;
    border-radius: 0;
  }

  .fullscreen-viewer__header {
    padding: 12px 16px;
  }

  .fullscreen-viewer__title {
    font-size: var(--basefont);
  }

  .fullscreen-viewer__control-btn {
    padding: 6px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .fullscreen-viewer__content {
    touch-action: pan-y pinch-zoom;
  }

  .fullscreen-viewer__control-btn {
    min-width: 44px;
    min-height: 44px;
  }
} 