import{j as s}from"./index-oGr_1a_A.js";const p=({columns:r,data:d,onRowClick:t,renderRow:c,className:i="",emptyMessage:j="No data available",variant:b="default",gridTemplate:h=""})=>{const x=e=>{t&&t(e)};return s.jsx("div",{className:`table-container ${b} ${i}`,children:b==="grid"?s.jsxs("div",{className:`table ${i}`,children:[s.jsx("div",{className:"table-header",style:h?{gridTemplateColumns:h}:{},children:r.map(e=>s.jsx("div",{className:`table-cell ${e.className||""}`,children:e.label},e.key))}),d.length>0?d.map((e,l)=>s.jsx("div",{className:"table-row",style:h?{gridTemplateColumns:h}:{},onClick:()=>x(e),children:c?c(e,l):r.map(a=>s.jsx("div",{className:`table-cell ${a.className||""}`,children:a.render?a.render(e,l):e[a.key]},a.key))},e.id||l)):s.jsx("div",{className:"table-row empty-row",children:s.jsx("div",{className:"table-cell full-span empty-message",children:j})})]}):s.jsxs("table",{className:`table ${i}`,children:[s.jsx("thead",{children:s.jsx("tr",{children:r.map(e=>s.jsx("th",{className:e.className||"",children:e.label},e.key))})}),s.jsx("tbody",{children:d.length>0?d.map((e,l)=>s.jsx("tr",{onClick:()=>x(e),className:t?"clickable":"",children:c?c(e,l):r.map(a=>s.jsx("td",{className:a.className||"",children:a.render?a.render(e,l):e[a.key]},a.key))},e.id||l)):s.jsx("tr",{children:s.jsx("td",{colSpan:r.length,className:"empty-message",children:j})})})]})})};export{p as T};
