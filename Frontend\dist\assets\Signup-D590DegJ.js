import{u as T,a as S,b as C,r as h,j as e,F as _,M as k,L as x,c as N,e as w,t as o,g as E,d as P,f as F}from"./index-oGr_1a_A.js";import{H as $,G as q,f}from"./index-BtoIuJph.js";const R=()=>{const l=T(),c=S(),{isLoading:d,isError:D,isSuccess:M,error:G}=C(s=>s.auth),[r,p]=h.useState({firstName:"",lastName:"",email:"",phone:"",countryCode:"+91",accountType:"learn",agreeToTerms:!1}),[a,g]=h.useState({}),u=s=>{const{name:i,value:n,type:t,checked:m}=s.target;p({...r,[i]:t==="checkbox"?m:n}),a[i]&&g({...a,[i]:null})},j=s=>{p({...r,countryCode:s.target.value})},y=()=>{const s={};return r.firstName.trim()||(s.firstName="First name is required"),r.lastName.trim()||(s.lastName="Last name is required"),r.email.trim()?/\S+@\S+\.\S+/.test(r.email)||(s.email="Email is invalid"):s.email="Email is required",r.phone.trim()?/^\d{10}$/.test(r.phone)||(s.phone="Phone number must be 10 digits"):s.phone="Phone number is required",r.agreeToTerms||(s.agreeToTerms="You must agree to the terms and conditions"),s},v=async s=>{s.preventDefault();const i=y();if(Object.keys(i).length>0){g(i);return}c(N());try{const n={firstName:r.firstName,lastName:r.lastName,email:r.email,mobile:`${r.countryCode}${r.phone}`,role:r.accountType==="learn"?"buyer":"seller"},t=await c(w(n)).unwrap();o.auth.registrationSuccess(),l("/otp-verification",{state:{userId:t.userId,phoneNumber:`${r.countryCode} ${r.phone}`,cooldownSeconds:t.cooldownSeconds||60,isLogin:!1,developmentOtp:t.developmentOtp}})}catch(n){console.error("Registration error:",n);let t="Registration failed. Please try again.";typeof n=="string"?t=n:n!=null&&n.message&&(t=n.message),t.includes("Email already registered")?o.error("This email is already registered. Please try logging in instead."):t.includes("Mobile number already registered")?o.error("This mobile number is already registered. Please try logging in instead."):t.includes("already registered")?o.error("This email or mobile number is already registered. Please try logging in instead."):o.error(t)}},b=async()=>{try{if(c(N()),!f.isInitialized()){o.error("Firebase is not initialized. Please check your configuration.");return}const s=await f.signInWithGoogle();try{const i=await c(E(s.idToken)).unwrap();if(o.info("Account already exists. Redirecting to dashboard..."),i.user.role==="buyer")l("/content");else if(i.user.role==="seller"){const n=P(i.user);l(n)}else i.user.role==="admin"?l("/admin/dashboard"):l("/")}catch(i){const n=typeof i=="string"?i:(i==null?void 0:i.message)||"";if(n.includes("not found")||n.includes("does not exist")){const t="buyer";try{await c(F({idToken:s.idToken,role:t})).unwrap(),o.auth.registrationSuccess(),t==="buyer"&&l("/content")}catch(m){throw m}}else throw i}}catch(s){console.error("Google sign-up error:",s);let i="Failed to sign up with Google. Please try again.";typeof s=="string"?i=s:s!=null&&s.message&&(i=s.message),o.error(i)}};return e.jsx("div",{className:"signup__page",children:e.jsxs("div",{className:"signup__container",children:[e.jsx("h1",{className:"signup__title",children:"Sign up to your account"}),e.jsxs("form",{onSubmit:v,className:"signup__form",children:[e.jsxs("div",{className:"signup__form-row",children:[e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(_,{})}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:r.firstName,onChange:u,placeholder:"First Name",className:`signup__input ${a.firstName?"signup__input--error":""}`,required:!0}),a.firstName&&e.jsx("p",{className:"signup__error",children:a.firstName})]}),e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(_,{})}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:r.lastName,onChange:u,placeholder:"Last Name",className:`signup__input ${a.lastName?"signup__input--error":""}`,required:!0}),a.lastName&&e.jsx("p",{className:"signup__error",children:a.lastName})]})]}),e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(k,{})}),e.jsx("input",{type:"email",id:"email",name:"email",value:r.email,onChange:u,placeholder:"Enter Email Address",className:`signup__input ${a.email?"signup__input--error":""}`,required:!0}),a.email&&e.jsx("p",{className:"signup__error",children:a.email})]}),e.jsxs("div",{className:"signup__form-input signup__phone-container",children:[e.jsxs("div",{className:"signup__phone-wrapper",children:[e.jsx("div",{children:e.jsxs("div",{className:"signup__country-code-select",children:[e.jsx($,{style:{color:"var(--dark-gray)",fontSize:"var(--heading5)"}}),e.jsxs("select",{value:r.countryCode,onChange:j,className:"selectstylesnone",children:[e.jsx("option",{value:"+91",children:"+91"}),e.jsx("option",{value:"+1",children:"+1"}),e.jsx("option",{value:"+44",children:"+44"}),e.jsx("option",{value:"+61",children:"+61"}),e.jsx("option",{value:"+86",children:"+86"}),e.jsx("option",{value:"+49",children:"+49"}),e.jsx("option",{value:"+33",children:"+33"}),e.jsx("option",{value:"+81",children:"+81"}),e.jsx("option",{value:"+7",children:"+7"}),e.jsx("option",{value:"+55",children:"+55"})]})]})}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:r.phone,onChange:s=>{const i=s.target.value.replace(/\D/g,"");u({target:{name:"phone",value:i}})},placeholder:"00000 00000",className:`signup__form-input signup__phone-input ${a.phone?"signup__input--error":""}`,required:!0,pattern:"[0-9]*"})]}),a.phone&&e.jsx("p",{className:"error-message",children:a.phone})]}),e.jsxs("div",{className:"signup__terms",children:[e.jsx("input",{type:"checkbox",id:"agreeToTerms",name:"agreeToTerms",checked:r.agreeToTerms,onChange:u,className:"signup__checkbox"}),e.jsxs("label",{htmlFor:"agreeToTerms",className:"signup__terms-label",children:["By sign up you agree to our"," ",e.jsx(x,{to:"/terms",className:"signup__terms-link",children:"Terms & Conditions"})]}),a.agreeToTerms&&e.jsx("p",{className:"signup__error",children:a.agreeToTerms})]}),e.jsx("button",{type:"submit",className:"signup__button btn-primary",disabled:d,children:d?"Creating Account...":"Create Your Account"}),e.jsx("div",{className:"signup__divider",children:e.jsx("span",{children:"or"})}),e.jsx(q,{onClick:b,isLoading:d,text:"Sign up with Google",variant:"secondary"}),e.jsxs("p",{className:"signup__login-link mt-10",children:["Do you have an account?"," ",e.jsx(x,{to:"/auth",className:"signup__link",children:"Sign In"})]})]})]})})};export{R as default};
