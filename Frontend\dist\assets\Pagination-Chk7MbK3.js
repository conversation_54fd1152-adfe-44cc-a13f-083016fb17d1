import{j as o}from"./index-oGr_1a_A.js";import{P as p}from"./index-auB_fJid.js";const r=({currentPage:s,totalPages:n,onPageChange:e,isLoading:a=!1,className:m=""})=>{const t=()=>{const l=[];l.push(o.jsx("button",{className:"pagination-arrow",onClick:()=>s>1&&e(s-1),disabled:s===1||a,children:"<"},"prev"));for(let i=1;i<=n;i++)i===1||i===n||i>=s-1&&i<=s+1?l.push(o.jsx("button",{className:`pagination-item ${s===i?"active":""}`,onClick:()=>e(i),disabled:a,children:i},i)):(i===s-2||i===s+2)&&l.push(o.jsx("span",{className:"pagination-ellipsis",children:"..."},`ellipsis-${i}`));return l.push(o.jsx("button",{className:"pagination-arrow",onClick:()=>s<n&&e(s+1),disabled:s===n||a,children:">"},"next")),l};return n<=1?null:o.jsx("div",{className:`pagination ${m}`.trim(),children:t()})};r.propTypes={currentPage:p.number.isRequired,totalPages:p.number.isRequired,onPageChange:p.func.isRequired,isLoading:p.bool,className:p.string};export{r as P};
