import{cb as Os}from"./index-oGr_1a_A.js";import{r as js}from"./jquery-BQXThELV.js";function Us(te,et){for(var X=0;X<et.length;X++){const K=et[X];if(typeof K!="string"&&!Array.isArray(K)){for(const M in K)if(M!=="default"&&!(M in te)){const D=Object.getOwnPropertyDescriptor(K,M);D&&Object.defineProperty(te,M,D.get?D:{enumerable:!0,get:()=>K[M]})}}}return Object.freeze(Object.defineProperty(te,Symbol.toStringTag,{value:"Module"}))}var Ct={exports:{}};/*!
 * 
 * Super simple WYSIWYG editor v0.9.1
 * https://summernote.org
 *
 * Copyright 2013~ Hackerwins and contributors
 * Summernote may be freely distributed under the MIT license.
 *
 * Date: 2024-10-09T10:28Z
 *
 */var so;function Ks(){return so||(so=1,function(te,et){(function(K,M){te.exports=M(js())})(self,X=>(()=>{var K={7e3:(o,t,e)=>{var n=e(8938),i=e.n(n);i().summernote=i().summernote||{lang:{}},i().extend(!0,i().summernote.lang,{"en-US":{font:{bold:"Bold",italic:"Italic",underline:"Underline",clear:"Remove Font Style",height:"Line Height",name:"Font Family",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript",size:"Font Size",sizeunit:"Font Size Unit"},image:{image:"Picture",insert:"Insert Image",resizeFull:"Resize full",resizeHalf:"Resize half",resizeQuarter:"Resize quarter",resizeNone:"Original size",floatLeft:"Float Left",floatRight:"Float Right",floatNone:"Remove float",shapeRounded:"Shape: Rounded",shapeCircle:"Shape: Circle",shapeThumbnail:"Shape: Thumbnail",shapeNone:"Shape: None",dragImageHere:"Drag image or text here",dropImage:"Drop image or Text",selectFromFiles:"Select from files",maximumFileSize:"Maximum file size",maximumFileSizeError:"Maximum file size exceeded.",url:"Image URL",remove:"Remove Image",original:"Original"},video:{video:"Video",videoLink:"Video Link",insert:"Insert Video",url:"Video URL",providers:"(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion, Youku, Peertube)"},link:{link:"Link",insert:"Insert Link",unlink:"Unlink",edit:"Edit",textToDisplay:"Text to display",url:"To what URL should this link go?",openInNewWindow:"Open in new window"},table:{table:"Table",addRowAbove:"Add row above",addRowBelow:"Add row below",addColLeft:"Add column left",addColRight:"Add column right",delRow:"Delete row",delCol:"Delete column",delTable:"Delete table"},hr:{insert:"Insert Horizontal Rule"},style:{style:"Style",p:"Normal",blockquote:"Quote",pre:"Code",h1:"Header 1",h2:"Header 2",h3:"Header 3",h4:"Header 4",h5:"Header 5",h6:"Header 6"},lists:{unordered:"Unordered list",ordered:"Ordered list"},options:{help:"Help",fullscreen:"Full Screen",codeview:"Code View"},paragraph:{paragraph:"Paragraph",outdent:"Outdent",indent:"Indent",left:"Align left",center:"Align center",right:"Align right",justify:"Justify full"},color:{recent:"Recent Color",more:"More Color",background:"Background Color",foreground:"Text Color",transparent:"Transparent",setTransparent:"Set transparent",reset:"Reset",resetToDefault:"Reset to default",cpSelect:"Select"},shortcut:{shortcuts:"Keyboard shortcuts",close:"Close",textFormatting:"Text formatting",action:"Action",paragraphFormatting:"Paragraph formatting",documentStyle:"Document Style",extraKeys:"Extra keys"},help:{escape:"Escape",insertParagraph:"Insert Paragraph",undo:"Undo the last command",redo:"Redo the last command",tab:"Tab",untab:"Untab",bold:"Set a bold style",italic:"Set a italic style",underline:"Set a underline style",strikethrough:"Set a strikethrough style",removeFormat:"Clean a style",justifyLeft:"Set left align",justifyCenter:"Set center align",justifyRight:"Set right align",justifyFull:"Set full align",insertUnorderedList:"Toggle unordered list",insertOrderedList:"Toggle ordered list",outdent:"Outdent on current paragraph",indent:"Indent on current paragraph",formatPara:"Change current block's format as a paragraph(P tag)",formatH1:"Change current block's format as H1",formatH2:"Change current block's format as H2",formatH3:"Change current block's format as H3",formatH4:"Change current block's format as H4",formatH5:"Change current block's format as H5",formatH6:"Change current block's format as H6",insertHorizontalRule:"Insert horizontal rule","linkDialog.show":"Show Link Dialog"},history:{undo:"Undo",redo:"Redo"},specialChar:{specialChar:"SPECIAL CHARACTERS",select:"Select Special characters"},output:{noSelection:"No Selection Made!"}}})},8938:o=>{o.exports=X}},M={};function D(o){var t=M[o];if(t!==void 0)return t.exports;var e=M[o]={exports:{}};return K[o](e,e.exports,D),e.exports}D.n=o=>{var t=o&&o.__esModule?()=>o.default:()=>o;return D.d(t,{a:t}),t},D.d=(o,t)=>{for(var e in t)D.o(t,e)&&!D.o(o,e)&&Object.defineProperty(o,e,{enumerable:!0,get:t[e]})},D.o=(o,t)=>Object.prototype.hasOwnProperty.call(o,t);var co={},uo=D(8938),u=D.n(uo);D(7e3);var St=["sans-serif","serif","monospace","cursive","fantasy"];function Pt(o){return u().inArray(o.toLowerCase(),St)===-1?"'".concat(o,"'"):o}function fo(){var o="mw",t="20px",e=40,n=20,i=document.createElement("canvas"),r=i.getContext("2d",{willReadFrequently:!0});i.width=e,i.height=n,r.textAlign="center",r.fillStyle="black",r.textBaseline="middle";function a(s,c){r.clearRect(0,0,e,n),r.font=t+" "+Pt(s)+', "'+c+'"',r.fillText(o,e/2,n/2);var f=r.getImageData(0,0,e,n).data;return f.join("")}return function(s){var c=s==="Comic Sans MS"?"Courier New":"Comic Sans MS",f=a(c,c),d=a(s,c);return f!==d}}var O=navigator.userAgent,tt=/MSIE|Trident/i.test(O),ot;if(tt){var oe=/MSIE (\d+[.]\d+)/.exec(O);oe&&(ot=parseFloat(oe[1])),oe=/Trident\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(O),oe&&(ot=parseFloat(oe[1]))}var ne=/Edge\/\d+/.test(O),ho="ontouchstart"in window||navigator.MaxTouchPoints>0||navigator.msMaxTouchPoints>0,po=tt?"DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted":"input";const I={isMac:navigator.appVersion.indexOf("Mac")>-1,isMSIE:tt,isEdge:ne,isFF:!ne&&/firefox/i.test(O),isPhantom:/PhantomJS/i.test(O),isWebkit:!ne&&/webkit/i.test(O),isChrome:!ne&&/chrome/i.test(O),isSafari:!ne&&/safari/i.test(O)&&!/chrome/i.test(O),browserVersion:ot,isSupportTouch:ho,isFontInstalled:fo(),isW3CRangeSupport:!!document.createRange,inputEventName:po,genericFontFamilies:St,validFontName:Pt};function vo(o){return function(t){return o===t}}function mo(o,t){return o===t}function go(o){return function(t,e){return t[o]===e[o]}}function bo(){return!0}function yo(){return!1}function ko(o){return function(){return!o.apply(o,arguments)}}function wo(o,t){return function(e){return o(e)&&t(e)}}function Co(o){return o}function So(o,t){return function(){return o[t].apply(o,arguments)}}var xt=0;function Po(){xt=0}function xo(o){var t=++xt+"";return o?o+t:t}function To(o){var t=u()(document);return{top:o.top+t.scrollTop(),left:o.left+t.scrollLeft(),width:o.right-o.left,height:o.bottom-o.top}}function Eo(o){var t={};for(var e in o)Object.prototype.hasOwnProperty.call(o,e)&&(t[o[e]]=e);return t}function Io(o,t){return t=t||"",t+o.split(".").map(function(e){return e.substring(0,1).toUpperCase()+e.substring(1)}).join("")}function _o(o,t,e){var n;return function(){var i=this,r=arguments,a=function(){n=null,e||o.apply(i,r)},s=e&&!n;clearTimeout(n),n=setTimeout(a,t),s&&o.apply(i,r)}}function Ao(o){var t=/[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/gi;return t.test(o)}const k={eq:vo,eq2:mo,peq2:go,ok:bo,fail:yo,self:Co,not:ko,and:wo,invoke:So,resetUniqueId:Po,uniqueId:xo,rect2bnd:To,invertObject:Eo,namespaceToCamel:Io,debounce:_o,isValidUrl:Ao};function Tt(o){return o[0]}function nt(o){return o[o.length-1]}function Lo(o){return o.slice(0,o.length-1)}function Et(o){return o.slice(1)}function $o(o,t){for(var e=0,n=o.length;e<n;e++){var i=o[e];if(t(i))return i}}function Do(o,t){for(var e=0,n=o.length;e<n;e++)if(!t(o[e]))return!1;return!0}function It(o,t){if(o&&o.length&&t){if(o.indexOf)return o.indexOf(t)!==-1;if(o.contains)return o.contains(t)}return!1}function No(o,t){return t=t||k.self,o.reduce(function(e,n){return e+t(n)},0)}function Ro(o){for(var t=[],e=o.length,n=-1;++n<e;)t[n]=o[n];return t}function Fo(o){return!o||!o.length}function Bo(o,t){if(!o.length)return[];var e=Et(o);return e.reduce(function(n,i){var r=nt(n);return t(nt(r),i)?r[r.length]=i:n[n.length]=[i],n},[[Tt(o)]])}function Ho(o){for(var t=[],e=0,n=o.length;e<n;e++)o[e]&&t.push(o[e]);return t}function zo(o){for(var t=[],e=0,n=o.length;e<n;e++)It(t,o[e])||t.push(o[e]);return t}function Mo(o,t){if(o&&o.length&&t){var e=o.indexOf(t);return e===-1?null:o[e+1]}return null}function Oo(o,t){if(o&&o.length&&t){var e=o.indexOf(t);return e===-1?null:o[e-1]}return null}const v={head:Tt,last:nt,initial:Lo,tail:Et,prev:Oo,next:Mo,find:$o,contains:It,all:Do,sum:No,from:Ro,isEmpty:Fo,clusterBy:Bo,compact:Ho,unique:zo};var it=" ",jo="\uFEFF";function B(o){return o&&u()(o).hasClass("note-editable")}function Uo(o){return o&&u()(o).hasClass("note-control-sizing")}function _(o){return o=o.toUpperCase(),function(t){return t&&t.nodeName.toUpperCase()===o}}function H(o){return o&&o.nodeType===3}function Ko(o){return o&&o.nodeType===1}function Ke(o){return o&&/^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT|^AUDIO|^VIDEO|^EMBED/.test(o.nodeName.toUpperCase())}function Q(o){return B(o)?!1:o&&/^DIV|^P|^LI|^H[1-7]/.test(o.nodeName.toUpperCase())}function Wo(o){return o&&/^H[1-7]/.test(o.nodeName.toUpperCase())}var Vo=_("PRE"),rt=_("LI");function qo(o){return Q(o)&&!rt(o)}var at=_("TABLE"),_t=_("DATA");function We(o){return!ct(o)&&!st(o)&&!Go(o)&&!Q(o)&&!at(o)&&!lt(o)&&!_t(o)}function st(o){return o&&/^UL|^OL/.test(o.nodeName.toUpperCase())}var Go=_("HR");function At(o){return o&&/^TD|^TH/.test(o.nodeName.toUpperCase())}var lt=_("BLOCKQUOTE");function ct(o){return At(o)||lt(o)||B(o)}var Lt=_("A");function Zo(o){return We(o)&&!!qe(o,Q)}function Yo(o){return We(o)&&!qe(o,Q)}var Xo=_("BODY");function Qo(o,t){return o.nextSibling===t||o.previousSibling===t}function Jo(o,t){t=t||k.ok;var e=[];return o.previousSibling&&t(o.previousSibling)&&e.push(o.previousSibling),e.push(o),o.nextSibling&&t(o.nextSibling)&&e.push(o.nextSibling),e}var Ve=I.isMSIE&&I.browserVersion<11?"&nbsp;":"<br>";function R(o){return H(o)?o.nodeValue.length:o?o.childNodes.length:0}function en(o){do if(o.firstElementChild===null||o.firstElementChild.innerHTML==="")break;while(o=o.firstElementChild);return J(o)}function J(o){var t=R(o);return t===0||!H(o)&&t===1&&o.innerHTML===Ve?!0:!!(v.all(o.childNodes,H)&&o.innerHTML==="")}function $t(o){!Ke(o)&&!R(o)&&(o.innerHTML=Ve)}function qe(o,t){for(;o;){if(t(o))return o;if(B(o))break;o=o.parentNode}return null}function tn(o,t){for(o=o.parentNode;o&&R(o)===1;){if(t(o))return o;if(B(o))break;o=o.parentNode}return null}function W(o,t){t=t||k.fail;var e=[];return qe(o,function(n){return B(n)||e.push(n),t(n)}),e}function on(o,t){var e=W(o);return v.last(e.filter(t))}function nn(o,t){for(var e=W(o),n=t;n;n=n.parentNode)if(e.indexOf(n)>-1)return n;return null}function rn(o,t){t=t||k.fail;for(var e=[];o&&!t(o);)e.push(o),o=o.previousSibling;return e}function Dt(o,t){t=t||k.fail;for(var e=[];o&&!t(o);)e.push(o),o=o.nextSibling;return e}function an(o,t){var e=[];return t=t||k.ok,function n(i){o!==i&&t(i)&&e.push(i);for(var r=0,a=i.childNodes.length;r<a;r++)n(i.childNodes[r])}(o),e}function sn(o,t){var e=o.parentNode,n=u()("<"+t+">")[0];return e.insertBefore(n,o),n.appendChild(o),n}function ut(o,t){var e=t.nextSibling,n=t.parentNode;return e?n.insertBefore(o,e):n.appendChild(o),o}function ft(o,t,e){return u().each(t,function(n,i){!e&&rt(o)&&o.firstChild===null&&st(i)&&o.appendChild(ht("br")),o.appendChild(i)}),o}function Ge(o){return o.offset===0}function ie(o){return o.offset===R(o.node)}function Nt(o){return Ge(o)||ie(o)}function Rt(o,t){for(;o&&o!==t;){if(V(o)!==0)return!1;o=o.parentNode}return!0}function Ft(o,t){if(!t)return!1;for(;o&&o!==t;){if(V(o)!==R(o.parentNode)-1)return!1;o=o.parentNode}return!0}function ln(o,t){return Ge(o)&&Rt(o.node,t)}function cn(o,t){return ie(o)&&Ft(o.node,t)}function V(o){for(var t=0;o=o.previousSibling;)t+=1;return t}function re(o){return!!(o&&o.childNodes&&o.childNodes.length)}function Bt(o,t){var e,n;if(o.offset===0){if(B(o.node))return null;e=o.node.parentNode,n=V(o.node)}else re(o.node)?(e=o.node.childNodes[o.offset-1],n=R(e)):(e=o.node,n=t?0:o.offset-1);return{node:e,offset:n}}function Ht(o,t){var e,n;if(R(o.node)===o.offset){if(B(o.node))return null;var i=Mt(o.node);i?(e=i,n=0):(e=o.node.parentNode,n=V(o.node)+1)}else re(o.node)?(e=o.node.childNodes[o.offset],n=0):(e=o.node,n=t?R(o.node):o.offset+1);return{node:e,offset:n}}function zt(o,t){var e,n=0;if(R(o.node)===o.offset){if(B(o.node))return null;e=o.node.parentNode,n=V(o.node)+1,B(e)&&(e=o.node.nextSibling,n=0)}else re(o.node)?(e=o.node.childNodes[o.offset],n=0):(e=o.node,n=t?R(o.node):o.offset+1);return{node:e,offset:n}}function Mt(o){if(o.nextSibling&&o.parent===o.nextSibling.parent)return H(o.nextSibling)?o.nextSibling:Mt(o.nextSibling)}function Ot(o,t){return o.node===t.node&&o.offset===t.offset}function un(o){if(H(o.node)||!re(o.node)||J(o.node))return!0;var t=o.node.childNodes[o.offset-1],e=o.node.childNodes[o.offset];return!!((!t||Ke(t))&&(!e||Ke(e))||at(e))}function fn(o,t){for(;o;){if(t(o))return o;o=Bt(o)}return null}function dn(o,t){for(;o;){if(t(o))return o;o=Ht(o)}return null}function hn(o){if(!H(o.node))return!1;var t=o.node.nodeValue.charAt(o.offset-1);return t&&t!==" "&&t!==it}function pn(o){if(!H(o.node))return!1;var t=o.node.nodeValue.charAt(o.offset-1);return t===" "||t===it}function vn(o,t,e,n){for(var i=o;i&&i.node&&(e(i),!Ot(i,t));){var r=n&&o.node!==i.node&&t.node!==i.node;i=zt(i,r)}}function mn(o,t){var e=W(t,k.eq(o));return e.map(V).reverse()}function gn(o,t){for(var e=o,n=0,i=t.length;n<i;n++)e.childNodes.length<=t[n]?e=e.childNodes[e.childNodes.length-1]:e=e.childNodes[t[n]];return e}function dt(o,t){var e=t&&t.isSkipPaddingBlankHTML,n=t&&t.isNotSplitEdgePoint,i=t&&t.isDiscardEmptySplits;if(i&&(e=!0),Nt(o)&&(H(o.node)||n)){if(Ge(o))return o.node;if(ie(o))return o.node.nextSibling}if(H(o.node))return o.node.splitText(o.offset);var r=o.node.childNodes[o.offset],a=Dt(r),s=ut(o.node.cloneNode(!1),o.node);return ft(s,a),e||($t(o.node),$t(s)),i&&(J(o.node)&&ae(o.node),J(s))?(ae(s),o.node.nextSibling):s}function jt(o,t,e){var n=W(t.node,k.eq(o));if(n.length){if(n.length===1)return dt(t,e)}else return null;if(n.length>2){var i=n.slice(0,n.length-1),r=i.find(function(c){return c.nextSibling});if(r&&t.offset!=0&&ie(t)){var a=r.nextSibling,s;a.nodeType==1?(s=a.childNodes[0],n=W(s,k.eq(o)),t={node:s,offset:0}):a.nodeType==3&&!a.data.match(/[\n\r]/g)&&(s=a,n=W(s,k.eq(o)),t={node:s,offset:0})}}return n.reduce(function(c,f){return c===t.node&&(c=dt(t,e)),dt({node:f,offset:c?V(c):R(f)},e)})}function bn(o,t){var e=t?Q:ct,n=W(o.node,e),i=v.last(n)||o.node,r,a;e(i)?(r=n[n.length-2],a=i):(r=i,a=r.parentNode);var s=r&&jt(r,o,{isSkipPaddingBlankHTML:t,isNotSplitEdgePoint:t});return!s&&a===o.node&&(s=o.node.childNodes[o.offset]),{rightNode:s,container:a}}function ht(o){return document.createElement(o)}function yn(o){return document.createTextNode(o)}function ae(o,t){if(!(!o||!o.parentNode)){if(o.removeNode)return o.removeNode(t);var e=o.parentNode;if(!t){for(var n=[],i=0,r=o.childNodes.length;i<r;i++)n.push(o.childNodes[i]);for(var a=0,s=n.length;a<s;a++)e.insertBefore(n[a],o)}e.removeChild(o)}}function kn(o,t){for(;o&&!(B(o)||!t(o));){var e=o.parentNode;ae(o),o=e}}function wn(o,t){if(o.nodeName.toUpperCase()===t.toUpperCase())return o;var e=ht(t);return o.style.cssText&&(e.style.cssText=o.style.cssText),ft(e,v.from(o.childNodes)),ut(e,o),ae(o),e}var Ut=_("TEXTAREA");function Kt(o,t){var e=Ut(o[0])?o.val():o.html();return t?e.replace(/[\n\r]/g,""):e}function Cn(o,t){var e=Kt(o);if(t){var n=/<(\/?)(\b(?!!)[^>\s]*)(.*?)(\s*\/?>)/g;e=e.replace(n,function(i,r,a){a=a.toUpperCase();var s=/^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(a)&&!!r,c=/^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(a);return i+(s||c?`
`:"")}),e=e.trim()}return e}function Sn(o){var t=u()(o),e=t.offset(),n=t.outerHeight(!0);return{left:e.left,top:e.top+n}}function Pn(o,t){Object.keys(t).forEach(function(e){o.on(e,t[e])})}function xn(o,t){Object.keys(t).forEach(function(e){o.off(e,t[e])})}function Tn(o){return o&&!H(o)&&v.contains(o.classList,"note-styletag")}const l={NBSP_CHAR:it,ZERO_WIDTH_NBSP_CHAR:jo,blank:Ve,emptyPara:"<p>".concat(Ve,"</p>"),makePredByNodeName:_,isEditable:B,isControlSizing:Uo,isText:H,isElement:Ko,isVoid:Ke,isPara:Q,isPurePara:qo,isHeading:Wo,isInline:We,isBlock:k.not(We),isBodyInline:Yo,isBody:Xo,isParaInline:Zo,isPre:Vo,isList:st,isTable:at,isData:_t,isCell:At,isBlockquote:lt,isBodyContainer:ct,isAnchor:Lt,isDiv:_("DIV"),isLi:rt,isBR:_("BR"),isSpan:_("SPAN"),isB:_("B"),isU:_("U"),isS:_("S"),isI:_("I"),isImg:_("IMG"),isTextarea:Ut,deepestChildIsEmpty:en,isEmpty:J,isEmptyAnchor:k.and(Lt,J),isClosestSibling:Qo,withClosestSiblings:Jo,nodeLength:R,isLeftEdgePoint:Ge,isRightEdgePoint:ie,isEdgePoint:Nt,isLeftEdgeOf:Rt,isRightEdgeOf:Ft,isLeftEdgePointOf:ln,isRightEdgePointOf:cn,prevPoint:Bt,nextPoint:Ht,nextPointWithEmptyNode:zt,isSamePoint:Ot,isVisiblePoint:un,prevPointUntil:fn,nextPointUntil:dn,isCharPoint:hn,isSpacePoint:pn,walkPoint:vn,ancestor:qe,singleChildAncestor:tn,listAncestor:W,lastAncestor:on,listNext:Dt,listPrev:rn,listDescendant:an,commonAncestor:nn,wrap:sn,insertAfter:ut,appendChildNodes:ft,position:V,hasChildren:re,makeOffsetPath:mn,fromOffsetPath:gn,splitTree:jt,splitPoint:bn,create:ht,createText:yn,remove:ae,removeWhile:kn,replace:wn,html:Cn,value:Kt,posFromPlaceholder:Sn,attachEvents:Pn,detachEvents:xn,isCustomStyleTag:Tn};function se(o){"@babel/helpers - typeof";return se=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},se(o)}function En(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function In(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,An(n.key),n)}}function _n(o,t,e){return t&&In(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function An(o){var t=Ln(o,"string");return se(t)=="symbol"?t:t+""}function Ln(o,t){if(se(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(se(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var $n=function(){function o(t,e){En(this,o),this.$note=t,this.memos={},this.modules={},this.layoutInfo={},this.options=u().extend(!0,{},e),u().summernote.ui=u().summernote.ui_template(this.options),this.ui=u().summernote.ui,this.initialize()}return _n(o,[{key:"initialize",value:function(){return this.layoutInfo=this.ui.createLayout(this.$note),this._initialize(),this.$note.hide(),this}},{key:"destroy",value:function(){this._destroy(),this.$note.removeData("summernote"),this.ui.removeLayout(this.$note,this.layoutInfo)}},{key:"reset",value:function(){var e=this.isDisabled();this.code(l.emptyPara),this._destroy(),this._initialize(),e&&this.disable()}},{key:"_initialize",value:function(){var e=this;this.options.id=k.uniqueId(u().now()),this.options.container=this.options.container||this.layoutInfo.editor;var n=u().extend({},this.options.buttons);Object.keys(n).forEach(function(r){e.memo("button."+r,n[r])});var i=u().extend({},this.options.modules,u().summernote.plugins||{});Object.keys(i).forEach(function(r){e.module(r,i[r],!0)}),Object.keys(this.modules).forEach(function(r){e.initializeModule(r)})}},{key:"_destroy",value:function(){var e=this;Object.keys(this.modules).reverse().forEach(function(n){e.removeModule(n)}),Object.keys(this.memos).forEach(function(n){e.removeMemo(n)}),this.triggerEvent("destroy",this)}},{key:"code",value:function(e){var n=this.invoke("codeview.isActivated");if(e===void 0)return this.invoke("codeview.sync"),n?this.layoutInfo.codable.val():this.layoutInfo.editable.html();n?this.invoke("codeview.sync",e):this.layoutInfo.editable.html(e),this.$note.val(e),this.triggerEvent("change",e,this.layoutInfo.editable)}},{key:"isDisabled",value:function(){return this.layoutInfo.editable.attr("contenteditable")==="false"}},{key:"enable",value:function(){this.layoutInfo.editable.attr("contenteditable",!0),this.invoke("toolbar.activate",!0),this.triggerEvent("disable",!1),this.options.editing=!0}},{key:"disable",value:function(){this.invoke("codeview.isActivated")&&this.invoke("codeview.deactivate"),this.layoutInfo.editable.attr("contenteditable",!1),this.options.editing=!1,this.invoke("toolbar.deactivate",!0),this.triggerEvent("disable",!0)}},{key:"triggerEvent",value:function(){var e=v.head(arguments),n=v.tail(v.from(arguments)),i=this.options.callbacks[k.namespaceToCamel(e,"on")];i&&i.apply(this.$note[0],n),this.$note.trigger("summernote."+e,n)}},{key:"initializeModule",value:function(e){var n=this.modules[e];n.shouldInitialize=n.shouldInitialize||k.ok,n.shouldInitialize()&&(n.initialize&&n.initialize(),n.events&&l.attachEvents(this.$note,n.events))}},{key:"module",value:function(e,n,i){if(arguments.length===1)return this.modules[e];this.modules[e]=new n(this),i||this.initializeModule(e)}},{key:"removeModule",value:function(e){var n=this.modules[e];n.shouldInitialize()&&(n.events&&l.detachEvents(this.$note,n.events),n.destroy&&n.destroy()),delete this.modules[e]}},{key:"memo",value:function(e,n){if(arguments.length===1)return this.memos[e];this.memos[e]=n}},{key:"removeMemo",value:function(e){this.memos[e]&&this.memos[e].destroy&&this.memos[e].destroy(),delete this.memos[e]}},{key:"createInvokeHandlerAndUpdateState",value:function(e,n){var i=this;return function(r){i.createInvokeHandler(e,n)(r),i.invoke("buttons.updateCurrentStyle")}}},{key:"createInvokeHandler",value:function(e,n){var i=this;return function(r){r.preventDefault();var a=u()(r.target);i.invoke(e,n||a.closest("[data-value]").data("value"),a)}}},{key:"invoke",value:function(){var e=v.head(arguments),n=v.tail(v.from(arguments)),i=e.split("."),r=i.length>1,a=r&&v.head(i),s=r?v.last(i):v.head(i),c=this.modules[a||"editor"];if(!a&&this[s])return this[s].apply(this,n);if(c&&c[s]&&c.shouldInitialize())return c[s].apply(c,n)}}])}();function pt(o){"@babel/helpers - typeof";return pt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pt(o)}u().fn.extend({summernote:function(){var t=pt(v.head(arguments)),e=t==="string",n=t==="object",i=u().extend({},u().summernote.options,n?v.head(arguments):{});i.langInfo=u().extend(!0,{},u().summernote.lang["en-US"],u().summernote.lang[i.lang]),i.icons=u().extend(!0,{},u().summernote.options.icons,i.icons),i.tooltip=i.tooltip==="auto"?!I.isSupportTouch:i.tooltip,this.each(function(s,c){var f=u()(c);if(!f.data("summernote")){var d=new $n(f,i);f.data("summernote",d),f.data("summernote").triggerEvent("init",d.layoutInfo)}});var r=this.first();if(r.length){var a=r.data("summernote");if(e)return a.invoke.apply(a,v.from(arguments));i.focus&&a.invoke("editor.focus")}return this}});function le(o){"@babel/helpers - typeof";return le=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},le(o)}function Dn(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Nn(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Fn(n.key),n)}}function Rn(o,t,e){return t&&Nn(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Fn(o){var t=Bn(o,"string");return le(t)=="symbol"?t:t+""}function Bn(o,t){if(le(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(le(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}function Wt(o,t){var e=o.parentElement(),n,i=document.body.createTextRange(),r,a=v.from(e.childNodes);for(n=0;n<a.length;n++)if(!l.isText(a[n])){if(i.moveToElementText(a[n]),i.compareEndPoints("StartToStart",o)>=0)break;r=a[n]}if(n!==0&&l.isText(a[n-1])){var s=document.body.createTextRange(),c=null;s.moveToElementText(r||e),s.collapse(!r),c=r?r.nextSibling:e.firstChild;var f=o.duplicate();f.setEndPoint("StartToStart",s);for(var d=f.text.replace(/[\r\n]/g,"").length;d>c.nodeValue.length&&c.nextSibling;)d-=c.nodeValue.length,c=c.nextSibling;c.nodeValue,t&&c.nextSibling&&l.isText(c.nextSibling)&&d===c.nodeValue.length&&(d-=c.nodeValue.length,c=c.nextSibling),e=c,n=d}return{cont:e,offset:n}}function Vt(o){var t=function i(r,a){var s,c;if(l.isText(r)){var f=l.listPrev(r,k.not(l.isText)),d=v.last(f).previousSibling;s=d||r.parentNode,a+=v.sum(v.tail(f),l.nodeLength),c=!d}else{if(s=r.childNodes[a]||r,l.isText(s))return i(s,0);a=0,c=!1}return{node:s,collapseToStart:c,offset:a}},e=document.body.createTextRange(),n=t(o.node,o.offset);return e.moveToElementText(n.node),e.collapse(n.collapseToStart),e.moveStart("character",n.offset),e}var ce=function(){function o(t,e,n,i){Dn(this,o),this.sc=t,this.so=e,this.ec=n,this.eo=i,this.isOnEditable=this.makeIsOn(l.isEditable),this.isOnList=this.makeIsOn(l.isList),this.isOnAnchor=this.makeIsOn(l.isAnchor),this.isOnCell=this.makeIsOn(l.isCell),this.isOnData=this.makeIsOn(l.isData)}return Rn(o,[{key:"nativeRange",value:function(){if(I.isW3CRangeSupport){var e=document.createRange();return e.setStart(this.sc,this.so),e.setEnd(this.ec,this.eo),e}else{var n=Vt({node:this.sc,offset:this.so});return n.setEndPoint("EndToEnd",Vt({node:this.ec,offset:this.eo})),n}}},{key:"getPoints",value:function(){return{sc:this.sc,so:this.so,ec:this.ec,eo:this.eo}}},{key:"getStartPoint",value:function(){return{node:this.sc,offset:this.so}}},{key:"getEndPoint",value:function(){return{node:this.ec,offset:this.eo}}},{key:"select",value:function(){var e=this.nativeRange();if(I.isW3CRangeSupport){var n=document.getSelection();n.rangeCount>0&&n.removeAllRanges(),n.addRange(e)}else e.select();return this}},{key:"scrollIntoView",value:function(e){var n=u()(e).height();return e.scrollTop+n<this.sc.offsetTop&&(e.scrollTop+=Math.abs(e.scrollTop+n-this.sc.offsetTop)),this}},{key:"normalize",value:function(){var e=function(a,s){if(!a||l.isVisiblePoint(a)&&(!l.isEdgePoint(a)||l.isRightEdgePoint(a)&&!s||l.isLeftEdgePoint(a)&&s||l.isRightEdgePoint(a)&&s&&l.isVoid(a.node.nextSibling)||l.isLeftEdgePoint(a)&&!s&&l.isVoid(a.node.previousSibling)||l.isBlock(a.node)&&l.isEmpty(a.node)))return a;var c=l.ancestor(a.node,l.isBlock),f=!1;if(!f){var d=l.prevPoint(a)||{node:null};f=(l.isLeftEdgePointOf(a,c)||l.isVoid(d.node))&&!s}var h=!1;if(!h){var p=l.nextPoint(a)||{node:null};h=(l.isRightEdgePointOf(a,c)||l.isVoid(p.node))&&s}if(f||h){if(l.isVisiblePoint(a))return a;s=!s}var m=s?l.nextPointUntil(l.nextPoint(a),l.isVisiblePoint):l.prevPointUntil(l.prevPoint(a),l.isVisiblePoint);return m||a},n=e(this.getEndPoint(),!1),i=this.isCollapsed()?n:e(this.getStartPoint(),!0);return new o(i.node,i.offset,n.node,n.offset)}},{key:"nodes",value:function(e,n){e=e||k.ok;var i=n&&n.includeAncestor,r=n&&n.fullyContains,a=this.getStartPoint(),s=this.getEndPoint(),c=[],f=[];return l.walkPoint(a,s,function(d){if(!l.isEditable(d.node)){var h;r?(l.isLeftEdgePoint(d)&&f.push(d.node),l.isRightEdgePoint(d)&&v.contains(f,d.node)&&(h=d.node)):i?h=l.ancestor(d.node,e):h=d.node,h&&e(h)&&c.push(h)}},!0),v.unique(c)}},{key:"commonAncestor",value:function(){return l.commonAncestor(this.sc,this.ec)}},{key:"expand",value:function(e){var n=l.ancestor(this.sc,e),i=l.ancestor(this.ec,e);if(!n&&!i)return new o(this.sc,this.so,this.ec,this.eo);var r=this.getPoints();return n&&(r.sc=n,r.so=0),i&&(r.ec=i,r.eo=l.nodeLength(i)),new o(r.sc,r.so,r.ec,r.eo)}},{key:"collapse",value:function(e){return e?new o(this.sc,this.so,this.sc,this.so):new o(this.ec,this.eo,this.ec,this.eo)}},{key:"splitText",value:function(){var e=this.sc===this.ec,n=this.getPoints();return l.isText(this.ec)&&!l.isEdgePoint(this.getEndPoint())&&this.ec.splitText(this.eo),l.isText(this.sc)&&!l.isEdgePoint(this.getStartPoint())&&(n.sc=this.sc.splitText(this.so),n.so=0,e&&(n.ec=n.sc,n.eo=this.eo-this.so)),new o(n.sc,n.so,n.ec,n.eo)}},{key:"deleteContents",value:function(){if(this.isCollapsed())return this;var e=this.splitText(),n=e.nodes(null,{fullyContains:!0}),i=l.prevPointUntil(e.getStartPoint(),function(a){return!v.contains(n,a.node)}),r=[];return u().each(n,function(a,s){var c=s.parentNode;i.node!==c&&l.nodeLength(c)===1&&r.push(c),l.remove(s,!1)}),u().each(r,function(a,s){l.remove(s,!1)}),new o(i.node,i.offset,i.node,i.offset).normalize()}},{key:"makeIsOn",value:function(e){return function(){var n=l.ancestor(this.sc,e);return!!n&&n===l.ancestor(this.ec,e)}}},{key:"isLeftEdgeOf",value:function(e){if(!l.isLeftEdgePoint(this.getStartPoint()))return!1;var n=l.ancestor(this.sc,e);return n&&l.isLeftEdgeOf(this.sc,n)}},{key:"isCollapsed",value:function(){return this.sc===this.ec&&this.so===this.eo}},{key:"wrapBodyInlineWithPara",value:function(){if(l.isBodyContainer(this.sc)&&l.isEmpty(this.sc))return this.sc.innerHTML=l.emptyPara,new o(this.sc.firstChild,0,this.sc.firstChild,0);var e=this.normalize();if(l.isParaInline(this.sc)||l.isPara(this.sc))return e;var n;if(l.isInline(e.sc)){var i=l.listAncestor(e.sc,k.not(l.isInline));n=v.last(i),l.isInline(n)||(n=i[i.length-2]||e.sc.childNodes[e.so])}else n=e.sc.childNodes[e.so>0?e.so-1:0];if(n){var r=l.listPrev(n,l.isParaInline).reverse();if(r=r.concat(l.listNext(n.nextSibling,l.isParaInline)),r.length){var a=l.wrap(v.head(r),"p");l.appendChildNodes(a,v.tail(r))}}return this.normalize()}},{key:"insertNode",value:function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,i=this;(l.isText(e)||l.isInline(e))&&(i=this.wrapBodyInlineWithPara().deleteContents());var r=l.splitPoint(i.getStartPoint(),l.isInline(e));return r.rightNode?(r.rightNode.parentNode.insertBefore(e,r.rightNode),l.isEmpty(r.rightNode)&&(n||l.isPara(e))&&r.rightNode.parentNode.removeChild(r.rightNode)):r.container.appendChild(e),e}},{key:"pasteHTML",value:function(e){e=((e||"")+"").trim(e);var n=u()("<div></div>").html(e)[0],i=v.from(n.childNodes),r=this,a=!1;return r.so>=0&&(i=i.reverse(),a=!0),i=i.map(function(s){return r.insertNode(s,!l.isInline(s))}),a&&(i=i.reverse()),i}},{key:"toString",value:function(){var e=this.nativeRange();return I.isW3CRangeSupport?e.toString():e.text}},{key:"getWordRange",value:function(e){var n=this.getEndPoint();if(!l.isCharPoint(n))return this;var i=l.prevPointUntil(n,function(r){return!l.isCharPoint(r)});return e&&(n=l.nextPointUntil(n,function(r){return!l.isCharPoint(r)})),new o(i.node,i.offset,n.node,n.offset)}},{key:"getWordsRange",value:function(e){var n=this.getEndPoint(),i=function(s){return!l.isCharPoint(s)&&!l.isSpacePoint(s)};if(i(n))return this;var r=l.prevPointUntil(n,i);return e&&(n=l.nextPointUntil(n,i)),new o(r.node,r.offset,n.node,n.offset)}},{key:"getWordsMatchRange",value:function(e){var n=this.getEndPoint(),i=l.prevPointUntil(n,function(c){if(!l.isCharPoint(c)&&!l.isSpacePoint(c))return!0;var f=new o(c.node,c.offset,n.node,n.offset),d=e.exec(f.toString());return d&&d.index===0}),r=new o(i.node,i.offset,n.node,n.offset),a=r.toString(),s=e.exec(a);return s&&s[0].length===a.length?r:null}},{key:"bookmark",value:function(e){return{s:{path:l.makeOffsetPath(e,this.sc),offset:this.so},e:{path:l.makeOffsetPath(e,this.ec),offset:this.eo}}}},{key:"paraBookmark",value:function(e){return{s:{path:v.tail(l.makeOffsetPath(v.head(e),this.sc)),offset:this.so},e:{path:v.tail(l.makeOffsetPath(v.last(e),this.ec)),offset:this.eo}}}},{key:"getClientRects",value:function(){var e=this.nativeRange();return e.getClientRects()}}])}();const S={create:function(t,e,n,i){if(arguments.length===4)return new ce(t,e,n,i);if(arguments.length===2)return n=t,i=e,new ce(t,e,n,i);var r=this.createFromSelection();if(!r&&arguments.length===1){var a=arguments[0];return l.isEditable(a)&&(a=a.lastChild),this.createFromBodyElement(a,l.emptyPara===arguments[0].innerHTML)}return r},createFromBodyElement:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=this.createFromNode(t);return n.collapse(e)},createFromSelection:function(){var t,e,n,i;if(I.isW3CRangeSupport){var r=document.getSelection();if(!r||r.rangeCount===0)return null;if(l.isBody(r.anchorNode))return null;var a=r.getRangeAt(0);t=a.startContainer,e=a.startOffset,n=a.endContainer,i=a.endOffset}else{var s=document.selection.createRange(),c=s.duplicate();c.collapse(!1);var f=s;f.collapse(!0);var d=Wt(f,!0),h=Wt(c,!1);l.isText(d.node)&&l.isLeftEdgePoint(d)&&l.isTextNode(h.node)&&l.isRightEdgePoint(h)&&h.node.nextSibling===d.node&&(d=h),t=d.cont,e=d.offset,n=h.cont,i=h.offset}return new ce(t,e,n,i)},createFromNode:function(t){var e=t,n=0,i=t,r=l.nodeLength(i);return l.isVoid(e)&&(n=l.listPrev(e).length-1,e=e.parentNode),l.isBR(i)?(r=l.listPrev(i).length-1,i=i.parentNode):l.isVoid(i)&&(r=l.listPrev(i).length,i=i.parentNode),this.create(e,n,i,r)},createFromNodeBefore:function(t){return this.createFromNode(t).collapse(!0)},createFromNodeAfter:function(t){return this.createFromNode(t).collapse()},createFromBookmark:function(t,e){var n=l.fromOffsetPath(t,e.s.path),i=e.s.offset,r=l.fromOffsetPath(t,e.e.path),a=e.e.offset;return new ce(n,i,r,a)},createFromParaBookmark:function(t,e){var n=t.s.offset,i=t.e.offset,r=l.fromOffsetPath(v.head(e),t.s.path),a=l.fromOffsetPath(v.last(e),t.e.path);return new ce(r,n,a,i)}};var L={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,SPACE:32,DELETE:46,LEFT:37,UP:38,RIGHT:39,DOWN:40,NUM0:48,NUM1:49,NUM2:50,NUM3:51,NUM4:52,NUM5:53,NUM6:54,NUM7:55,NUM8:56,B:66,E:69,I:73,J:74,K:75,L:76,R:82,S:83,U:85,V:86,Y:89,Z:90,SLASH:191,LEFTBRACKET:219,BACKSLASH:220,RIGHTBRACKET:221,HOME:36,END:35,PAGEUP:33,PAGEDOWN:34};const P={isEdit:function(t){return v.contains([L.BACKSPACE,L.TAB,L.ENTER,L.SPACE,L.DELETE],t)},isRemove:function(t){return v.contains([L.BACKSPACE,L.DELETE],t)},isMove:function(t){return v.contains([L.LEFT,L.UP,L.RIGHT,L.DOWN],t)},isNavigation:function(t){return v.contains([L.HOME,L.END,L.PAGEUP,L.PAGEDOWN],t)},nameFromCode:k.invertObject(L),code:L};function Hn(o){return u().Deferred(function(t){u().extend(new FileReader,{onload:function(n){var i=n.target.result;t.resolve(i)},onerror:function(n){t.reject(n)}}).readAsDataURL(o)}).promise()}function zn(o){return u().Deferred(function(t){var e=u()("<img>");e.one("load",function(){e.off("error abort"),t.resolve(e)}).one("error abort",function(){e.off("load").detach(),t.reject(e)}).css({display:"none"}).appendTo(document.body).attr("src",o)}).promise()}function ue(o){"@babel/helpers - typeof";return ue=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ue(o)}function Mn(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function On(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Un(n.key),n)}}function jn(o,t,e){return t&&On(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Un(o){var t=Kn(o,"string");return ue(t)=="symbol"?t:t+""}function Kn(o,t){if(ue(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(ue(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Wn=function(){function o(t){Mn(this,o),this.stack=[],this.stackOffset=-1,this.context=t,this.$editable=t.layoutInfo.editable,this.editable=this.$editable[0]}return jn(o,[{key:"makeSnapshot",value:function(){var e=S.create(this.editable),n={s:{path:[],offset:0},e:{path:[],offset:0}};return{contents:this.$editable.html(),bookmark:e&&e.isOnEditable()?e.bookmark(this.editable):n}}},{key:"applySnapshot",value:function(e){e.contents!==null&&this.$editable.html(e.contents),e.bookmark!==null&&S.createFromBookmark(this.editable,e.bookmark).select()}},{key:"rewind",value:function(){this.$editable.html()!==this.stack[this.stackOffset].contents&&this.recordUndo(),this.stackOffset=0,this.applySnapshot(this.stack[this.stackOffset])}},{key:"commit",value:function(){this.stack=[],this.stackOffset=-1,this.recordUndo()}},{key:"reset",value:function(){this.stack=[],this.stackOffset=-1,this.$editable.html(""),this.recordUndo()}},{key:"undo",value:function(){this.$editable.html()!==this.stack[this.stackOffset].contents&&this.recordUndo(),this.stackOffset>0&&(this.stackOffset--,this.applySnapshot(this.stack[this.stackOffset]))}},{key:"redo",value:function(){this.stack.length-1>this.stackOffset&&(this.stackOffset++,this.applySnapshot(this.stack[this.stackOffset]))}},{key:"recordUndo",value:function(){this.stackOffset++,this.stack.length>this.stackOffset&&(this.stack=this.stack.slice(0,this.stackOffset)),this.stack.push(this.makeSnapshot()),this.stack.length>this.context.options.historyLimit&&(this.stack.shift(),this.stackOffset-=1)}}])}();function fe(o){"@babel/helpers - typeof";return fe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fe(o)}function Vn(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function qn(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Zn(n.key),n)}}function Gn(o,t,e){return t&&qn(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Zn(o){var t=Yn(o,"string");return fe(t)=="symbol"?t:t+""}function Yn(o,t){if(fe(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(fe(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Xn=function(){function o(){Vn(this,o)}return Gn(o,[{key:"jQueryCSS",value:function(e,n){var i={};return u().each(n,function(r,a){i[a]=e.css(a)}),i}},{key:"fromNode",value:function(e){var n=["font-family","font-size","text-align","list-style-type","line-height"],i=this.jQueryCSS(e,n)||{},r=e[0].style.fontSize||i["font-size"];return i["font-size"]=parseInt(r,10),i["font-size-unit"]=r.match(/[a-z%]+$/),i}},{key:"stylePara",value:function(e,n){u().each(e.nodes(l.isPara,{includeAncestor:!0}),function(i,r){u()(r).css(n)})}},{key:"styleNodes",value:function(e,n){e=e.splitText();var i=n&&n.nodeName||"SPAN",r=!!(n&&n.expandClosestSibling),a=!!(n&&n.onlyPartialContains);if(e.isCollapsed())return[e.insertNode(l.create(i))];var s=l.makePredByNodeName(i),c=e.nodes(l.isText,{fullyContains:!0}).map(function(d){return l.singleChildAncestor(d,s)||l.wrap(d,i)});if(r){if(a){var f=e.nodes();s=k.and(s,function(d){return v.contains(f,d)})}return c.map(function(d){var h=l.withClosestSiblings(d,s),p=v.head(h),m=v.tail(h);return u().each(m,function(y,w){l.appendChildNodes(p,w.childNodes),l.remove(w)}),v.head(h)})}else return c}},{key:"current",value:function(e){var n=u()(l.isElement(e.sc)?e.sc:e.sc.parentNode),i=this.fromNode(n);try{i=u().extend(i,{"font-bold":document.queryCommandState("bold")?"bold":"normal","font-italic":document.queryCommandState("italic")?"italic":"normal","font-underline":document.queryCommandState("underline")?"underline":"normal","font-subscript":document.queryCommandState("subscript")?"subscript":"normal","font-superscript":document.queryCommandState("superscript")?"superscript":"normal","font-strikethrough":document.queryCommandState("strikethrough")?"strikethrough":"normal","font-family":document.queryCommandValue("fontname")||i["font-family"]})}catch{}if(!e.isOnList())i["list-style"]="none";else{var r=["circle","disc","disc-leading-zero","square"],a=r.indexOf(i["list-style-type"])>-1;i["list-style"]=a?"unordered":"ordered"}var s=l.ancestor(e.sc,l.isPara);if(s&&s.style["line-height"])i["line-height"]=s.style.lineHeight;else{var c=parseInt(i["line-height"],10)/parseInt(i["font-size"],10);i["line-height"]=c.toFixed(1)}return i.anchor=e.isOnAnchor()&&l.ancestor(e.sc,l.isAnchor),i.ancestors=l.listAncestor(e.sc,l.isEditable),i.range=e,i}}])}();function de(o){"@babel/helpers - typeof";return de=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},de(o)}function Qn(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Jn(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,ti(n.key),n)}}function ei(o,t,e){return t&&Jn(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function ti(o){var t=oi(o,"string");return de(t)=="symbol"?t:t+""}function oi(o,t){if(de(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(de(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var qt=function(){function o(){Qn(this,o)}return ei(o,[{key:"insertOrderedList",value:function(e){this.toggleList("OL",e)}},{key:"insertUnorderedList",value:function(e){this.toggleList("UL",e)}},{key:"indent",value:function(e){var n=this,i=S.create(e).wrapBodyInlineWithPara(),r=i.nodes(l.isPara,{includeAncestor:!0}),a=v.clusterBy(r,k.peq2("parentNode"));u().each(a,function(s,c){var f=v.head(c);if(l.isLi(f)){var d=n.findList(f.previousSibling);d?c.map(function(h){return d.appendChild(h)}):(n.wrapList(c,f.parentNode.nodeName),c.map(function(h){return h.parentNode}).map(function(h){return n.appendToPrevious(h)}))}else u().each(c,function(h,p){u()(p).css("marginLeft",function(m,y){return(parseInt(y,10)||0)+25})})}),i.select()}},{key:"outdent",value:function(e){var n=this,i=S.create(e).wrapBodyInlineWithPara(),r=i.nodes(l.isPara,{includeAncestor:!0}),a=v.clusterBy(r,k.peq2("parentNode"));u().each(a,function(s,c){var f=v.head(c);l.isLi(f)?n.releaseList([c]):u().each(c,function(d,h){u()(h).css("marginLeft",function(p,m){return m=parseInt(m,10)||0,m>25?m-25:""})})}),i.select()}},{key:"toggleList",value:function(e,n){var i=this,r=S.create(n).wrapBodyInlineWithPara(),a=r.nodes(l.isPara,{includeAncestor:!0}),s=r.paraBookmark(a),c=v.clusterBy(a,k.peq2("parentNode"));if(v.find(a,l.isPurePara)){var f=[];u().each(c,function(h,p){f=f.concat(i.wrapList(p,e))}),a=f}else{var d=r.nodes(l.isList,{includeAncestor:!0}).filter(function(h){return!u().nodeName(h,e)});d.length?u().each(d,function(h,p){l.replace(p,e)}):a=this.releaseList(c,!0)}S.createFromParaBookmark(s,a).select()}},{key:"wrapList",value:function(e,n){var i=v.head(e),r=v.last(e),a=l.isList(i.previousSibling)&&i.previousSibling,s=l.isList(r.nextSibling)&&r.nextSibling,c=a||l.insertAfter(l.create(n||"UL"),r);return e=e.map(function(f){return l.isPurePara(f)?l.replace(f,"LI"):f}),l.appendChildNodes(c,e,!0),s&&(l.appendChildNodes(c,v.from(s.childNodes),!0),l.remove(s)),e}},{key:"releaseList",value:function(e,n){var i=this,r=[];return u().each(e,function(a,s){var c=v.head(s),f=v.last(s),d=n?l.lastAncestor(c,l.isList):c.parentNode,h=d.parentNode;if(d.parentNode.nodeName==="LI")s.map(function(w){var T=i.findNextSiblings(w);h.nextSibling?h.parentNode.insertBefore(w,h.nextSibling):h.parentNode.appendChild(w),T.length&&(i.wrapList(T,d.nodeName),w.appendChild(T[0].parentNode))}),d.children.length===0&&h.removeChild(d),h.childNodes.length===0&&h.parentNode.removeChild(h);else{var p=d.childNodes.length>1?l.splitTree(d,{node:f.parentNode,offset:l.position(f)+1},{isSkipPaddingBlankHTML:!0}):null,m=l.splitTree(d,{node:c.parentNode,offset:l.position(c)},{isSkipPaddingBlankHTML:!0});s=n?l.listDescendant(m,l.isLi):v.from(m.childNodes).filter(l.isLi),(n||!l.isList(d.parentNode))&&(s=s.map(function(w){return l.replace(w,"P")})),u().each(v.from(s).reverse(),function(w,T){l.insertAfter(T,d)});var y=v.compact([d,m,p]);u().each(y,function(w,T){var A=[T].concat(l.listDescendant(T,l.isList));u().each(A.reverse(),function(b,g){l.nodeLength(g)||l.remove(g,!0)})})}r=r.concat(s)}),r}},{key:"appendToPrevious",value:function(e){return e.previousSibling?l.appendChildNodes(e.previousSibling,[e]):this.wrapList([e],"LI")}},{key:"findList",value:function(e){return e?v.find(e.children,function(n){return["OL","UL"].indexOf(n.nodeName)>-1}):null}},{key:"findNextSiblings",value:function(e){for(var n=[];e.nextSibling;)n.push(e.nextSibling),e=e.nextSibling;return n}}])}();function he(o){"@babel/helpers - typeof";return he=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},he(o)}function ni(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function ii(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,ai(n.key),n)}}function ri(o,t,e){return t&&ii(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function ai(o){var t=si(o,"string");return he(t)=="symbol"?t:t+""}function si(o,t){if(he(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(he(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var li=function(){function o(t){ni(this,o),this.bullet=new qt,this.options=t.options}return ri(o,[{key:"insertTab",value:function(e,n){var i=l.createText(new Array(n+1).join(l.NBSP_CHAR));e=e.deleteContents(),e.insertNode(i,!0),e=S.create(i,n),e.select()}},{key:"insertParagraph",value:function(e,n){n=n||S.create(e),n=n.deleteContents(),n=n.wrapBodyInlineWithPara();var i=l.ancestor(n.sc,l.isPara),r;if(i)if(l.isLi(i)&&(l.isEmpty(i)||l.deepestChildIsEmpty(i))){this.bullet.toggleList(i.parentNode.nodeName);return}else{var a=null;if(this.options.blockquoteBreakingLevel===1?a=l.ancestor(i,l.isBlockquote):this.options.blockquoteBreakingLevel===2&&(a=l.lastAncestor(i,l.isBlockquote)),a){r=u()(l.emptyPara)[0],l.isRightEdgePoint(n.getStartPoint())&&l.isBR(n.sc.nextSibling)&&u()(n.sc.nextSibling).remove();var s=l.splitTree(a,n.getStartPoint(),{isDiscardEmptySplits:!0});s?s.parentNode.insertBefore(r,s):l.insertAfter(r,a)}else{r=l.splitTree(i,n.getStartPoint());var c=l.listDescendant(i,l.isEmptyAnchor);c=c.concat(l.listDescendant(r,l.isEmptyAnchor)),u().each(c,function(d,h){l.remove(h)}),(l.isHeading(r)||l.isPre(r)||l.isCustomStyleTag(r))&&l.isEmpty(r)&&(r=l.replace(r,"p"))}}else{var f=n.sc.childNodes[n.so];r=u()(l.emptyPara)[0],f?n.sc.insertBefore(r,f):n.sc.appendChild(r)}S.create(r,0).normalize().select().scrollIntoView(e)}}])}();function pe(o){"@babel/helpers - typeof";return pe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pe(o)}function ci(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function ui(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,di(n.key),n)}}function fi(o,t,e){return t&&ui(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function di(o){var t=hi(o,"string");return pe(t)=="symbol"?t:t+""}function hi(o,t){if(pe(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(pe(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var x=function o(t,e,n,i){var r={colPos:0,rowPos:0},a=[],s=[];function c(){!t||!t.tagName||t.tagName.toLowerCase()!=="td"&&t.tagName.toLowerCase()!=="th"||(r.colPos=t.cellIndex,!(!t.parentElement||!t.parentElement.tagName||t.parentElement.tagName.toLowerCase()!=="tr")&&(r.rowPos=t.parentElement.rowIndex))}function f(b,g,C,E,j,z,U){var F={baseRow:C,baseCell:E,isRowSpan:j,isColSpan:z,isVirtual:U};a[b]||(a[b]=[]),a[b][g]=F}function d(b,g,C,E){return{baseCell:b.baseCell,action:g,virtualTable:{rowIndex:C,cellIndex:E}}}function h(b,g){if(!a[b]||!a[b][g])return g;for(var C=g;a[b][C];)if(C++,!a[b][C])return C}function p(b,g){var C=h(b.rowIndex,g.cellIndex),E=g.colSpan>1,j=g.rowSpan>1,z=b.rowIndex===r.rowPos&&g.cellIndex===r.colPos;f(b.rowIndex,C,b,g,j,E,!1);var U=g.attributes.rowSpan?parseInt(g.attributes.rowSpan.value,10):0;if(U>1)for(var F=1;F<U;F++){var Z=b.rowIndex+F;m(Z,C,g,z),f(Z,C,b,g,!0,E,!0)}var Qe=g.attributes.colSpan?parseInt(g.attributes.colSpan.value,10):0;if(Qe>1)for(var Oe=1;Oe<Qe;Oe++){var Je=h(b.rowIndex,C+Oe);m(b.rowIndex,Je,g,z),f(b.rowIndex,Je,b,g,j,!0,!0)}}function m(b,g,C,E){b===r.rowPos&&r.colPos>=C.cellIndex&&C.cellIndex<=g&&!E&&r.colPos++}function y(){for(var b=i.rows,g=0;g<b.length;g++)for(var C=b[g].cells,E=0;E<C.length;E++)p(b[g],C[E])}function w(b){switch(e){case o.where.Column:if(b.isColSpan)return o.resultAction.SubtractSpanCount;break;case o.where.Row:if(!b.isVirtual&&b.isRowSpan)return o.resultAction.AddCell;if(b.isRowSpan)return o.resultAction.SubtractSpanCount;break}return o.resultAction.RemoveCell}function T(b){switch(e){case o.where.Column:if(b.isColSpan)return o.resultAction.SumSpanCount;if(b.isRowSpan&&b.isVirtual)return o.resultAction.Ignore;break;case o.where.Row:if(b.isRowSpan)return o.resultAction.SumSpanCount;if(b.isColSpan&&b.isVirtual)return o.resultAction.Ignore;break}return o.resultAction.AddCell}function A(){c(),y()}this.getActionList=function(){for(var b=e===o.where.Row?r.rowPos:-1,g=e===o.where.Column?r.colPos:-1,C=0,E=!0;E;){var j=b>=0?b:C,z=g>=0?g:C,U=a[j];if(!U)return E=!1,s;var F=U[z];if(!F)return E=!1,s;var Z=o.resultAction.Ignore;switch(n){case o.requestAction.Add:Z=T(F);break;case o.requestAction.Delete:Z=w(F);break}s.push(d(F,Z,j,z)),C++}return s},A()};x.where={Row:0,Column:1},x.requestAction={Add:0,Delete:1},x.resultAction={Ignore:0,SubtractSpanCount:1,RemoveCell:2,AddCell:3,SumSpanCount:4};var pi=function(){function o(){ci(this,o)}return fi(o,[{key:"tab",value:function(e,n){var i=l.ancestor(e.commonAncestor(),l.isCell),r=l.ancestor(i,l.isTable),a=l.listDescendant(r,l.isCell),s=v[n?"prev":"next"](a,i);s&&S.create(s,0).select()}},{key:"addRow",value:function(e,n){for(var i=l.ancestor(e.commonAncestor(),l.isCell),r=u()(i).closest("tr"),a=this.recoverAttributes(r),s=u()("<tr"+a+"></tr>"),c=new x(i,x.where.Row,x.requestAction.Add,u()(r).closest("table")[0]),f=c.getActionList(),d=0;d<f.length;d++){var h=f[d],p=this.recoverAttributes(h.baseCell);switch(h.action){case x.resultAction.AddCell:s.append("<td"+p+">"+l.blank+"</td>");break;case x.resultAction.SumSpanCount:{if(n==="top"){var m=h.baseCell.parent,y=(m?h.baseCell.closest("tr").rowIndex:0)<=r[0].rowIndex;if(y){var w=u()("<div></div>").append(u()("<td"+p+">"+l.blank+"</td>").removeAttr("rowspan")).html();s.append(w);break}}var T=parseInt(h.baseCell.rowSpan,10);T++,h.baseCell.setAttribute("rowSpan",T)}break}}if(n==="top")r.before(s);else{var A=i.rowSpan>1;if(A){var b=r[0].rowIndex+(i.rowSpan-2);u()(u()(r).parent().find("tr")[b]).after(u()(s));return}r.after(s)}}},{key:"addCol",value:function(e,n){var i=l.ancestor(e.commonAncestor(),l.isCell),r=u()(i).closest("tr"),a=u()(r).siblings();a.push(r);for(var s=new x(i,x.where.Column,x.requestAction.Add,u()(r).closest("table")[0]),c=s.getActionList(),f=0;f<c.length;f++){var d=c[f],h=this.recoverAttributes(d.baseCell);switch(d.action){case x.resultAction.AddCell:n==="right"?u()(d.baseCell).after("<td"+h+">"+l.blank+"</td>"):u()(d.baseCell).before("<td"+h+">"+l.blank+"</td>");break;case x.resultAction.SumSpanCount:if(n==="right"){var p=parseInt(d.baseCell.colSpan,10);p++,d.baseCell.setAttribute("colSpan",p)}else u()(d.baseCell).before("<td"+h+">"+l.blank+"</td>");break}}}},{key:"recoverAttributes",value:function(e){var n="";if(!e)return n;for(var i=e.attributes||[],r=0;r<i.length;r++)i[r].name.toLowerCase()!=="id"&&i[r].specified&&(n+=" "+i[r].name+"='"+i[r].value+"'");return n}},{key:"deleteRow",value:function(e){for(var n=l.ancestor(e.commonAncestor(),l.isCell),i=u()(n).closest("tr"),r=i.children("td, th").index(u()(n)),a=i[0].rowIndex,s=new x(n,x.where.Row,x.requestAction.Delete,u()(i).closest("table")[0]),c=s.getActionList(),f=0;f<c.length;f++)if(c[f]){var d=c[f].baseCell,h=c[f].virtualTable,p=d.rowSpan&&d.rowSpan>1,m=p?parseInt(d.rowSpan,10):0;switch(c[f].action){case x.resultAction.Ignore:continue;case x.resultAction.AddCell:{var y=i.next("tr")[0];if(!y)continue;var w=i[0].cells[r];p&&(m>2?(m--,y.insertBefore(w,y.cells[r]),y.cells[r].setAttribute("rowSpan",m),y.cells[r].innerHTML=""):m===2&&(y.insertBefore(w,y.cells[r]),y.cells[r].removeAttribute("rowSpan"),y.cells[r].innerHTML=""))}continue;case x.resultAction.SubtractSpanCount:p&&(m>2?(m--,d.setAttribute("rowSpan",m),h.rowIndex!==a&&d.cellIndex===r&&(d.innerHTML="")):m===2&&(d.removeAttribute("rowSpan"),h.rowIndex!==a&&d.cellIndex===r&&(d.innerHTML="")));continue;case x.resultAction.RemoveCell:continue}}i.remove()}},{key:"deleteCol",value:function(e){for(var n=l.ancestor(e.commonAncestor(),l.isCell),i=u()(n).closest("tr"),r=i.children("td, th").index(u()(n)),a=new x(n,x.where.Column,x.requestAction.Delete,u()(i).closest("table")[0]),s=a.getActionList(),c=0;c<s.length;c++)if(s[c])switch(s[c].action){case x.resultAction.Ignore:continue;case x.resultAction.SubtractSpanCount:{var f=s[c].baseCell,d=f.colSpan&&f.colSpan>1;if(d){var h=f.colSpan?parseInt(f.colSpan,10):0;h>2?(h--,f.setAttribute("colSpan",h),f.cellIndex===r&&(f.innerHTML="")):h===2&&(f.removeAttribute("colSpan"),f.cellIndex===r&&(f.innerHTML=""))}}continue;case x.resultAction.RemoveCell:l.remove(s[c].baseCell,!0);continue}}},{key:"createTable",value:function(e,n,i){for(var r=[],a,s=0;s<e;s++)r.push("<td>"+l.blank+"</td>");a=r.join("");for(var c=[],f,d=0;d<n;d++)c.push("<tr>"+a+"</tr>");f=c.join("");var h=u()("<table>"+f+"</table>");return i&&i.tableClassName&&h.addClass(i.tableClassName),h[0]}},{key:"deleteTable",value:function(e){var n=l.ancestor(e.commonAncestor(),l.isCell);u()(n).closest("table").remove()}}])}();function ve(o){"@babel/helpers - typeof";return ve=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ve(o)}function vi(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function mi(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,bi(n.key),n)}}function gi(o,t,e){return t&&mi(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function bi(o){var t=yi(o,"string");return ve(t)=="symbol"?t:t+""}function yi(o,t){if(ve(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(ve(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var ki="bogus",wi=/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,Ci=/^(\+?\d{1,3}[\s-]?)?(\d{1,4})[\s-]?(\d{1,4})[\s-]?(\d{1,4})$/,Si=/^([A-Za-z][A-Za-z0-9+-.]*\:|#|\/)/,Pi=function(){function o(t){var e=this;vi(this,o),this.context=t,this.$note=t.layoutInfo.note,this.$editor=t.layoutInfo.editor,this.$editable=t.layoutInfo.editable,this.options=t.options,this.lang=this.options.langInfo,this.editable=this.$editable[0],this.lastRange=null,this.snapshot=null,this.style=new Xn,this.table=new pi,this.typing=new li(t),this.bullet=new qt,this.history=new Wn(t),this.context.memo("help.escape",this.lang.help.escape),this.context.memo("help.undo",this.lang.help.undo),this.context.memo("help.redo",this.lang.help.redo),this.context.memo("help.tab",this.lang.help.tab),this.context.memo("help.untab",this.lang.help.untab),this.context.memo("help.insertParagraph",this.lang.help.insertParagraph),this.context.memo("help.insertOrderedList",this.lang.help.insertOrderedList),this.context.memo("help.insertUnorderedList",this.lang.help.insertUnorderedList),this.context.memo("help.indent",this.lang.help.indent),this.context.memo("help.outdent",this.lang.help.outdent),this.context.memo("help.formatPara",this.lang.help.formatPara),this.context.memo("help.insertHorizontalRule",this.lang.help.insertHorizontalRule),this.context.memo("help.fontName",this.lang.help.fontName);for(var n=["bold","italic","underline","strikethrough","superscript","subscript","justifyLeft","justifyCenter","justifyRight","justifyFull","formatBlock","removeFormat","backColor"],i=0,r=n.length;i<r;i++)this[n[i]]=function(s){return function(c){e.beforeCommand(),document.execCommand(s,!1,c),e.afterCommand(!0)}}(n[i]),this.context.memo("help."+n[i],this.lang.help[n[i]]);this.fontName=this.wrapCommand(function(s){return e.fontStyling("font-family",I.validFontName(s))}),this.fontSize=this.wrapCommand(function(s){var c=e.currentStyle()["font-size-unit"];return e.fontStyling("font-size",s+c)}),this.fontSizeUnit=this.wrapCommand(function(s){var c=e.currentStyle()["font-size"];return e.fontStyling("font-size",c+s)});for(var a=1;a<=6;a++)this["formatH"+a]=function(s){return function(){e.formatBlock("H"+s)}}(a),this.context.memo("help.formatH"+a,this.lang.help["formatH"+a]);this.insertParagraph=this.wrapCommand(function(){e.typing.insertParagraph(e.editable)}),this.insertOrderedList=this.wrapCommand(function(){e.bullet.insertOrderedList(e.editable)}),this.insertUnorderedList=this.wrapCommand(function(){e.bullet.insertUnorderedList(e.editable)}),this.indent=this.wrapCommand(function(){e.bullet.indent(e.editable)}),this.outdent=this.wrapCommand(function(){e.bullet.outdent(e.editable)}),this.insertNode=this.wrapCommand(function(s){if(!e.isLimited(u()(s).text().length)){var c=e.getLastRange();c.insertNode(s),e.setLastRange(S.createFromNodeAfter(s).select())}}),this.insertText=this.wrapCommand(function(s){if(!e.isLimited(s.length)){var c=e.getLastRange(),f=c.insertNode(l.createText(s));e.setLastRange(S.create(f,l.nodeLength(f)).select())}}),this.pasteHTML=this.wrapCommand(function(s){if(!e.isLimited(s.length)){s=e.context.invoke("codeview.purify",s);var c=e.getLastRange().pasteHTML(s);e.setLastRange(S.createFromNodeAfter(v.last(c)).select())}}),this.formatBlock=this.wrapCommand(function(s,c){var f=e.options.callbacks.onApplyCustomStyle;f?f.call(e,c,e.context,e.onFormatBlock):e.onFormatBlock(s,c)}),this.insertHorizontalRule=this.wrapCommand(function(){var s=e.getLastRange().insertNode(l.create("HR"));s.nextSibling&&e.setLastRange(S.create(s.nextSibling,0).normalize().select())}),this.lineHeight=this.wrapCommand(function(s){e.style.stylePara(e.getLastRange(),{lineHeight:s})}),this.createLink=this.wrapCommand(function(s){var c=[],f=s.url,d=s.text,h=s.isNewWindow,p=e.options.linkAddNoReferrer,m=e.options.linkAddNoOpener,y=s.range||e.getLastRange(),w=d.length-y.toString().length;if(!(w>0&&e.isLimited(w))){var T=y.toString()!==d;typeof f=="string"&&(f=f.trim()),e.options.onCreateLink?f=e.options.onCreateLink(f):f=e.checkLinkUrl(f);var A=[];if(T){y=y.deleteContents();var b=y.insertNode(u()("<A></A>").text(d)[0]);A.push(b)}else A=e.style.styleNodes(y,{nodeName:"A",expandClosestSibling:!0,onlyPartialContains:!0});u().each(A,function(g,C){u()(C).attr("href",f),h?(u()(C).attr("target","_blank"),p&&c.push("noreferrer"),m&&c.push("noopener"),c.length&&u()(C).attr("rel",c.join(" "))):u()(C).removeAttr("target")}),e.setLastRange(e.createRangeFromList(A).select())}}),this.color=this.wrapCommand(function(s){var c=s.foreColor,f=s.backColor;c&&document.execCommand("foreColor",!1,c),f&&document.execCommand("backColor",!1,f)}),this.foreColor=this.wrapCommand(function(s){document.execCommand("foreColor",!1,s)}),this.insertTable=this.wrapCommand(function(s){var c=s.split("x"),f=e.getLastRange().deleteContents();f.insertNode(e.table.createTable(c[0],c[1],e.options))}),this.removeMedia=this.wrapCommand(function(){var s=u()(e.restoreTarget()).parent();s.closest("figure").length?s.closest("figure").remove():s=u()(e.restoreTarget()).detach(),e.setLastRange(S.createFromSelection(s).select()),e.context.triggerEvent("media.delete",s,e.$editable)}),this.floatMe=this.wrapCommand(function(s){var c=u()(e.restoreTarget());c.toggleClass("note-float-left",s==="left"),c.toggleClass("note-float-right",s==="right"),c.css("float",s==="none"?"":s)}),this.resize=this.wrapCommand(function(s){var c=u()(e.restoreTarget());s=parseFloat(s),s===0?c.css("width",""):c.css({width:s*100+"%",height:""})})}return gi(o,[{key:"initialize",value:function(){var e=this;this.$editable.on("keydown",function(n){if(n.keyCode===P.code.ENTER&&e.context.triggerEvent("enter",n),e.context.triggerEvent("keydown",n),e.snapshot=e.history.makeSnapshot(),e.hasKeyShortCut=!1,n.isDefaultPrevented()||(e.options.shortcuts?e.hasKeyShortCut=e.handleKeyMap(n):e.preventDefaultEditableShortCuts(n)),e.isLimited(1,n)){var i=e.getLastRange();if(i.eo-i.so===0)return!1}e.setLastRange(),e.options.recordEveryKeystroke&&e.hasKeyShortCut===!1&&e.history.recordUndo()}).on("keyup",function(n){e.setLastRange(),e.context.triggerEvent("keyup",n)}).on("focus",function(n){e.setLastRange(),e.context.triggerEvent("focus",n)}).on("blur",function(n){e.context.triggerEvent("blur",n)}).on("mousedown",function(n){e.context.triggerEvent("mousedown",n)}).on("mouseup",function(n){e.setLastRange(),e.history.recordUndo(),e.context.triggerEvent("mouseup",n)}).on("scroll",function(n){e.context.triggerEvent("scroll",n)}).on("paste",function(n){e.setLastRange(),e.context.triggerEvent("paste",n)}).on("copy",function(n){e.context.triggerEvent("copy",n)}).on("input",function(){e.isLimited(0)&&e.snapshot&&e.history.applySnapshot(e.snapshot)}),this.$editable.attr("spellcheck",this.options.spellCheck),this.$editable.attr("autocorrect",this.options.spellCheck),this.options.disableGrammar&&this.$editable.attr("data-gramm",!1),this.$editable.html(l.html(this.$note)||l.emptyPara),this.$editable.on(I.inputEventName,k.debounce(function(){e.context.triggerEvent("change",e.$editable.html(),e.$editable)},10)),this.$editable.on("focusin",function(n){e.context.triggerEvent("focusin",n)}).on("focusout",function(n){e.context.triggerEvent("focusout",n)}),this.options.airMode?this.options.overrideContextMenu&&this.$editor.on("contextmenu",function(n){return e.context.triggerEvent("contextmenu",n),!1}):(this.options.width&&this.$editor.outerWidth(this.options.width),this.options.height&&this.$editable.outerHeight(this.options.height),this.options.maxHeight&&this.$editable.css("max-height",this.options.maxHeight),this.options.minHeight&&this.$editable.css("min-height",this.options.minHeight)),this.history.recordUndo(),this.setLastRange()}},{key:"destroy",value:function(){this.$editable.off()}},{key:"handleKeyMap",value:function(e){var n=this.options.keyMap[I.isMac?"mac":"pc"],i=[];e.metaKey&&i.push("CMD"),e.ctrlKey&&!e.altKey&&i.push("CTRL"),e.shiftKey&&i.push("SHIFT");var r=P.nameFromCode[e.keyCode];r&&i.push(r);var a=n[i.join("+")];if(r==="TAB"&&!this.options.tabDisable)this.afterCommand();else if(a){if(this.context.invoke(a)!==!1)return e.preventDefault(),!0}else P.isEdit(e.keyCode)&&(P.isRemove(e.keyCode)&&this.context.invoke("removed"),this.afterCommand());return!1}},{key:"preventDefaultEditableShortCuts",value:function(e){(e.ctrlKey||e.metaKey)&&v.contains([66,73,85],e.keyCode)&&e.preventDefault()}},{key:"isLimited",value:function(e,n){return e=e||0,typeof n<"u"&&(P.isMove(n.keyCode)||P.isNavigation(n.keyCode)||n.ctrlKey||n.metaKey||v.contains([P.code.BACKSPACE,P.code.DELETE],n.keyCode))?!1:this.options.maxTextLength>0&&this.$editable.text().length+e>this.options.maxTextLength}},{key:"checkLinkUrl",value:function(e){return wi.test(e)?"mailto://"+e:Ci.test(e)?"tel://"+e:Si.test(e)?e:"http://"+e}},{key:"createRange",value:function(){return this.focus(),this.setLastRange(),this.getLastRange()}},{key:"createRangeFromList",value:function(e){var n=S.createFromNodeBefore(v.head(e)),i=n.getStartPoint(),r=S.createFromNodeAfter(v.last(e)),a=r.getEndPoint();return S.create(i.node,i.offset,a.node,a.offset)}},{key:"setLastRange",value:function(e){e?this.lastRange=e:(this.lastRange=S.create(this.editable),u()(this.lastRange.sc).closest(".note-editable").length===0&&(this.lastRange=S.createFromBodyElement(this.editable)))}},{key:"getLastRange",value:function(){return this.lastRange||this.setLastRange(),this.lastRange}},{key:"saveRange",value:function(e){e&&this.getLastRange().collapse().select()}},{key:"restoreRange",value:function(){this.lastRange&&(this.lastRange.select(),this.focus())}},{key:"saveTarget",value:function(e){this.$editable.data("target",e)}},{key:"clearTarget",value:function(){this.$editable.removeData("target")}},{key:"restoreTarget",value:function(){return this.$editable.data("target")}},{key:"currentStyle",value:function(){var e=S.create();return e&&(e=e.normalize()),e?this.style.current(e):this.style.fromNode(this.$editable)}},{key:"styleFromNode",value:function(e){return this.style.fromNode(e)}},{key:"undo",value:function(){this.context.triggerEvent("before.command",this.$editable.html()),this.history.undo(),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}},{key:"commit",value:function(){this.context.triggerEvent("before.command",this.$editable.html()),this.history.commit(),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}},{key:"redo",value:function(){this.context.triggerEvent("before.command",this.$editable.html()),this.history.redo(),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}},{key:"beforeCommand",value:function(){this.context.triggerEvent("before.command",this.$editable.html()),document.execCommand("styleWithCSS",!1,this.options.styleWithCSS),this.focus()}},{key:"afterCommand",value:function(e){this.normalizeContent(),this.history.recordUndo(),e||this.context.triggerEvent("change",this.$editable.html(),this.$editable)}},{key:"tab",value:function(){var e=this.getLastRange();if(e.isCollapsed()&&e.isOnCell())this.table.tab(e);else{if(this.options.tabSize===0)return!1;this.isLimited(this.options.tabSize)||(this.beforeCommand(),this.typing.insertTab(e,this.options.tabSize),this.afterCommand())}}},{key:"untab",value:function(){var e=this.getLastRange();if(e.isCollapsed()&&e.isOnCell())this.table.tab(e,!0);else if(this.options.tabSize===0)return!1}},{key:"wrapCommand",value:function(e){return function(){this.beforeCommand(),e.apply(this,arguments),this.afterCommand()}}},{key:"removed",value:function(e,n,i){e=S.create(),e.isCollapsed()&&e.isOnCell()&&(n=e.ec,(i=n.tagName)&&n.childElementCount===1&&n.childNodes[0].tagName==="BR"&&(i==="P"?n.remove():["TH","TD"].indexOf(i)>=0&&n.firstChild.remove()))}},{key:"insertImage",value:function(e,n){var i=this;return zn(e).then(function(r){i.beforeCommand(),typeof n=="function"?n(r):(typeof n=="string"&&r.attr("data-filename",n),r.css("width",Math.min(i.$editable.width(),r.width()))),r.show(),i.getLastRange().insertNode(r[0]),i.setLastRange(S.createFromNodeAfter(r[0]).select()),i.afterCommand()}).fail(function(r){i.context.triggerEvent("image.upload.error",r)})}},{key:"insertImagesAsDataURL",value:function(e){var n=this;u().each(e,function(i,r){var a=r.name;n.options.maximumImageFileSize&&n.options.maximumImageFileSize<r.size?n.context.triggerEvent("image.upload.error",n.lang.image.maximumFileSizeError):Hn(r).then(function(s){return n.insertImage(s,a)}).fail(function(){n.context.triggerEvent("image.upload.error")})})}},{key:"insertImagesOrCallback",value:function(e){var n=this.options.callbacks;n.onImageUpload?this.context.triggerEvent("image.upload",e):this.insertImagesAsDataURL(e)}},{key:"getSelectedText",value:function(){var e=this.getLastRange();return e.isOnAnchor()&&(e=S.createFromNode(l.ancestor(e.sc,l.isAnchor))),e.toString()}},{key:"onFormatBlock",value:function(e,n){if(document.execCommand("FormatBlock",!1,I.isMSIE?"<"+e+">":e),n&&n.length&&(n[0].tagName.toUpperCase()!==e.toUpperCase()&&(n=n.find(e)),n&&n.length)){var i=this.createRange(),r=u()([i.sc,i.ec]).closest(e);r.removeClass();var a=n[0].className||"";a&&r.addClass(a)}}},{key:"formatPara",value:function(){this.formatBlock("P")}},{key:"fontStyling",value:function(e,n){var i=this.getLastRange();if(i!==""){var r=this.style.styleNodes(i);if(this.$editor.find(".note-status-output").html(""),u()(r).css(e,n),i.isCollapsed()){var a=v.head(r);a&&!l.nodeLength(a)&&(a.innerHTML=l.ZERO_WIDTH_NBSP_CHAR,S.createFromNode(a.firstChild).select(),this.setLastRange(),this.$editable.data(ki,a))}else i.select()}else{var s=u().now();this.$editor.find(".note-status-output").html('<div id="note-status-output-'+s+'" class="alert alert-info">'+this.lang.output.noSelection+"</div>"),setTimeout(function(){u()("#note-status-output-"+s).remove()},5e3)}}},{key:"unlink",value:function(){var e=this.getLastRange();if(e.isOnAnchor()){var n=l.ancestor(e.sc,l.isAnchor);e=S.createFromNode(n),e.select(),this.setLastRange(),this.beforeCommand(),document.execCommand("unlink"),this.afterCommand()}}},{key:"getLinkInfo",value:function(){this.hasFocus()||this.focus();var e=this.getLastRange().expand(l.isAnchor),n=u()(v.head(e.nodes(l.isAnchor))),i={range:e,text:e.toString(),url:n.length?n.attr("href"):""};return n.length&&(i.isNewWindow=n.attr("target")==="_blank"),i}},{key:"addRow",value:function(e){var n=this.getLastRange(this.$editable);n.isCollapsed()&&n.isOnCell()&&(this.beforeCommand(),this.table.addRow(n,e),this.afterCommand())}},{key:"addCol",value:function(e){var n=this.getLastRange(this.$editable);n.isCollapsed()&&n.isOnCell()&&(this.beforeCommand(),this.table.addCol(n,e),this.afterCommand())}},{key:"deleteRow",value:function(){var e=this.getLastRange(this.$editable);e.isCollapsed()&&e.isOnCell()&&(this.beforeCommand(),this.table.deleteRow(e),this.afterCommand())}},{key:"deleteCol",value:function(){var e=this.getLastRange(this.$editable);e.isCollapsed()&&e.isOnCell()&&(this.beforeCommand(),this.table.deleteCol(e),this.afterCommand())}},{key:"deleteTable",value:function(){var e=this.getLastRange(this.$editable);e.isCollapsed()&&e.isOnCell()&&(this.beforeCommand(),this.table.deleteTable(e),this.afterCommand())}},{key:"resizeTo",value:function(e,n,i){var r;if(i){var a=e.y/e.x,s=n.data("ratio");r={width:s>a?e.x:e.y/s,height:s>a?e.x*s:e.y}}else r={width:e.x,height:e.y};n.css(r)}},{key:"hasFocus",value:function(){return this.$editable.is(":focus")}},{key:"focus",value:function(){this.hasFocus()||this.$editable.trigger("focus")}},{key:"isEmpty",value:function(){return l.isEmpty(this.$editable[0])||l.emptyPara===this.$editable.html()}},{key:"empty",value:function(){this.context.invoke("code",l.emptyPara)}},{key:"normalizeContent",value:function(){this.$editable[0].normalize()}}])}();function me(o){"@babel/helpers - typeof";return me=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},me(o)}function xi(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Ti(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Ii(n.key),n)}}function Ei(o,t,e){return t&&Ti(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Ii(o){var t=_i(o,"string");return me(t)=="symbol"?t:t+""}function _i(o,t){if(me(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(me(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Ai=function(){function o(t){xi(this,o),this.context=t,this.options=t.options,this.$editable=t.layoutInfo.editable}return Ei(o,[{key:"initialize",value:function(){this.$editable.on("paste",this.pasteByEvent.bind(this))}},{key:"pasteByEvent",value:function(e){var n=this;if(!this.context.isDisabled()){var i=e.originalEvent.clipboardData;if(i&&i.items&&i.items.length){var r=i.files,a=i.getData("Text");r.length>0&&this.options.allowClipboardImagePasting&&(this.context.invoke("editor.insertImagesOrCallback",r),e.preventDefault()),a.length>0&&this.context.invoke("editor.isLimited",a.length)&&e.preventDefault()}else if(window.clipboardData){var s=window.clipboardData.getData("text");this.context.invoke("editor.isLimited",s.length)&&e.preventDefault()}setTimeout(function(){n.context.invoke("editor.afterCommand")},10)}}}])}();function ge(o){"@babel/helpers - typeof";return ge=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ge(o)}function Li(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function $i(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Ni(n.key),n)}}function Di(o,t,e){return t&&$i(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Ni(o){var t=Ri(o,"string");return ge(t)=="symbol"?t:t+""}function Ri(o,t){if(ge(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(ge(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Fi=function(){function o(t){Li(this,o),this.context=t,this.$eventListener=u()(document),this.$editor=t.layoutInfo.editor,this.$editable=t.layoutInfo.editable,this.options=t.options,this.lang=this.options.langInfo,this.documentEventHandlers={},this.$dropzone=u()(['<div class="note-dropzone">','<div class="note-dropzone-message"></div>',"</div>"].join("")).prependTo(this.$editor)}return Di(o,[{key:"initialize",value:function(){this.options.disableDragAndDrop?(this.documentEventHandlers.onDrop=function(e){e.preventDefault()},this.$eventListener=this.$dropzone,this.$eventListener.on("drop",this.documentEventHandlers.onDrop)):this.attachDragAndDropEvent()}},{key:"attachDragAndDropEvent",value:function(){var e=this,n=u()(),i=this.$dropzone.find(".note-dropzone-message");this.documentEventHandlers.onDragenter=function(r){var a=e.context.invoke("codeview.isActivated"),s=e.$editor.width()>0&&e.$editor.height()>0;!a&&!n.length&&s&&(e.$editor.addClass("dragover"),e.$dropzone.width(e.$editor.width()),e.$dropzone.height(e.$editor.height()),i.text(e.lang.image.dragImageHere)),n=n.add(r.target)},this.documentEventHandlers.onDragleave=function(r){n=n.not(r.target),(!n.length||r.target.nodeName==="BODY")&&(n=u()(),e.$editor.removeClass("dragover"))},this.documentEventHandlers.onDrop=function(){n=u()(),e.$editor.removeClass("dragover")},this.$eventListener.on("dragenter",this.documentEventHandlers.onDragenter).on("dragleave",this.documentEventHandlers.onDragleave).on("drop",this.documentEventHandlers.onDrop),this.$dropzone.on("dragenter",function(){e.$dropzone.addClass("hover"),i.text(e.lang.image.dropImage)}).on("dragleave",function(){e.$dropzone.removeClass("hover"),i.text(e.lang.image.dragImageHere)}),this.$dropzone.on("drop",function(r){var a=r.originalEvent.dataTransfer;r.preventDefault(),a&&a.files&&a.files.length?(e.$editable.trigger("focus"),e.context.invoke("editor.insertImagesOrCallback",a.files)):u().each(a.types,function(s,c){if(!(c.toLowerCase().indexOf("_moz_")>-1)){var f=a.getData(c);c.toLowerCase().indexOf("text")>-1?e.context.invoke("editor.pasteHTML",f):u()(f).each(function(d,h){e.context.invoke("editor.insertNode",h)})}})}).on("dragover",!1)}},{key:"destroy",value:function(){var e=this;Object.keys(this.documentEventHandlers).forEach(function(n){e.$eventListener.off(n.slice(2).toLowerCase(),e.documentEventHandlers[n])}),this.documentEventHandlers={}}}])}();function be(o){"@babel/helpers - typeof";return be=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},be(o)}function Bi(o,t){var e=typeof Symbol<"u"&&o[Symbol.iterator]||o["@@iterator"];if(!e){if(Array.isArray(o)||(e=Hi(o))||t){e&&(o=e);var n=0,i=function(){};return{s:i,n:function(){return n>=o.length?{done:!0}:{done:!1,value:o[n++]}},e:function(f){throw f},f:i}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var r,a=!0,s=!1;return{s:function(){e=e.call(o)},n:function(){var f=e.next();return a=f.done,f},e:function(f){s=!0,r=f},f:function(){try{a||e.return==null||e.return()}finally{if(s)throw r}}}}function Hi(o,t){if(o){if(typeof o=="string")return Gt(o,t);var e={}.toString.call(o).slice(8,-1);return e==="Object"&&o.constructor&&(e=o.constructor.name),e==="Map"||e==="Set"?Array.from(o):e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Gt(o,t):void 0}}function Gt(o,t){(t==null||t>o.length)&&(t=o.length);for(var e=0,n=Array(t);e<t;e++)n[e]=o[e];return n}function zi(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Mi(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,ji(n.key),n)}}function Oi(o,t,e){return t&&Mi(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function ji(o){var t=Ui(o,"string");return be(t)=="symbol"?t:t+""}function Ui(o,t){if(be(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(be(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Ki=function(){function o(t){zi(this,o),this.context=t,this.$editor=t.layoutInfo.editor,this.$editable=t.layoutInfo.editable,this.$codable=t.layoutInfo.codable,this.options=t.options,this.CodeMirrorConstructor=window.CodeMirror,this.options.codemirror.CodeMirrorConstructor&&(this.CodeMirrorConstructor=this.options.codemirror.CodeMirrorConstructor)}return Oi(o,[{key:"sync",value:function(e){var n=this.isActivated(),i=this.CodeMirrorConstructor;n&&(e?i?this.$codable.data("cmEditor").getDoc().setValue(e):this.$codable.val(e):i&&this.$codable.data("cmEditor").save())}},{key:"initialize",value:function(){var e=this;this.$codable.on("keyup",function(n){n.keyCode===P.code.ESCAPE&&e.deactivate()})}},{key:"isActivated",value:function(){return this.$editor.hasClass("codeview")}},{key:"toggle",value:function(){this.isActivated()?this.deactivate():this.activate(),this.context.triggerEvent("codeview.toggled")}},{key:"purify",value:function(e){if(this.options.codeviewFilter&&(e=e.replace(this.options.codeviewFilterRegex,""),this.options.codeviewIframeFilter)){var n=this.options.codeviewIframeWhitelistSrc.concat(this.options.codeviewIframeWhitelistSrcBase);e=e.replace(/(<iframe.*?>.*?(?:<\/iframe>)?)/gi,function(i){if(/<.+src(?==?('|"|\s)?)[\s\S]+src(?=('|"|\s)?)[^>]*?>/i.test(i))return"";var r=Bi(n),a;try{for(r.s();!(a=r.n()).done;){var s=a.value;if(new RegExp('src="(https?:)?//'+s.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")+'/(.+)"').test(i))return i}}catch(c){r.e(c)}finally{r.f()}return""})}return e}},{key:"activate",value:function(){var e=this,n=this.CodeMirrorConstructor;if(this.$codable.val(l.html(this.$editable,this.options.prettifyHtml)),this.$codable.height(this.$editable.height()),this.context.invoke("toolbar.updateCodeview",!0),this.context.invoke("airPopover.updateCodeview",!0),this.$editor.addClass("codeview"),this.$codable.trigger("focus"),n){var i=n.fromTextArea(this.$codable[0],this.options.codemirror);if(this.options.codemirror.tern){var r=new n.TernServer(this.options.codemirror.tern);i.ternServer=r,i.on("cursorActivity",function(a){r.updateArgHints(a)})}i.on("blur",function(a){e.context.triggerEvent("blur.codeview",i.getValue(),a)}),i.on("change",function(){e.context.triggerEvent("change.codeview",i.getValue(),i)}),i.setSize(null,this.$editable.outerHeight()),this.$codable.data("cmEditor",i)}else this.$codable.on("blur",function(a){e.context.triggerEvent("blur.codeview",e.$codable.val(),a)}),this.$codable.on("input",function(){e.context.triggerEvent("change.codeview",e.$codable.val(),e.$codable)})}},{key:"deactivate",value:function(){var e=this.CodeMirrorConstructor;if(e){var n=this.$codable.data("cmEditor");this.$codable.val(n.getValue()),n.toTextArea()}var i=this.purify(l.value(this.$codable,this.options.prettifyHtml)||l.emptyPara),r=this.$editable.html()!==i;this.$editable.html(i),this.$editable.height(this.options.height?this.$codable.height():"auto"),this.$editor.removeClass("codeview"),r&&this.context.triggerEvent("change",this.$editable.html(),this.$editable),this.$editable.trigger("focus"),this.context.invoke("toolbar.updateCodeview",!1),this.context.invoke("airPopover.updateCodeview",!1)}},{key:"destroy",value:function(){this.isActivated()&&this.deactivate()}}])}();function ye(o){"@babel/helpers - typeof";return ye=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ye(o)}function Wi(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Vi(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Gi(n.key),n)}}function qi(o,t,e){return t&&Vi(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Gi(o){var t=Zi(o,"string");return ye(t)=="symbol"?t:t+""}function Zi(o,t){if(ye(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(ye(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Zt=24,Yi=function(){function o(t){Wi(this,o),this.$document=u()(document),this.$statusbar=t.layoutInfo.statusbar,this.$editable=t.layoutInfo.editable,this.$codable=t.layoutInfo.codable,this.options=t.options}return qi(o,[{key:"initialize",value:function(){var e=this;if(this.options.airMode||this.options.disableResizeEditor){this.destroy();return}this.$statusbar.on("mousedown touchstart",function(n){n.preventDefault(),n.stopPropagation();var i=e.$editable.offset().top-e.$document.scrollTop(),r=e.$codable.offset().top-e.$document.scrollTop(),a=function(c){var f=c.type=="mousemove"?c:c.originalEvent.touches[0],d=f.clientY-(i+Zt),h=f.clientY-(r+Zt);d=e.options.minheight>0?Math.max(d,e.options.minheight):d,d=e.options.maxHeight>0?Math.min(d,e.options.maxHeight):d,h=e.options.minheight>0?Math.max(h,e.options.minheight):h,h=e.options.maxHeight>0?Math.min(h,e.options.maxHeight):h,e.$editable.height(d),e.$codable.height(h)};e.$document.on("mousemove touchmove",a).one("mouseup touchend",function(){e.$document.off("mousemove touchmove",a)})})}},{key:"destroy",value:function(){this.$statusbar.off(),this.$statusbar.addClass("locked")}}])}();function ke(o){"@babel/helpers - typeof";return ke=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ke(o)}function Xi(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Qi(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,er(n.key),n)}}function Ji(o,t,e){return t&&Qi(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function er(o){var t=tr(o,"string");return ke(t)=="symbol"?t:t+""}function tr(o,t){if(ke(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(ke(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var or=function(){function o(t){var e=this;Xi(this,o),this.context=t,this.$editor=t.layoutInfo.editor,this.$toolbar=t.layoutInfo.toolbar,this.$editable=t.layoutInfo.editable,this.$codable=t.layoutInfo.codable,this.$window=u()(window),this.$scrollbar=u()("html, body"),this.scrollbarClassName="note-fullscreen-body",this.onResize=function(){e.resizeTo({h:e.$window.height()-e.$toolbar.outerHeight()})}}return Ji(o,[{key:"resizeTo",value:function(e){this.$editable.css("height",e.h),this.$codable.css("height",e.h),this.$codable.data("cmeditor")&&this.$codable.data("cmeditor").setsize(null,e.h)}},{key:"toggle",value:function(){this.$editor.toggleClass("fullscreen");var e=this.isFullscreen();this.$scrollbar.toggleClass(this.scrollbarClassName,e),e?(this.$editable.data("orgHeight",this.$editable.css("height")),this.$editable.data("orgMaxHeight",this.$editable.css("maxHeight")),this.$editable.css("maxHeight",""),this.$window.on("resize",this.onResize).trigger("resize")):(this.$window.off("resize",this.onResize),this.resizeTo({h:this.$editable.data("orgHeight")}),this.$editable.css("maxHeight",this.$editable.css("orgMaxHeight"))),this.context.invoke("toolbar.updateFullscreen",e)}},{key:"isFullscreen",value:function(){return this.$editor.hasClass("fullscreen")}},{key:"destroy",value:function(){this.$scrollbar.removeClass(this.scrollbarClassName)}}])}();function we(o){"@babel/helpers - typeof";return we=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},we(o)}function nr(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function ir(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,ar(n.key),n)}}function rr(o,t,e){return t&&ir(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function ar(o){var t=sr(o,"string");return we(t)=="symbol"?t:t+""}function sr(o,t){if(we(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(we(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var lr=function(){function o(t){var e=this;nr(this,o),this.context=t,this.$document=u()(document),this.$editingArea=t.layoutInfo.editingArea,this.options=t.options,this.lang=this.options.langInfo,this.events={"summernote.mousedown":function(i,r){e.update(r.target,r)&&r.preventDefault()},"summernote.keyup summernote.scroll summernote.change summernote.dialog.shown":function(){e.update()},"summernote.disable summernote.blur":function(){e.hide()},"summernote.codeview.toggled":function(){e.update()}}}return rr(o,[{key:"initialize",value:function(){var e=this;this.$handle=u()(['<div class="note-handle">','<div class="note-control-selection">','<div class="note-control-selection-bg"></div>','<div class="note-control-holder note-control-nw"></div>','<div class="note-control-holder note-control-ne"></div>','<div class="note-control-holder note-control-sw"></div>','<div class="',this.options.disableResizeImage?"note-control-holder":"note-control-sizing",' note-control-se"></div>',this.options.disableResizeImage?"":'<div class="note-control-selection-info"></div>',"</div>","</div>"].join("")).prependTo(this.$editingArea),this.$handle.on("mousedown",function(n){if(l.isControlSizing(n.target)){n.preventDefault(),n.stopPropagation();var i=e.$handle.find(".note-control-selection").data("target"),r=i.offset(),a=e.$document.scrollTop(),s=function(f){e.context.invoke("editor.resizeTo",{x:f.clientX-r.left,y:f.clientY-(r.top-a)},i,!f.shiftKey),e.update(i[0],f)};e.$document.on("mousemove",s).one("mouseup",function(c){c.preventDefault(),e.$document.off("mousemove",s),e.context.invoke("editor.afterCommand")}),i.data("ratio")||i.data("ratio",i.height()/i.width())}}),this.$handle.on("wheel",function(n){n.preventDefault(),e.update()})}},{key:"destroy",value:function(){this.$handle.remove()}},{key:"update",value:function(e,n){if(this.context.isDisabled())return!1;var i=l.isImg(e),r=this.$handle.find(".note-control-selection");if(this.context.invoke("imagePopover.update",e,n),i){var a=u()(e),s=this.$editingArea[0].getBoundingClientRect(),c=e.getBoundingClientRect();r.css({display:"block",left:c.left-s.left,top:c.top-s.top,width:c.width,height:c.height}).data("target",a);var f=new Image;f.src=a.attr("src");var d=c.width+"x"+c.height+" ("+this.lang.image.original+": "+f.width+"x"+f.height+")";r.find(".note-control-selection-info").text(d),this.context.invoke("editor.saveTarget",e)}else this.hide();return i}},{key:"hide",value:function(){this.context.invoke("editor.clearTarget"),this.$handle.children().hide()}}])}();function Ce(o){"@babel/helpers - typeof";return Ce=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ce(o)}function cr(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function ur(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,dr(n.key),n)}}function fr(o,t,e){return t&&ur(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function dr(o){var t=hr(o,"string");return Ce(t)=="symbol"?t:t+""}function hr(o,t){if(Ce(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Ce(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var pr="http://",vr=/^([A-Za-z][A-Za-z0-9+-.]*\:[\/]{2}|tel:|mailto:[A-Z0-9._%+-]+@|xmpp:[A-Z0-9._%+-]+@)?(www\.)?(.+)$/i,mr=function(){function o(t){var e=this;cr(this,o),this.context=t,this.options=t.options,this.$editable=t.layoutInfo.editable,this.events={"summernote.keyup":function(i,r){r.isDefaultPrevented()||e.handleKeyup(r)},"summernote.keydown":function(i,r){e.handleKeydown(r)}}}return fr(o,[{key:"initialize",value:function(){this.lastWordRange=null}},{key:"destroy",value:function(){this.lastWordRange=null}},{key:"replace",value:function(){if(this.lastWordRange){var e=this.lastWordRange.toString(),n=e.match(vr);if(n&&(n[1]||n[2])){var i=n[1]?e:pr+e,r=this.options.showDomainOnlyForAutolink?e.replace(/^(?:https?:\/\/)?(?:tel?:?)?(?:mailto?:?)?(?:xmpp?:?)?(?:www\.)?/i,"").split("/")[0]:e,a=u()("<a></a>").html(r).attr("href",i)[0];this.context.options.linkTargetBlank&&u()(a).attr("target","_blank"),this.lastWordRange.insertNode(a),this.lastWordRange=null,this.context.invoke("editor.focus"),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}}}},{key:"handleKeydown",value:function(e){if(v.contains([P.code.ENTER,P.code.SPACE],e.keyCode)){var n=this.context.invoke("editor.createRange").getWordRange();this.lastWordRange=n}}},{key:"handleKeyup",value:function(e){(P.code.SPACE===e.keyCode||P.code.ENTER===e.keyCode&&!e.shiftKey)&&this.replace()}}])}();function Se(o){"@babel/helpers - typeof";return Se=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Se(o)}function gr(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function br(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,kr(n.key),n)}}function yr(o,t,e){return t&&br(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function kr(o){var t=wr(o,"string");return Se(t)=="symbol"?t:t+""}function wr(o,t){if(Se(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Se(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Cr=function(){function o(t){var e=this;gr(this,o),this.$note=t.layoutInfo.note,this.events={"summernote.change":function(){e.$note.val(t.invoke("code"))}}}return yr(o,[{key:"shouldInitialize",value:function(){return l.isTextarea(this.$note[0])}}])}();function Pe(o){"@babel/helpers - typeof";return Pe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pe(o)}function Sr(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Pr(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Tr(n.key),n)}}function xr(o,t,e){return t&&Pr(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Tr(o){var t=Er(o,"string");return Pe(t)=="symbol"?t:t+""}function Er(o,t){if(Pe(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Pe(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Ir=function(){function o(t){var e=this;Sr(this,o),this.context=t,this.options=t.options.replace||{},this.keys=[P.code.ENTER,P.code.SPACE,P.code.PERIOD,P.code.COMMA,P.code.SEMICOLON,P.code.SLASH],this.previousKeydownCode=null,this.events={"summernote.keyup":function(i,r){r.isDefaultPrevented()||e.handleKeyup(r)},"summernote.keydown":function(i,r){e.handleKeydown(r)}}}return xr(o,[{key:"shouldInitialize",value:function(){return!!this.options.match}},{key:"initialize",value:function(){this.lastWord=null}},{key:"destroy",value:function(){this.lastWord=null}},{key:"replace",value:function(){if(this.lastWord){var e=this,n=this.lastWord.toString();this.options.match(n,function(i){if(i){var r="";if(typeof i=="string"?r=l.createText(i):i instanceof jQuery?r=i[0]:i instanceof Node&&(r=i),!r)return;e.lastWord.insertNode(r),e.lastWord=null,e.context.invoke("editor.focus")}})}}},{key:"handleKeydown",value:function(e){if(this.previousKeydownCode&&v.contains(this.keys,this.previousKeydownCode)){this.previousKeydownCode=e.keyCode;return}if(v.contains(this.keys,e.keyCode)){var n=this.context.invoke("editor.createRange").getWordRange();this.lastWord=n}this.previousKeydownCode=e.keyCode}},{key:"handleKeyup",value:function(e){v.contains(this.keys,e.keyCode)&&this.replace()}}])}();function xe(o){"@babel/helpers - typeof";return xe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xe(o)}function _r(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Ar(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,$r(n.key),n)}}function Lr(o,t,e){return t&&Ar(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function $r(o){var t=Dr(o,"string");return xe(t)=="symbol"?t:t+""}function Dr(o,t){if(xe(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(xe(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Nr=function(){function o(t){var e=this;_r(this,o),this.context=t,this.$editingArea=t.layoutInfo.editingArea,this.options=t.options,this.options.inheritPlaceholder===!0&&(this.options.placeholder=this.context.$note.attr("placeholder")||this.options.placeholder),this.events={"summernote.init summernote.change":function(){e.update()},"summernote.codeview.toggled":function(){e.update()}}}return Lr(o,[{key:"shouldInitialize",value:function(){return!!this.options.placeholder}},{key:"initialize",value:function(){var e=this;this.$placeholder=u()('<div class="note-placeholder"></div>'),this.$placeholder.on("click",function(){e.context.invoke("focus")}).html(this.options.placeholder).prependTo(this.$editingArea),this.update()}},{key:"destroy",value:function(){this.$placeholder.remove()}},{key:"update",value:function(){var e=!this.context.invoke("codeview.isActivated")&&this.context.invoke("editor.isEmpty");this.$placeholder.toggle(e)}}])}();function Te(o){"@babel/helpers - typeof";return Te=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Te(o)}function Rr(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Fr(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Hr(n.key),n)}}function Br(o,t,e){return t&&Fr(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Hr(o){var t=zr(o,"string");return Te(t)=="symbol"?t:t+""}function zr(o,t){if(Te(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Te(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Mr=function(){function o(t){Rr(this,o),this.ui=u().summernote.ui,this.context=t,this.$toolbar=t.layoutInfo.toolbar,this.options=t.options,this.lang=this.options.langInfo,this.invertedKeyMap=k.invertObject(this.options.keyMap[I.isMac?"mac":"pc"])}return Br(o,[{key:"representShortcut",value:function(e){var n=this.invertedKeyMap[e];return!this.options.shortcuts||!n?"":(I.isMac&&(n=n.replace("CMD","⌘").replace("SHIFT","⇧")),n=n.replace("BACKSLASH","\\").replace("SLASH","/").replace("LEFTBRACKET","[").replace("RIGHTBRACKET","]")," ("+n+")")}},{key:"button",value:function(e){return!this.options.tooltip&&e.tooltip&&delete e.tooltip,e.container=this.options.container,this.ui.button(e)}},{key:"initialize",value:function(){this.addToolbarButtons(),this.addImagePopoverButtons(),this.addLinkPopoverButtons(),this.addTablePopoverButtons(),this.fontInstalledMap={}}},{key:"destroy",value:function(){delete this.fontInstalledMap}},{key:"isFontInstalled",value:function(e){return Object.prototype.hasOwnProperty.call(this.fontInstalledMap,e)||(this.fontInstalledMap[e]=I.isFontInstalled(e)||v.contains(this.options.fontNamesIgnoreCheck,e)),this.fontInstalledMap[e]}},{key:"isFontDeservedToAdd",value:function(e){return e=e.toLowerCase(),e!==""&&this.isFontInstalled(e)&&I.genericFontFamilies.indexOf(e)===-1}},{key:"colorPalette",value:function(e,n,i,r){var a=this;return this.ui.buttonGroup({className:"note-color "+e,children:[this.button({className:"note-current-color-button",contents:this.ui.icon(this.options.icons.font+" note-recent-color"),tooltip:n,click:function(c){var f=u()(c.currentTarget);i&&r?a.context.invoke("editor.color",{backColor:f.attr("data-backColor"),foreColor:f.attr("data-foreColor")}):i?a.context.invoke("editor.color",{backColor:f.attr("data-backColor")}):r&&a.context.invoke("editor.color",{foreColor:f.attr("data-foreColor")})},callback:function(c){var f=c.find(".note-recent-color");i&&(f.css("background-color",a.options.colorButton.backColor),c.attr("data-backColor",a.options.colorButton.backColor)),r?(f.css("color",a.options.colorButton.foreColor),c.attr("data-foreColor",a.options.colorButton.foreColor)):f.css("color","transparent")}}),this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents("",this.options),tooltip:this.lang.color.more,data:{toggle:"dropdown"}}),this.ui.dropdown({items:(i?['<div class="note-palette">','<div class="note-palette-title">'+this.lang.color.background+"</div>","<div>",'<button type="button" class="note-color-reset btn btn-light btn-default" data-event="backColor" data-value="transparent">',this.lang.color.transparent,"</button>","</div>",'<div class="note-holder" data-event="backColor"><!-- back colors --></div>',"<div>",'<button type="button" class="note-color-select btn btn-light btn-default" data-event="openPalette" data-value="backColorPicker-'+this.options.id+'">',this.lang.color.cpSelect,"</button>",'<input type="color" id="backColorPicker-'+this.options.id+'" class="note-btn note-color-select-btn" value="'+this.options.colorButton.backColor+'" data-event="backColorPalette-'+this.options.id+'">',"</div>",'<div class="note-holder-custom" id="backColorPalette-'+this.options.id+'" data-event="backColor"></div>',"</div>"].join(""):"")+(r?['<div class="note-palette">','<div class="note-palette-title">'+this.lang.color.foreground+"</div>","<div>",'<button type="button" class="note-color-reset btn btn-light btn-default" data-event="removeFormat" data-value="foreColor">',this.lang.color.resetToDefault,"</button>","</div>",'<div class="note-holder" data-event="foreColor"><!-- fore colors --></div>',"<div>",'<button type="button" class="note-color-select btn btn-light btn-default" data-event="openPalette" data-value="foreColorPicker-'+this.options.id+'">',this.lang.color.cpSelect,"</button>",'<input type="color" id="foreColorPicker-'+this.options.id+'" class="note-btn note-color-select-btn" value="'+this.options.colorButton.foreColor+'" data-event="foreColorPalette-'+this.options.id+'">',"</div>",'<div class="note-holder-custom" id="foreColorPalette-'+this.options.id+'" data-event="foreColor"></div>',"</div>"].join(""):""),callback:function(c){c.find(".note-holder").each(function(d,h){var p=u()(h);p.append(a.ui.palette({colors:a.options.colors,colorsName:a.options.colorsName,eventName:p.data("event"),container:a.options.container,tooltip:a.options.tooltip}).render())});var f=[["#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF"]];c.find(".note-holder-custom").each(function(d,h){var p=u()(h);p.append(a.ui.palette({colors:f,colorsName:f,eventName:p.data("event"),container:a.options.container,tooltip:a.options.tooltip}).render())}),c.find("input[type=color]").each(function(d,h){u()(h).on("change",function(){var p=c.find("#"+u()(this).data("event")).find(".note-color-btn").first(),m=this.value.toUpperCase();p.css("background-color",m).attr("aria-label",m).attr("data-value",m).attr("data-original-title",m),p.trigger("click")})})},click:function(c){c.stopPropagation();var f=u()("."+e).find(".note-dropdown-menu"),d=u()(c.target),h=d.data("event"),p=d.attr("data-value");if(h==="openPalette"){var m=f.find("#"+p),y=u()(f.find("#"+m.data("event")).find(".note-color-row")[0]),w=y.find(".note-color-btn").last().detach(),T=m.val();w.css("background-color",T).attr("aria-label",T).attr("data-value",T).attr("data-original-title",T),y.prepend(w),m.trigger("click")}else{if(v.contains(["backColor","foreColor"],h)){var A=h==="backColor"?"background-color":"color",b=d.closest(".note-color").find(".note-recent-color"),g=d.closest(".note-color").find(".note-current-color-button");b.css(A,p),g.attr("data-"+h,p)}a.context.invoke("editor."+h,p)}}})]}).render()}},{key:"addToolbarButtons",value:function(){var e=this;this.context.memo("button.style",function(){return e.ui.buttonGroup([e.button({className:"dropdown-toggle",contents:e.ui.dropdownButtonContents(e.ui.icon(e.options.icons.magic),e.options),tooltip:e.lang.style.style,data:{toggle:"dropdown"}}),e.ui.dropdown({className:"dropdown-style",items:e.options.styleTags,title:e.lang.style.style,template:function(m){typeof m=="string"&&(m={tag:m,title:Object.prototype.hasOwnProperty.call(e.lang.style,m)?e.lang.style[m]:m});var y=m.tag,w=m.title,T=m.style?' style="'+m.style+'" ':"",A=m.className?' class="'+m.className+'"':"";return"<"+y+T+A+">"+w+"</"+y+">"},click:e.context.createInvokeHandler("editor.formatBlock")})]).render()});for(var n=function(){var m=e.options.styleTags[i];e.context.memo("button.style."+m,function(){return e.button({className:"note-btn-style-"+m,contents:'<div data-value="'+m+'">'+m.toUpperCase()+"</div>",tooltip:e.lang.style[m],click:e.context.createInvokeHandler("editor.formatBlock")}).render()})},i=0,r=this.options.styleTags.length;i<r;i++)n();this.context.memo("button.bold",function(){return e.button({className:"note-btn-bold",contents:e.ui.icon(e.options.icons.bold),tooltip:e.lang.font.bold+e.representShortcut("bold"),click:e.context.createInvokeHandlerAndUpdateState("editor.bold")}).render()}),this.context.memo("button.italic",function(){return e.button({className:"note-btn-italic",contents:e.ui.icon(e.options.icons.italic),tooltip:e.lang.font.italic+e.representShortcut("italic"),click:e.context.createInvokeHandlerAndUpdateState("editor.italic")}).render()}),this.context.memo("button.underline",function(){return e.button({className:"note-btn-underline",contents:e.ui.icon(e.options.icons.underline),tooltip:e.lang.font.underline+e.representShortcut("underline"),click:e.context.createInvokeHandlerAndUpdateState("editor.underline")}).render()}),this.context.memo("button.clear",function(){return e.button({contents:e.ui.icon(e.options.icons.eraser),tooltip:e.lang.font.clear+e.representShortcut("removeFormat"),click:e.context.createInvokeHandler("editor.removeFormat")}).render()}),this.context.memo("button.strikethrough",function(){return e.button({className:"note-btn-strikethrough",contents:e.ui.icon(e.options.icons.strikethrough),tooltip:e.lang.font.strikethrough+e.representShortcut("strikethrough"),click:e.context.createInvokeHandlerAndUpdateState("editor.strikethrough")}).render()}),this.context.memo("button.superscript",function(){return e.button({className:"note-btn-superscript",contents:e.ui.icon(e.options.icons.superscript),tooltip:e.lang.font.superscript,click:e.context.createInvokeHandlerAndUpdateState("editor.superscript")}).render()}),this.context.memo("button.subscript",function(){return e.button({className:"note-btn-subscript",contents:e.ui.icon(e.options.icons.subscript),tooltip:e.lang.font.subscript,click:e.context.createInvokeHandlerAndUpdateState("editor.subscript")}).render()}),this.context.memo("button.fontname",function(){var p=e.context.invoke("editor.currentStyle");return e.options.addDefaultFonts&&u().each(p["font-family"].split(","),function(m,y){y=y.trim().replace(/['"]+/g,""),e.isFontDeservedToAdd(y)&&e.options.fontNames.indexOf(y)===-1&&e.options.fontNames.push(y)}),e.ui.buttonGroup([e.button({className:"dropdown-toggle",contents:e.ui.dropdownButtonContents('<span class="note-current-fontname"></span>',e.options),tooltip:e.lang.font.name,data:{toggle:"dropdown"}}),e.ui.dropdownCheck({className:"dropdown-fontname",checkClassName:e.options.icons.menuCheck,items:e.options.fontNames.filter(e.isFontInstalled.bind(e)),title:e.lang.font.name,template:function(y){return'<span style="font-family: '+I.validFontName(y)+'">'+y+"</span>"},click:e.context.createInvokeHandlerAndUpdateState("editor.fontName")})]).render()}),this.context.memo("button.fontsize",function(){return e.ui.buttonGroup([e.button({className:"dropdown-toggle",contents:e.ui.dropdownButtonContents('<span class="note-current-fontsize"></span>',e.options),tooltip:e.lang.font.size,data:{toggle:"dropdown"}}),e.ui.dropdownCheck({className:"dropdown-fontsize",checkClassName:e.options.icons.menuCheck,items:e.options.fontSizes,title:e.lang.font.size,click:e.context.createInvokeHandlerAndUpdateState("editor.fontSize")})]).render()}),this.context.memo("button.fontsizeunit",function(){return e.ui.buttonGroup([e.button({className:"dropdown-toggle",contents:e.ui.dropdownButtonContents('<span class="note-current-fontsizeunit"></span>',e.options),tooltip:e.lang.font.sizeunit,data:{toggle:"dropdown"}}),e.ui.dropdownCheck({className:"dropdown-fontsizeunit",checkClassName:e.options.icons.menuCheck,items:e.options.fontSizeUnits,title:e.lang.font.sizeunit,click:e.context.createInvokeHandlerAndUpdateState("editor.fontSizeUnit")})]).render()}),this.context.memo("button.color",function(){return e.colorPalette("note-color-all",e.lang.color.recent,!0,!0)}),this.context.memo("button.forecolor",function(){return e.colorPalette("note-color-fore",e.lang.color.foreground,!1,!0)}),this.context.memo("button.backcolor",function(){return e.colorPalette("note-color-back",e.lang.color.background,!0,!1)}),this.context.memo("button.ul",function(){return e.button({contents:e.ui.icon(e.options.icons.unorderedlist),tooltip:e.lang.lists.unordered+e.representShortcut("insertUnorderedList"),click:e.context.createInvokeHandler("editor.insertUnorderedList")}).render()}),this.context.memo("button.ol",function(){return e.button({contents:e.ui.icon(e.options.icons.orderedlist),tooltip:e.lang.lists.ordered+e.representShortcut("insertOrderedList"),click:e.context.createInvokeHandler("editor.insertOrderedList")}).render()});var a=this.button({contents:this.ui.icon(this.options.icons.alignLeft),tooltip:this.lang.paragraph.left+this.representShortcut("justifyLeft"),click:this.context.createInvokeHandler("editor.justifyLeft")}),s=this.button({contents:this.ui.icon(this.options.icons.alignCenter),tooltip:this.lang.paragraph.center+this.representShortcut("justifyCenter"),click:this.context.createInvokeHandler("editor.justifyCenter")}),c=this.button({contents:this.ui.icon(this.options.icons.alignRight),tooltip:this.lang.paragraph.right+this.representShortcut("justifyRight"),click:this.context.createInvokeHandler("editor.justifyRight")}),f=this.button({contents:this.ui.icon(this.options.icons.alignJustify),tooltip:this.lang.paragraph.justify+this.representShortcut("justifyFull"),click:this.context.createInvokeHandler("editor.justifyFull")}),d=this.button({contents:this.ui.icon(this.options.icons.outdent),tooltip:this.lang.paragraph.outdent+this.representShortcut("outdent"),click:this.context.createInvokeHandler("editor.outdent")}),h=this.button({contents:this.ui.icon(this.options.icons.indent),tooltip:this.lang.paragraph.indent+this.representShortcut("indent"),click:this.context.createInvokeHandler("editor.indent")});this.context.memo("button.justifyLeft",k.invoke(a,"render")),this.context.memo("button.justifyCenter",k.invoke(s,"render")),this.context.memo("button.justifyRight",k.invoke(c,"render")),this.context.memo("button.justifyFull",k.invoke(f,"render")),this.context.memo("button.outdent",k.invoke(d,"render")),this.context.memo("button.indent",k.invoke(h,"render")),this.context.memo("button.paragraph",function(){return e.ui.buttonGroup([e.button({className:"dropdown-toggle",contents:e.ui.dropdownButtonContents(e.ui.icon(e.options.icons.alignLeft),e.options),tooltip:e.lang.paragraph.paragraph,data:{toggle:"dropdown"}}),e.ui.dropdown([e.ui.buttonGroup({className:"note-align",children:[a,s,c,f]}),e.ui.buttonGroup({className:"note-list",children:[d,h]})])]).render()}),this.context.memo("button.height",function(){return e.ui.buttonGroup([e.button({className:"dropdown-toggle",contents:e.ui.dropdownButtonContents(e.ui.icon(e.options.icons.textHeight),e.options),tooltip:e.lang.font.height,data:{toggle:"dropdown"}}),e.ui.dropdownCheck({items:e.options.lineHeights,checkClassName:e.options.icons.menuCheck,className:"dropdown-line-height",title:e.lang.font.height,click:e.context.createInvokeHandler("editor.lineHeight")})]).render()}),this.context.memo("button.table",function(){return e.ui.buttonGroup([e.button({className:"dropdown-toggle",contents:e.ui.dropdownButtonContents(e.ui.icon(e.options.icons.table),e.options),tooltip:e.lang.table.table,data:{toggle:"dropdown"}}),e.ui.dropdown({title:e.lang.table.table,className:"note-table",items:['<div class="note-dimension-picker">','<div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"></div>','<div class="note-dimension-picker-highlighted"></div>','<div class="note-dimension-picker-unhighlighted"></div>',"</div>",'<div class="note-dimension-display">1 x 1</div>'].join("")})],{callback:function(m){var y=m.find(".note-dimension-picker-mousecatcher");y.css({width:e.options.insertTableMaxSize.col+"em",height:e.options.insertTableMaxSize.row+"em"}).on("mousedown",e.context.createInvokeHandler("editor.insertTable")).on("mousemove",e.tableMoveHandler.bind(e))}}).render()}),this.context.memo("button.link",function(){return e.button({contents:e.ui.icon(e.options.icons.link),tooltip:e.lang.link.link+e.representShortcut("linkDialog.show"),click:e.context.createInvokeHandler("linkDialog.show")}).render()}),this.context.memo("button.picture",function(){return e.button({contents:e.ui.icon(e.options.icons.picture),tooltip:e.lang.image.image,click:e.context.createInvokeHandler("imageDialog.show")}).render()}),this.context.memo("button.video",function(){return e.button({contents:e.ui.icon(e.options.icons.video),tooltip:e.lang.video.video,click:e.context.createInvokeHandler("videoDialog.show")}).render()}),this.context.memo("button.hr",function(){return e.button({contents:e.ui.icon(e.options.icons.minus),tooltip:e.lang.hr.insert+e.representShortcut("insertHorizontalRule"),click:e.context.createInvokeHandler("editor.insertHorizontalRule")}).render()}),this.context.memo("button.fullscreen",function(){return e.button({className:"btn-fullscreen note-codeview-keep",contents:e.ui.icon(e.options.icons.arrowsAlt),tooltip:e.lang.options.fullscreen,click:e.context.createInvokeHandler("fullscreen.toggle")}).render()}),this.context.memo("button.codeview",function(){return e.button({className:"btn-codeview note-codeview-keep",contents:e.ui.icon(e.options.icons.code),tooltip:e.lang.options.codeview,click:e.context.createInvokeHandler("codeview.toggle")}).render()}),this.context.memo("button.redo",function(){return e.button({contents:e.ui.icon(e.options.icons.redo),tooltip:e.lang.history.redo+e.representShortcut("redo"),click:e.context.createInvokeHandler("editor.redo")}).render()}),this.context.memo("button.undo",function(){return e.button({contents:e.ui.icon(e.options.icons.undo),tooltip:e.lang.history.undo+e.representShortcut("undo"),click:e.context.createInvokeHandler("editor.undo")}).render()}),this.context.memo("button.help",function(){return e.button({contents:e.ui.icon(e.options.icons.question),tooltip:e.lang.options.help,click:e.context.createInvokeHandler("helpDialog.show")}).render()})}},{key:"addImagePopoverButtons",value:function(){var e=this;this.context.memo("button.resizeFull",function(){return e.button({contents:'<span class="note-fontsize-10">100%</span>',tooltip:e.lang.image.resizeFull,click:e.context.createInvokeHandler("editor.resize","1")}).render()}),this.context.memo("button.resizeHalf",function(){return e.button({contents:'<span class="note-fontsize-10">50%</span>',tooltip:e.lang.image.resizeHalf,click:e.context.createInvokeHandler("editor.resize","0.5")}).render()}),this.context.memo("button.resizeQuarter",function(){return e.button({contents:'<span class="note-fontsize-10">25%</span>',tooltip:e.lang.image.resizeQuarter,click:e.context.createInvokeHandler("editor.resize","0.25")}).render()}),this.context.memo("button.resizeNone",function(){return e.button({contents:e.ui.icon(e.options.icons.rollback),tooltip:e.lang.image.resizeNone,click:e.context.createInvokeHandler("editor.resize","0")}).render()}),this.context.memo("button.floatLeft",function(){return e.button({contents:e.ui.icon(e.options.icons.floatLeft),tooltip:e.lang.image.floatLeft,click:e.context.createInvokeHandler("editor.floatMe","left")}).render()}),this.context.memo("button.floatRight",function(){return e.button({contents:e.ui.icon(e.options.icons.floatRight),tooltip:e.lang.image.floatRight,click:e.context.createInvokeHandler("editor.floatMe","right")}).render()}),this.context.memo("button.floatNone",function(){return e.button({contents:e.ui.icon(e.options.icons.rollback),tooltip:e.lang.image.floatNone,click:e.context.createInvokeHandler("editor.floatMe","none")}).render()}),this.context.memo("button.removeMedia",function(){return e.button({contents:e.ui.icon(e.options.icons.trash),tooltip:e.lang.image.remove,click:e.context.createInvokeHandler("editor.removeMedia")}).render()})}},{key:"addLinkPopoverButtons",value:function(){var e=this;this.context.memo("button.linkDialogShow",function(){return e.button({contents:e.ui.icon(e.options.icons.link),tooltip:e.lang.link.edit,click:e.context.createInvokeHandler("linkDialog.show")}).render()}),this.context.memo("button.unlink",function(){return e.button({contents:e.ui.icon(e.options.icons.unlink),tooltip:e.lang.link.unlink,click:e.context.createInvokeHandler("editor.unlink")}).render()})}},{key:"addTablePopoverButtons",value:function(){var e=this;this.context.memo("button.addRowUp",function(){return e.button({className:"btn-md",contents:e.ui.icon(e.options.icons.rowAbove),tooltip:e.lang.table.addRowAbove,click:e.context.createInvokeHandler("editor.addRow","top")}).render()}),this.context.memo("button.addRowDown",function(){return e.button({className:"btn-md",contents:e.ui.icon(e.options.icons.rowBelow),tooltip:e.lang.table.addRowBelow,click:e.context.createInvokeHandler("editor.addRow","bottom")}).render()}),this.context.memo("button.addColLeft",function(){return e.button({className:"btn-md",contents:e.ui.icon(e.options.icons.colBefore),tooltip:e.lang.table.addColLeft,click:e.context.createInvokeHandler("editor.addCol","left")}).render()}),this.context.memo("button.addColRight",function(){return e.button({className:"btn-md",contents:e.ui.icon(e.options.icons.colAfter),tooltip:e.lang.table.addColRight,click:e.context.createInvokeHandler("editor.addCol","right")}).render()}),this.context.memo("button.deleteRow",function(){return e.button({className:"btn-md",contents:e.ui.icon(e.options.icons.rowRemove),tooltip:e.lang.table.delRow,click:e.context.createInvokeHandler("editor.deleteRow")}).render()}),this.context.memo("button.deleteCol",function(){return e.button({className:"btn-md",contents:e.ui.icon(e.options.icons.colRemove),tooltip:e.lang.table.delCol,click:e.context.createInvokeHandler("editor.deleteCol")}).render()}),this.context.memo("button.deleteTable",function(){return e.button({className:"btn-md",contents:e.ui.icon(e.options.icons.trash),tooltip:e.lang.table.delTable,click:e.context.createInvokeHandler("editor.deleteTable")}).render()})}},{key:"build",value:function(e,n){for(var i=0,r=n.length;i<r;i++){for(var a=n[i],s=Array.isArray(a)?a[0]:a,c=Array.isArray(a)?a.length===1?[a[0]]:a[1]:[a],f=this.ui.buttonGroup({className:"note-"+s}).render(),d=0,h=c.length;d<h;d++){var p=this.context.memo("button."+c[d]);p&&f.append(typeof p=="function"?p(this.context):p)}f.appendTo(e)}}},{key:"updateCurrentStyle",value:function(e){var n=e||this.$toolbar,i=this.context.invoke("editor.currentStyle");if(this.updateBtnStates(n,{".note-btn-bold":function(){return i["font-bold"]==="bold"},".note-btn-italic":function(){return i["font-italic"]==="italic"},".note-btn-underline":function(){return i["font-underline"]==="underline"},".note-btn-subscript":function(){return i["font-subscript"]==="subscript"},".note-btn-superscript":function(){return i["font-superscript"]==="superscript"},".note-btn-strikethrough":function(){return i["font-strikethrough"]==="strikethrough"}}),i["font-family"]){var r=i["font-family"].split(",").map(function(d){return d.replace(/[\'\"]/g,"").replace(/\s+$/,"").replace(/^\s+/,"")}),a=v.find(r,this.isFontInstalled.bind(this));n.find(".dropdown-fontname a").each(function(d,h){var p=u()(h),m=p.data("value")+""==a+"";p.toggleClass("checked",m)}),n.find(".note-current-fontname").text(a).css("font-family",a)}if(i["font-size"]){var s=i["font-size"];n.find(".dropdown-fontsize a").each(function(d,h){var p=u()(h),m=p.data("value")+""==s+"";p.toggleClass("checked",m)}),n.find(".note-current-fontsize").text(s);var c=i["font-size-unit"];n.find(".dropdown-fontsizeunit a").each(function(d,h){var p=u()(h),m=p.data("value")+""==c+"";p.toggleClass("checked",m)}),n.find(".note-current-fontsizeunit").text(c)}if(i["line-height"]){var f=i["line-height"];n.find(".dropdown-line-height a").each(function(d,h){var p=u()(h),m=u()(h).data("value")+""==f+"";p.toggleClass("checked",m)}),n.find(".note-current-line-height").text(f)}}},{key:"updateBtnStates",value:function(e,n){var i=this;u().each(n,function(r,a){i.ui.toggleBtnActive(e.find(r),a())})}},{key:"tableMoveHandler",value:function(e){var n=18,i=u()(e.target.parentNode),r=i.next(),a=i.find(".note-dimension-picker-mousecatcher"),s=i.find(".note-dimension-picker-highlighted"),c=i.find(".note-dimension-picker-unhighlighted"),f;if(e.offsetX===void 0){var d=u()(e.target).offset();f={x:e.pageX-d.left,y:e.pageY-d.top}}else f={x:e.offsetX,y:e.offsetY};var h={c:Math.ceil(f.x/n)||1,r:Math.ceil(f.y/n)||1};s.css({width:h.c+"em",height:h.r+"em"}),a.data("value",h.c+"x"+h.r),h.c>3&&h.c<this.options.insertTableMaxSize.col&&c.css({width:h.c+1+"em"}),h.r>3&&h.r<this.options.insertTableMaxSize.row&&c.css({height:h.r+1+"em"}),r.html(h.c+" x "+h.r)}}])}();function Ee(o){"@babel/helpers - typeof";return Ee=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ee(o)}function Or(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function jr(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Kr(n.key),n)}}function Ur(o,t,e){return t&&jr(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Kr(o){var t=Wr(o,"string");return Ee(t)=="symbol"?t:t+""}function Wr(o,t){if(Ee(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Ee(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Vr=function(){function o(t){Or(this,o),this.context=t,this.$window=u()(window),this.$document=u()(document),this.ui=u().summernote.ui,this.$note=t.layoutInfo.note,this.$editor=t.layoutInfo.editor,this.$toolbar=t.layoutInfo.toolbar,this.$editable=t.layoutInfo.editable,this.$statusbar=t.layoutInfo.statusbar,this.options=t.options,this.isFollowing=!1,this.followScroll=this.followScroll.bind(this)}return Ur(o,[{key:"shouldInitialize",value:function(){return!this.options.airMode}},{key:"initialize",value:function(){var e=this;this.options.toolbar=this.options.toolbar||[],this.options.toolbar.length?this.context.invoke("buttons.build",this.$toolbar,this.options.toolbar):this.$toolbar.hide(),this.options.toolbarContainer&&this.$toolbar.appendTo(this.options.toolbarContainer),this.changeContainer(!1),this.$note.on("summernote.keyup summernote.mouseup summernote.change",function(){e.context.invoke("buttons.updateCurrentStyle")}),this.context.invoke("buttons.updateCurrentStyle"),this.options.followingToolbar&&this.$window.on("scroll resize",this.followScroll)}},{key:"destroy",value:function(){this.$toolbar.children().remove(),this.options.followingToolbar&&this.$window.off("scroll resize",this.followScroll)}},{key:"followScroll",value:function(){if(this.$editor.hasClass("fullscreen"))return!1;var e=this.$editor.outerHeight(),n=this.$editor.width(),i=this.$toolbar.height(),r=this.$statusbar.height(),a=0;this.options.otherStaticBar&&(a=u()(this.options.otherStaticBar).outerHeight());var s=this.$document.scrollTop(),c=this.$editor.offset().top,f=c+e,d=c-a,h=f-a-i-r;!this.isFollowing&&s>d&&s<h-i?(this.isFollowing=!0,this.$editable.css({marginTop:this.$toolbar.outerHeight()}),this.$toolbar.css({position:"fixed",top:a,width:n,zIndex:1e3})):this.isFollowing&&(s<d||s>h)&&(this.isFollowing=!1,this.$toolbar.css({position:"relative",top:0,width:"100%",zIndex:"auto"}),this.$editable.css({marginTop:""}))}},{key:"changeContainer",value:function(e){e?this.$toolbar.prependTo(this.$editor):this.options.toolbarContainer&&this.$toolbar.appendTo(this.options.toolbarContainer),this.options.followingToolbar&&this.followScroll()}},{key:"updateFullscreen",value:function(e){this.ui.toggleBtnActive(this.$toolbar.find(".btn-fullscreen"),e),this.changeContainer(e)}},{key:"updateCodeview",value:function(e){this.ui.toggleBtnActive(this.$toolbar.find(".btn-codeview"),e),e?this.deactivate():this.activate()}},{key:"activate",value:function(e){var n=this.$toolbar.find("button");e||(n=n.not(".note-codeview-keep")),this.ui.toggleBtn(n,!0)}},{key:"deactivate",value:function(e){var n=this.$toolbar.find("button");e||(n=n.not(".note-codeview-keep")),this.ui.toggleBtn(n,!1)}}])}();function Ie(o){"@babel/helpers - typeof";return Ie=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ie(o)}function qr(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Gr(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Yr(n.key),n)}}function Zr(o,t,e){return t&&Gr(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Yr(o){var t=Xr(o,"string");return Ie(t)=="symbol"?t:t+""}function Xr(o,t){if(Ie(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Ie(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Qr=/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,Jr=/^(\+?\d{1,3}[\s-]?)?(\d{1,4})[\s-]?(\d{1,4})[\s-]?(\d{1,4})$/,ea=/^([A-Za-z][A-Za-z0-9+-.]*\:|#|\/)/,ta=function(){function o(t){qr(this,o),this.context=t,this.ui=u().summernote.ui,this.$body=u()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo,t.memo("help.linkDialog.show",this.options.langInfo.help["linkDialog.show"])}return Zr(o,[{key:"initialize",value:function(){var e=this.options.dialogsInBody?this.$body:this.options.container,n=['<div class="form-group note-form-group">','<label for="note-dialog-link-txt-'.concat(this.options.id,'" class="note-form-label">').concat(this.lang.link.textToDisplay,"</label>"),'<input id="note-dialog-link-txt-'.concat(this.options.id,'" class="note-link-text form-control note-form-control note-input" type="text"/>'),"</div>",'<div class="form-group note-form-group">','<label for="note-dialog-link-url-'.concat(this.options.id,'" class="note-form-label">').concat(this.lang.link.url,"</label>"),'<input id="note-dialog-link-url-'.concat(this.options.id,'" class="note-link-url form-control note-form-control note-input" type="text" value="http://"/>'),"</div>",this.options.disableLinkTarget?"":u()("<div></div>").append(this.ui.checkbox({className:"sn-checkbox-open-in-new-window",text:this.lang.link.openInNewWindow,checked:!0}).render()).html()].join(""),i="btn btn-primary note-btn note-btn-primary note-link-btn",r='<input type="button" href="#" class="'.concat(i,'" value="').concat(this.lang.link.insert,'" disabled>');this.$dialog=this.ui.dialog({className:"link-dialog",title:this.lang.link.insert,fade:this.options.dialogsFade,body:n,footer:r}).render().appendTo(e)}},{key:"destroy",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:"bindEnterKey",value:function(e,n){e.on("keypress",function(i){i.keyCode===P.code.ENTER&&(i.preventDefault(),n.trigger("click"))})}},{key:"checkLinkUrl",value:function(e){return Qr.test(e)?"mailto://"+e:Jr.test(e)?"tel://"+e:ea.test(e)?e:"http://"+e}},{key:"onCheckLinkUrl",value:function(e){var n=this;e.on("blur",function(i){i.target.value=i.target.value==""?"":n.checkLinkUrl(i.target.value)})}},{key:"toggleLinkBtn",value:function(e,n,i){this.ui.toggleBtn(e,n.val()&&i.val())}},{key:"showLinkDialog",value:function(e){var n=this;return u().Deferred(function(i){var r=n.$dialog.find(".note-link-text"),a=n.$dialog.find(".note-link-url"),s=n.$dialog.find(".note-link-btn"),c=n.$dialog.find(".sn-checkbox-open-in-new-window input[type=checkbox]");n.ui.onDialogShown(n.$dialog,function(){n.context.triggerEvent("dialog.shown"),!e.url&&k.isValidUrl(e.text)&&(e.url=n.checkLinkUrl(e.text)),r.on("input paste propertychange",function(){var d=r.val(),h=document.createElement("div");h.innerText=d,d=h.innerHTML,e.text=d,n.toggleLinkBtn(s,r,a)}).val(e.text),a.on("input paste propertychange",function(){e.text||r.val(a.val()),n.toggleLinkBtn(s,r,a)}).val(e.url),I.isSupportTouch||a.trigger("focus"),n.toggleLinkBtn(s,r,a),n.bindEnterKey(a,s),n.bindEnterKey(r,s),n.onCheckLinkUrl(a);var f=e.isNewWindow!==void 0?e.isNewWindow:n.context.options.linkTargetBlank;c.prop("checked",f),s.one("click",function(d){d.preventDefault(),i.resolve({range:e.range,url:a.val(),text:r.val(),isNewWindow:c.is(":checked")}),n.ui.hideDialog(n.$dialog)})}),n.ui.onDialogHidden(n.$dialog,function(){r.off(),a.off(),s.off(),i.state()==="pending"&&i.reject()}),n.ui.showDialog(n.$dialog)}).promise()}},{key:"show",value:function(){var e=this,n=this.context.invoke("editor.getLinkInfo");this.context.invoke("editor.saveRange"),this.showLinkDialog(n).then(function(i){e.context.invoke("editor.restoreRange"),e.context.invoke("editor.createLink",i)}).fail(function(){e.context.invoke("editor.restoreRange")})}}])}();function _e(o){"@babel/helpers - typeof";return _e=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_e(o)}function oa(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function na(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,ra(n.key),n)}}function ia(o,t,e){return t&&na(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function ra(o){var t=aa(o,"string");return _e(t)=="symbol"?t:t+""}function aa(o,t){if(_e(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(_e(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var sa=function(){function o(t){var e=this;oa(this,o),this.context=t,this.ui=u().summernote.ui,this.options=t.options,this.events={"summernote.keyup summernote.mouseup summernote.change summernote.scroll":function(){e.update()},"summernote.disable summernote.dialog.shown":function(){e.hide()},"summernote.blur":function(i,r){r.originalEvent&&r.originalEvent.relatedTarget&&e.$popover[0].contains(r.originalEvent.relatedTarget)||e.hide()}}}return ia(o,[{key:"shouldInitialize",value:function(){return!v.isEmpty(this.options.popover.link)}},{key:"initialize",value:function(){this.$popover=this.ui.popover({className:"note-link-popover",callback:function(i){var r=i.find(".popover-content,.note-popover-content");r.prepend('<span><a target="_blank"></a>&nbsp;</span>')}}).render().appendTo(this.options.container);var e=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",e,this.options.popover.link),this.$popover.on("mousedown",function(n){n.preventDefault()})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"update",value:function(){if(!this.context.invoke("editor.hasFocus")){this.hide();return}var e=this.context.invoke("editor.getLastRange");if(e.isCollapsed()&&e.isOnAnchor()){var n=l.ancestor(e.sc,l.isAnchor),i=u()(n).attr("href");this.$popover.find("a").attr("href",i).text(i);var r=l.posFromPlaceholder(n),a=u()(this.options.container).offset();r.top-=a.top,r.left-=a.left,this.$popover.css({display:"block",left:r.left,top:r.top})}else this.hide()}},{key:"hide",value:function(){this.$popover.hide()}}])}();function Ae(o){"@babel/helpers - typeof";return Ae=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ae(o)}function la(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function ca(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,fa(n.key),n)}}function ua(o,t,e){return t&&ca(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function fa(o){var t=da(o,"string");return Ae(t)=="symbol"?t:t+""}function da(o,t){if(Ae(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Ae(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var ha=function(){function o(t){la(this,o),this.context=t,this.ui=u().summernote.ui,this.$body=u()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo}return ua(o,[{key:"initialize",value:function(){var e="";if(this.options.maximumImageFileSize){var n=Math.floor(Math.log(this.options.maximumImageFileSize)/Math.log(1024)),i=(this.options.maximumImageFileSize/Math.pow(1024,n)).toFixed(2)*1+" "+" KMGTP"[n]+"B";e="<small>".concat(this.lang.image.maximumFileSize+" : "+i,"</small>")}var r=this.options.dialogsInBody?this.$body:this.options.container,a=['<div class="form-group note-form-group note-group-select-from-files">','<label for="note-dialog-image-file-'+this.options.id+'" class="note-form-label">'+this.lang.image.selectFromFiles+"</label>",'<input id="note-dialog-image-file-'+this.options.id+'" class="note-image-input form-control-file note-form-control note-input" ',' type="file" name="files" accept="'+this.options.acceptImageFileTypes+'" multiple="multiple"/>',e,"</div>",'<div class="form-group note-group-image-url">','<label for="note-dialog-image-url-'+this.options.id+'" class="note-form-label">'+this.lang.image.url+"</label>",'<input id="note-dialog-image-url-'+this.options.id+'" class="note-image-url form-control note-form-control note-input" type="text"/>',"</div>"].join(""),s="btn btn-primary note-btn note-btn-primary note-image-btn",c='<input type="button" href="#" class="'.concat(s,'" value="').concat(this.lang.image.insert,'" disabled>');this.$dialog=this.ui.dialog({title:this.lang.image.insert,fade:this.options.dialogsFade,body:a,footer:c}).render().appendTo(r)}},{key:"destroy",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:"bindEnterKey",value:function(e,n){e.on("keypress",function(i){i.keyCode===P.code.ENTER&&(i.preventDefault(),n.trigger("click"))})}},{key:"show",value:function(){var e=this;this.context.invoke("editor.saveRange"),this.showImageDialog().then(function(n){e.ui.hideDialog(e.$dialog),e.context.invoke("editor.restoreRange"),typeof n=="string"?e.options.callbacks.onImageLinkInsert?e.context.triggerEvent("image.link.insert",n):e.context.invoke("editor.insertImage",n):e.context.invoke("editor.insertImagesOrCallback",n)}).fail(function(){e.context.invoke("editor.restoreRange")})}},{key:"showImageDialog",value:function(){var e=this;return u().Deferred(function(n){var i=e.$dialog.find(".note-image-input"),r=e.$dialog.find(".note-image-url"),a=e.$dialog.find(".note-image-btn");e.ui.onDialogShown(e.$dialog,function(){e.context.triggerEvent("dialog.shown"),i.replaceWith(i.clone().on("change",function(s){n.resolve(s.target.files||s.target.value)}).val("")),r.on("input paste propertychange",function(){e.ui.toggleBtn(a,r.val())}).val(""),I.isSupportTouch||r.trigger("focus"),a.on("click",function(s){s.preventDefault(),n.resolve(r.val())}),e.bindEnterKey(r,a)}),e.ui.onDialogHidden(e.$dialog,function(){i.off(),r.off(),a.off(),n.state()==="pending"&&n.reject()}),e.ui.showDialog(e.$dialog)})}}])}();function Le(o){"@babel/helpers - typeof";return Le=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Le(o)}function pa(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function va(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,ga(n.key),n)}}function ma(o,t,e){return t&&va(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function ga(o){var t=ba(o,"string");return Le(t)=="symbol"?t:t+""}function ba(o,t){if(Le(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Le(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var ya=function(){function o(t){var e=this;pa(this,o),this.context=t,this.ui=u().summernote.ui,this.editable=t.layoutInfo.editable[0],this.options=t.options,this.events={"summernote.disable summernote.dialog.shown":function(){e.hide()},"summernote.blur":function(i,r){r.originalEvent&&r.originalEvent.relatedTarget&&e.$popover[0].contains(r.originalEvent.relatedTarget)||e.hide()}}}return ma(o,[{key:"shouldInitialize",value:function(){return!v.isEmpty(this.options.popover.image)}},{key:"initialize",value:function(){this.$popover=this.ui.popover({className:"note-image-popover"}).render().appendTo(this.options.container);var e=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",e,this.options.popover.image),this.$popover.on("mousedown",function(n){n.preventDefault()})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"update",value:function(e,n){if(l.isImg(e)){var i=u()(e).offset(),r=u()(this.options.container).offset(),a={};this.options.popatmouse?(a.left=n.pageX-20,a.top=n.pageY):a=i,a.top-=r.top,a.left-=r.left,this.$popover.css({display:"block",left:a.left,top:a.top})}else this.hide()}},{key:"hide",value:function(){this.$popover.hide()}}])}();function $e(o){"@babel/helpers - typeof";return $e=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$e(o)}function ka(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function wa(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Sa(n.key),n)}}function Ca(o,t,e){return t&&wa(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Sa(o){var t=Pa(o,"string");return $e(t)=="symbol"?t:t+""}function Pa(o,t){if($e(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if($e(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var xa=function(){function o(t){var e=this;ka(this,o),this.context=t,this.ui=u().summernote.ui,this.options=t.options,this.events={"summernote.mousedown":function(i,r){e.update(r.target)},"summernote.keyup summernote.scroll summernote.change":function(){e.update()},"summernote.disable summernote.dialog.shown":function(){e.hide()},"summernote.blur":function(i,r){r.originalEvent&&r.originalEvent.relatedTarget&&e.$popover[0].contains(r.originalEvent.relatedTarget)||e.hide()}}}return Ca(o,[{key:"shouldInitialize",value:function(){return!v.isEmpty(this.options.popover.table)}},{key:"initialize",value:function(){this.$popover=this.ui.popover({className:"note-table-popover"}).render().appendTo(this.options.container);var e=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",e,this.options.popover.table),I.isFF&&document.execCommand("enableInlineTableEditing",!1,!1),this.$popover.on("mousedown",function(n){n.preventDefault()})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"update",value:function(e){if(this.context.isDisabled())return!1;var n=l.isCell(e)||l.isCell(e==null?void 0:e.parentElement);if(n){var i=l.posFromPlaceholder(e),r=u()(this.options.container).offset();i.top-=r.top,i.left-=r.left,this.$popover.css({display:"block",left:i.left,top:i.top})}else this.hide();return n}},{key:"hide",value:function(){this.$popover.hide()}}])}();function De(o){"@babel/helpers - typeof";return De=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},De(o)}function Ta(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Ea(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,_a(n.key),n)}}function Ia(o,t,e){return t&&Ea(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function _a(o){var t=Aa(o,"string");return De(t)=="symbol"?t:t+""}function Aa(o,t){if(De(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(De(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var La=function(){function o(t){Ta(this,o),this.context=t,this.ui=u().summernote.ui,this.$body=u()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo}return Ia(o,[{key:"initialize",value:function(){var e=this.options.dialogsInBody?this.$body:this.options.container,n=['<div class="form-group note-form-group row-fluid">','<label for="note-dialog-video-url-'.concat(this.options.id,'" class="note-form-label">').concat(this.lang.video.url,' <small class="text-muted">').concat(this.lang.video.providers,"</small></label>"),'<input id="note-dialog-video-url-'.concat(this.options.id,'" class="note-video-url form-control note-form-control note-input" type="text"/>'),"</div>"].join(""),i="btn btn-primary note-btn note-btn-primary note-video-btn",r='<input type="button" href="#" class="'.concat(i,'" value="').concat(this.lang.video.insert,'" disabled>');this.$dialog=this.ui.dialog({title:this.lang.video.insert,fade:this.options.dialogsFade,body:n,footer:r}).render().appendTo(e)}},{key:"destroy",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:"bindEnterKey",value:function(e,n){e.on("keypress",function(i){i.keyCode===P.code.ENTER&&(i.preventDefault(),n.trigger("click"))})}},{key:"createVideoNode",value:function(e){var n=/(?:youtu\.be\/|youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=|shorts\/|live\/))([^&\n?]+)(?:.*[?&]t=([^&\n]+))?.*/,i=/^(?:(\d+)h)?(?:(\d+)m)?(?:(\d+)s)?$/,r=e.match(n),a=/(?:\.|\/\/)drive\.google\.com\/file\/d\/(.[a-zA-Z0-9_-]*)\/view/,s=e.match(a),c=/(?:www\.|\/\/)instagram\.com\/(reel|p)\/(.[a-zA-Z0-9_-]*)/,f=e.match(c),d=/\/\/vine\.co\/v\/([a-zA-Z0-9]+)/,h=e.match(d),p=/\/\/(player\.)?vimeo\.com\/([a-z]*\/)*(\d+)[?]?.*/,m=e.match(p),y=/.+dailymotion.com\/(video|hub)\/([^_]+)[^#]*(#video=([^_&]+))?/,w=e.match(y),T=/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/,A=e.match(T),b=/\/\/(.*)\/videos\/watch\/([^?]*)(?:\?(?:start=(\w*))?(?:&stop=(\w*))?(?:&loop=([10]))?(?:&autoplay=([10]))?(?:&muted=([10]))?)?/,g=e.match(b),C=/\/\/v\.qq\.com.*?vid=(.+)/,E=e.match(C),j=/\/\/v\.qq\.com\/x?\/?(page|cover).*?\/([^\/]+)\.html\??.*/,z=e.match(j),U=/^.+.(mp4|m4v)$/,F=e.match(U),Z=/^.+.(ogg|ogv)$/,Qe=e.match(Z),Oe=/^.+.(webm)$/,Je=e.match(Oe),Bs=/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/,yt=e.match(Bs),N;if(r&&r[1].length===11){var Hs=r[1],je=0;if(typeof r[2]<"u"){var kt=r[2].match(i);if(kt)for(var oo=[3600,60,1],Ue=0,zs=oo.length;Ue<zs;Ue++)je+=typeof kt[Ue+1]<"u"?oo[Ue]*parseInt(kt[Ue+1],10):0;else je=parseInt(r[2],10)}N=u()("<iframe>").attr("frameborder",0).attr("src","//www.youtube.com/embed/"+Hs+(je>0?"?start="+je:"")).attr("width","640").attr("height","360")}else if(s&&s[0].length)N=u()("<iframe>").attr("frameborder",0).attr("src","https://drive.google.com/file/d/"+s[1]+"/preview").attr("width","640").attr("height","480");else if(f&&f[0].length)N=u()("<iframe>").attr("frameborder",0).attr("src","https://instagram.com/p/"+f[2]+"/embed/").attr("width","612").attr("height","710").attr("scrolling","no").attr("allowtransparency","true");else if(h&&h[0].length)N=u()("<iframe>").attr("frameborder",0).attr("src",h[0]+"/embed/simple").attr("width","600").attr("height","600").attr("class","vine-embed");else if(m&&m[3].length)N=u()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("src","//player.vimeo.com/video/"+m[3]).attr("width","640").attr("height","360");else if(w&&w[2].length)N=u()("<iframe>").attr("frameborder",0).attr("src","//www.dailymotion.com/embed/video/"+w[2]).attr("width","640").attr("height","360");else if(A&&A[1].length)N=u()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("height","498").attr("width","510").attr("src","//player.youku.com/embed/"+A[1]);else if(g&&g[0].length){var wt=0;g[2]!=="undefined"&&(wt=g[2]);var no=0;g[3]!=="undefined"&&(no=g[3]);var io=0;g[4]!=="undefined"&&(io=g[4]);var ro=0;g[5]!=="undefined"&&(ro=g[5]);var ao=0;g[6]!=="undefined"&&(ao=g[6]),N=u()('<iframe allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups">').attr("frameborder",0).attr("src","//"+g[1]+"/videos/embed/"+g[2]+"?loop="+io+"&autoplay="+ro+"&muted="+ao+(wt>0?"&start="+wt:"")+(no>0?"&end="+je:"")).attr("width","560").attr("height","315")}else if(E&&E[1].length||z&&z[2].length){var Ms=E&&E[1].length?E[1]:z[2];N=u()("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("height","310").attr("width","500").attr("src","https://v.qq.com/txp/iframe/player.html?vid="+Ms+"&amp;auto=0")}else if(F||Qe||Je)N=u()("<video controls>").attr("src",e).attr("width","640").attr("height","360");else if(yt&&yt[0].length)N=u()("<iframe>").attr("frameborder",0).attr("src","https://www.facebook.com/plugins/video.php?href="+encodeURIComponent(yt[0])+"&show_text=0&width=560").attr("width","560").attr("height","301").attr("scrolling","no").attr("allowtransparency","true");else return!1;return N.addClass("note-video-clip"),N[0]}},{key:"show",value:function(){var e=this,n=this.context.invoke("editor.getSelectedText");this.context.invoke("editor.saveRange"),this.showVideoDialog(n).then(function(i){e.ui.hideDialog(e.$dialog),e.context.invoke("editor.restoreRange");var r=e.createVideoNode(i);r&&e.context.invoke("editor.insertNode",r)}).fail(function(){e.context.invoke("editor.restoreRange")})}},{key:"showVideoDialog",value:function(){var e=this;return u().Deferred(function(n){var i=e.$dialog.find(".note-video-url"),r=e.$dialog.find(".note-video-btn");e.ui.onDialogShown(e.$dialog,function(){e.context.triggerEvent("dialog.shown"),i.on("input paste propertychange",function(){e.ui.toggleBtn(r,i.val())}),I.isSupportTouch||i.trigger("focus"),r.on("click",function(a){a.preventDefault(),n.resolve(i.val())}),e.bindEnterKey(i,r)}),e.ui.onDialogHidden(e.$dialog,function(){i.off(),r.off(),n.state()==="pending"&&n.reject()}),e.ui.showDialog(e.$dialog)})}}])}();function Ne(o){"@babel/helpers - typeof";return Ne=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ne(o)}function $a(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Da(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Ra(n.key),n)}}function Na(o,t,e){return t&&Da(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Ra(o){var t=Fa(o,"string");return Ne(t)=="symbol"?t:t+""}function Fa(o,t){if(Ne(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Ne(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Ba=function(){function o(t){$a(this,o),this.context=t,this.ui=u().summernote.ui,this.$body=u()(document.body),this.$editor=t.layoutInfo.editor,this.options=t.options,this.lang=this.options.langInfo}return Na(o,[{key:"initialize",value:function(){var e=this.options.dialogsInBody?this.$body:this.options.container,n=['<p class="text-center">','<a href="http://summernote.org/" target="_blank" rel="noopener noreferrer">Summernote 0.9.1</a> · ','<a href="https://github.com/summernote/summernote" target="_blank" rel="noopener noreferrer">Project</a> · ','<a href="https://github.com/summernote/summernote/issues" target="_blank" rel="noopener noreferrer">Issues</a>',"</p>"].join("");this.$dialog=this.ui.dialog({title:this.lang.options.help,fade:this.options.dialogsFade,body:this.createShortcutList(),footer:n,callback:function(r){r.find(".modal-body,.note-modal-body").css({"max-height":300,overflow:"scroll"})}}).render().appendTo(e)}},{key:"destroy",value:function(){this.ui.hideDialog(this.$dialog),this.$dialog.remove()}},{key:"createShortcutList",value:function(){var e=this,n=this.options.keyMap[I.isMac?"mac":"pc"];return Object.keys(n).map(function(i){var r=n[i],a=u()('<div><div class="help-list-item"></div></div>');return a.append(u()("<label><kbd>"+i+"</kdb></label>").css({width:180,"margin-right":10})).append(u()("<span></span>").html(e.context.memo("help."+r)||r)),a.html()}).join("")}},{key:"showHelpDialog",value:function(){var e=this;return u().Deferred(function(n){e.ui.onDialogShown(e.$dialog,function(){e.context.triggerEvent("dialog.shown"),n.resolve()}),e.ui.showDialog(e.$dialog)}).promise()}},{key:"show",value:function(){var e=this;this.context.invoke("editor.saveRange"),this.showHelpDialog().then(function(){e.context.invoke("editor.restoreRange")})}}])}();function Re(o){"@babel/helpers - typeof";return Re=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Re(o)}function Ha(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function za(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Oa(n.key),n)}}function Ma(o,t,e){return t&&za(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Oa(o){var t=ja(o,"string");return Re(t)=="symbol"?t:t+""}function ja(o,t){if(Re(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Re(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Ua=-5,Ka=5,Wa=function(){function o(t){var e=this;Ha(this,o),this.context=t,this.ui=u().summernote.ui,this.options=t.options,this.hidable=!0,this.onContextmenu=!1,this.pageX=null,this.pageY=null,this.events={"summernote.contextmenu":function(i){e.options.editing&&(i.preventDefault(),i.stopPropagation(),e.onContextmenu=!0,e.update(!0))},"summernote.mousedown":function(i,r){e.pageX=r.pageX,e.pageY=r.pageY},"summernote.keyup summernote.mouseup summernote.scroll":function(i,r){if(e.options.editing&&!e.onContextmenu){if(r.type=="keyup"){var a=e.context.invoke("editor.getLastRange"),s=a.getWordRange(),c=k.rect2bnd(v.last(s.getClientRects()));e.pageX=c.left,e.pageY=c.top}else e.pageX=r.pageX,e.pageY=r.pageY;e.update()}e.onContextmenu=!1},"summernote.disable summernote.change summernote.dialog.shown summernote.blur":function(){e.hide()},"summernote.focusout":function(){e.$popover.is(":active,:focus")||e.hide()}}}return Ma(o,[{key:"shouldInitialize",value:function(){return this.options.airMode&&!v.isEmpty(this.options.popover.air)}},{key:"initialize",value:function(){var e=this;this.$popover=this.ui.popover({className:"note-air-popover"}).render().appendTo(this.options.container);var n=this.$popover.find(".popover-content");this.context.invoke("buttons.build",n,this.options.popover.air),this.$popover.on("mousedown",function(){e.hidable=!1}),this.$popover.on("mouseup",function(){e.hidable=!0})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"update",value:function(e){var n=this.context.invoke("editor.currentStyle");if(n.range&&(!n.range.isCollapsed()||e)){var i={left:this.pageX,top:this.pageY},r=u()(this.options.container).offset();i.top-=r.top,i.left-=r.left,this.$popover.css({display:"block",left:Math.max(i.left,0)+Ua,top:i.top+Ka}),this.context.invoke("buttons.updateCurrentStyle",this.$popover)}else this.hide()}},{key:"updateCodeview",value:function(e){this.ui.toggleBtnActive(this.$popover.find(".btn-codeview"),e),e&&this.hide()}},{key:"hide",value:function(){this.hidable&&this.$popover.hide()}}])}();function Fe(o){"@babel/helpers - typeof";return Fe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fe(o)}function Va(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function qa(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,Za(n.key),n)}}function Ga(o,t,e){return t&&qa(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function Za(o){var t=Ya(o,"string");return Fe(t)=="symbol"?t:t+""}function Ya(o,t){if(Fe(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Fe(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Yt=5,Xa=function(){function o(t){var e=this;Va(this,o),this.context=t,this.ui=u().summernote.ui,this.$editable=t.layoutInfo.editable,this.options=t.options,this.hint=this.options.hint||[],this.direction=this.options.hintDirection||"bottom",this.hints=Array.isArray(this.hint)?this.hint:[this.hint],this.events={"summernote.keyup":function(i,r){r.isDefaultPrevented()||e.handleKeyup(r)},"summernote.keydown":function(i,r){e.handleKeydown(r)},"summernote.disable summernote.dialog.shown summernote.blur":function(){e.hide()}}}return Ga(o,[{key:"shouldInitialize",value:function(){return this.hints.length>0}},{key:"initialize",value:function(){var e=this;this.lastWordRange=null,this.matchingWord=null,this.$popover=this.ui.popover({className:"note-hint-popover",hideArrow:!0,direction:""}).render().appendTo(this.options.container),this.$popover.hide(),this.$content=this.$popover.find(".popover-content,.note-popover-content"),this.$content.on("click",".note-hint-item",function(n){e.$content.find(".active").removeClass("active"),u()(n.currentTarget).addClass("active"),e.replace()}),this.$popover.on("mousedown",function(n){n.preventDefault()})}},{key:"destroy",value:function(){this.$popover.remove()}},{key:"selectItem",value:function(e){this.$content.find(".active").removeClass("active"),e.addClass("active"),this.$content[0].scrollTop=e[0].offsetTop-this.$content.innerHeight()/2}},{key:"moveDown",value:function(){var e=this.$content.find(".note-hint-item.active"),n=e.next();if(n.length)this.selectItem(n);else{var i=e.parent().next();i.length||(i=this.$content.find(".note-hint-group").first()),this.selectItem(i.find(".note-hint-item").first())}}},{key:"moveUp",value:function(){var e=this.$content.find(".note-hint-item.active"),n=e.prev();if(n.length)this.selectItem(n);else{var i=e.parent().prev();i.length||(i=this.$content.find(".note-hint-group").last()),this.selectItem(i.find(".note-hint-item").last())}}},{key:"replace",value:function(){var e=this.$content.find(".note-hint-item.active");if(e.length){var n=this.nodeFromItem(e);if(this.matchingWord!==null&&this.matchingWord.length===0)this.lastWordRange.so=this.lastWordRange.eo;else if(this.matchingWord!==null&&this.matchingWord.length>0&&!this.lastWordRange.isCollapsed()){var i=this.lastWordRange.eo-this.lastWordRange.so-this.matchingWord.length;i>0&&(this.lastWordRange.so+=i)}if(this.lastWordRange.insertNode(n),this.options.hintSelect==="next"){var r=document.createTextNode("");u()(n).after(r),S.createFromNodeBefore(r).select()}else S.createFromNodeAfter(n).select();this.lastWordRange=null,this.hide(),this.context.invoke("editor.focus"),this.context.triggerEvent("change",this.$editable.html(),this.$editable)}}},{key:"nodeFromItem",value:function(e){var n=this.hints[e.data("index")],i=e.data("item"),r=n.content?n.content(i):i;return typeof r=="string"&&(r=l.createText(r)),r}},{key:"createItemTemplates",value:function(e,n){var i=this.hints[e];return n.map(function(r,a){var s=u()('<div class="note-hint-item"></div>');return s.append(i.template?i.template(r):r+""),s.data({index:e,item:r}),e===0&&a===0&&s.addClass("active"),s})}},{key:"handleKeydown",value:function(e){this.$popover.is(":visible")&&(e.keyCode===P.code.ENTER?(e.preventDefault(),this.replace()):e.keyCode===P.code.UP?(e.preventDefault(),this.moveUp()):e.keyCode===P.code.DOWN&&(e.preventDefault(),this.moveDown()))}},{key:"searchKeyword",value:function(e,n,i){var r=this.hints[e];if(r&&r.match.test(n)&&r.search){var a=r.match.exec(n);this.matchingWord=a[0],r.search(a[1],i)}else i()}},{key:"createGroup",value:function(e,n){var i=this,r=u()('<div class="note-hint-group note-hint-group-'+e+'"></div>');return this.searchKeyword(e,n,function(a){a=a||[],a.length&&(r.html(i.createItemTemplates(e,a)),i.show())}),r}},{key:"handleKeyup",value:function(e){var n=this;if(!v.contains([P.code.ENTER,P.code.UP,P.code.DOWN],e.keyCode)){var i=this.context.invoke("editor.getLastRange"),r,a;if(this.options.hintMode==="words"){if(r=i.getWordsRange(i),a=r.toString(),this.hints.forEach(function(f){if(f.match.test(a))return r=i.getWordsMatchRange(f.match),!1}),!r){this.hide();return}a=r.toString()}else r=i.getWordRange(),a=r.toString();if(this.hints.length&&a){this.$content.empty();var s=k.rect2bnd(v.last(r.getClientRects())),c=u()(this.options.container).offset();s&&(s.top-=c.top,s.left-=c.left,this.$popover.hide(),this.lastWordRange=r,this.hints.forEach(function(f,d){f.match.test(a)&&n.createGroup(d,a).appendTo(n.$content)}),this.$content.find(".note-hint-item").first().addClass("active"),this.direction==="top"?this.$popover.css({left:s.left,top:s.top-this.$popover.outerHeight()-Yt}):this.$popover.css({left:s.left,top:s.top+s.height+Yt}))}else this.hide()}}},{key:"show",value:function(){this.$popover.show()}},{key:"hide",value:function(){this.$popover.hide()}}])}();u().summernote=u().extend(u().summernote,{version:"0.9.1",plugins:{},dom:l,range:S,lists:v,options:{langInfo:u().summernote.lang["en-US"],editing:!0,modules:{editor:Pi,clipboard:Ai,dropzone:Fi,codeview:Ki,statusbar:Yi,fullscreen:or,handle:lr,hintPopover:Xa,autoLink:mr,autoSync:Cr,autoReplace:Ir,placeholder:Nr,buttons:Mr,toolbar:Vr,linkDialog:ta,linkPopover:sa,imageDialog:ha,imagePopover:ya,tablePopover:xa,videoDialog:La,helpDialog:Ba,airPopover:Wa},buttons:{},lang:"en-US",followingToolbar:!1,toolbarPosition:"top",otherStaticBar:"",codeviewKeepButton:!1,toolbar:[["style",["style"]],["font",["bold","underline","clear"]],["fontname",["fontname"]],["color",["color"]],["para",["ul","ol","paragraph"]],["table",["table"]],["insert",["link","picture","video"]],["view",["fullscreen","codeview","help"]]],popatmouse:!0,popover:{image:[["resize",["resizeFull","resizeHalf","resizeQuarter","resizeNone"]],["float",["floatLeft","floatRight","floatNone"]],["remove",["removeMedia"]]],link:[["link",["linkDialogShow","unlink"]]],table:[["add",["addRowDown","addRowUp","addColLeft","addColRight"]],["delete",["deleteRow","deleteCol","deleteTable"]]],air:[["color",["color"]],["font",["bold","underline","clear"]],["para",["ul","paragraph"]],["table",["table"]],["insert",["link","picture"]],["view",["fullscreen","codeview"]]]},linkAddNoReferrer:!1,addLinkNoOpener:!1,airMode:!1,overrideContextMenu:!1,width:null,height:null,linkTargetBlank:!0,focus:!1,tabDisable:!1,tabSize:4,styleWithCSS:!1,shortcuts:!0,textareaAutoSync:!0,tooltip:"auto",container:null,maxTextLength:0,blockquoteBreakingLevel:2,spellCheck:!0,disableGrammar:!1,placeholder:null,inheritPlaceholder:!1,recordEveryKeystroke:!1,historyLimit:200,showDomainOnlyForAutolink:!1,hintMode:"word",hintSelect:"after",hintDirection:"bottom",styleTags:["p","blockquote","pre","h1","h2","h3","h4","h5","h6"],fontNames:["Arial","Arial Black","Comic Sans MS","Courier New","Helvetica Neue","Helvetica","Impact","Lucida Grande","Tahoma","Times New Roman","Verdana"],fontNamesIgnoreCheck:[],addDefaultFonts:!0,fontSizes:["8","9","10","11","12","14","18","24","36"],fontSizeUnits:["px","pt"],colors:[["#000000","#424242","#636363","#9C9C94","#CEC6CE","#EFEFEF","#F7F7F7","#FFFFFF"],["#FF0000","#FF9C00","#FFFF00","#00FF00","#00FFFF","#0000FF","#9C00FF","#FF00FF"],["#F7C6CE","#FFE7CE","#FFEFC6","#D6EFD6","#CEDEE7","#CEE7F7","#D6D6E7","#E7D6DE"],["#E79C9C","#FFC69C","#FFE79C","#B5D6A5","#A5C6CE","#9CC6EF","#B5A5D6","#D6A5BD"],["#E76363","#F7AD6B","#FFD663","#94BD7B","#73A5AD","#6BADDE","#8C7BC6","#C67BA5"],["#CE0000","#E79439","#EFC631","#6BA54A","#4A7B8C","#3984C6","#634AA5","#A54A7B"],["#9C0000","#B56308","#BD9400","#397B21","#104A5A","#085294","#311873","#731842"],["#630000","#7B3900","#846300","#295218","#083139","#003163","#21104A","#4A1031"]],colorsName:[["Black","Tundora","Dove Gray","Star Dust","Pale Slate","Gallery","Alabaster","White"],["Red","Orange Peel","Yellow","Green","Cyan","Blue","Electric Violet","Magenta"],["Azalea","Karry","Egg White","Zanah","Botticelli","Tropical Blue","Mischka","Twilight"],["Tonys Pink","Peach Orange","Cream Brulee","Sprout","Casper","Perano","Cold Purple","Careys Pink"],["Mandy","Rajah","Dandelion","Olivine","Gulf Stream","Viking","Blue Marguerite","Puce"],["Guardsman Red","Fire Bush","Golden Dream","Chelsea Cucumber","Smalt Blue","Boston Blue","Butterfly Bush","Cadillac"],["Sangria","Mai Tai","Buddha Gold","Forest Green","Eden","Venice Blue","Meteorite","Claret"],["Rosewood","Cinnamon","Olive","Parsley","Tiber","Midnight Blue","Valentino","Loulou"]],colorButton:{foreColor:"#000000",backColor:"#FFFF00"},lineHeights:["1.0","1.2","1.4","1.5","1.6","1.8","2.0","3.0"],tableClassName:"table table-bordered",insertTableMaxSize:{col:10,row:10},dialogsInBody:!1,dialogsFade:!1,maximumImageFileSize:null,acceptImageFileTypes:"image/*",allowClipboardImagePasting:!0,callbacks:{onBeforeCommand:null,onBlur:null,onBlurCodeview:null,onChange:null,onChangeCodeview:null,onDialogShown:null,onEnter:null,onFocus:null,onImageLinkInsert:null,onImageUpload:null,onImageUploadError:null,onInit:null,onKeydown:null,onKeyup:null,onMousedown:null,onMouseup:null,onPaste:null,onScroll:null},codemirror:{mode:"text/html",htmlMode:!0,lineNumbers:!0},codeviewFilter:!0,codeviewFilterRegex:/<\/*(?:applet|b(?:ase|gsound|link)|embed|frame(?:set)?|ilayer|l(?:ayer|ink)|meta|object|s(?:cript|tyle)|t(?:itle|extarea)|xml)[^>]*?>/gi,codeviewIframeFilter:!0,codeviewIframeWhitelistSrc:[],codeviewIframeWhitelistSrcBase:["www.youtube.com","www.youtube-nocookie.com","www.facebook.com","vine.co","instagram.com","player.vimeo.com","www.dailymotion.com","player.youku.com","jumpingbean.tv","v.qq.com"],keyMap:{pc:{ESC:"escape",ENTER:"insertParagraph","CTRL+Z":"undo","CTRL+Y":"redo",TAB:"tab","SHIFT+TAB":"untab","CTRL+B":"bold","CTRL+I":"italic","CTRL+U":"underline","CTRL+SHIFT+S":"strikethrough","CTRL+BACKSLASH":"removeFormat","CTRL+SHIFT+L":"justifyLeft","CTRL+SHIFT+E":"justifyCenter","CTRL+SHIFT+R":"justifyRight","CTRL+SHIFT+J":"justifyFull","CTRL+SHIFT+NUM7":"insertUnorderedList","CTRL+SHIFT+NUM8":"insertOrderedList","CTRL+LEFTBRACKET":"outdent","CTRL+RIGHTBRACKET":"indent","CTRL+NUM0":"formatPara","CTRL+NUM1":"formatH1","CTRL+NUM2":"formatH2","CTRL+NUM3":"formatH3","CTRL+NUM4":"formatH4","CTRL+NUM5":"formatH5","CTRL+NUM6":"formatH6","CTRL+ENTER":"insertHorizontalRule","CTRL+K":"linkDialog.show"},mac:{ESC:"escape",ENTER:"insertParagraph","CMD+Z":"undo","CMD+SHIFT+Z":"redo",TAB:"tab","SHIFT+TAB":"untab","CMD+B":"bold","CMD+I":"italic","CMD+U":"underline","CMD+SHIFT+S":"strikethrough","CMD+BACKSLASH":"removeFormat","CMD+SHIFT+L":"justifyLeft","CMD+SHIFT+E":"justifyCenter","CMD+SHIFT+R":"justifyRight","CMD+SHIFT+J":"justifyFull","CMD+SHIFT+NUM7":"insertUnorderedList","CMD+SHIFT+NUM8":"insertOrderedList","CMD+LEFTBRACKET":"outdent","CMD+RIGHTBRACKET":"indent","CMD+NUM0":"formatPara","CMD+NUM1":"formatH1","CMD+NUM2":"formatH2","CMD+NUM3":"formatH3","CMD+NUM4":"formatH4","CMD+NUM5":"formatH5","CMD+NUM6":"formatH6","CMD+ENTER":"insertHorizontalRule","CMD+K":"linkDialog.show"}},icons:{align:"note-icon-align",alignCenter:"note-icon-align-center",alignJustify:"note-icon-align-justify",alignLeft:"note-icon-align-left",alignRight:"note-icon-align-right",rowBelow:"note-icon-row-below",colBefore:"note-icon-col-before",colAfter:"note-icon-col-after",rowAbove:"note-icon-row-above",rowRemove:"note-icon-row-remove",colRemove:"note-icon-col-remove",indent:"note-icon-align-indent",outdent:"note-icon-align-outdent",arrowsAlt:"note-icon-arrows-alt",bold:"note-icon-bold",caret:"note-icon-caret",circle:"note-icon-circle",close:"note-icon-close",code:"note-icon-code",eraser:"note-icon-eraser",floatLeft:"note-icon-float-left",floatRight:"note-icon-float-right",font:"note-icon-font",frame:"note-icon-frame",italic:"note-icon-italic",link:"note-icon-link",unlink:"note-icon-chain-broken",magic:"note-icon-magic",menuCheck:"note-icon-menu-check",minus:"note-icon-minus",orderedlist:"note-icon-orderedlist",pencil:"note-icon-pencil",picture:"note-icon-picture",question:"note-icon-question",redo:"note-icon-redo",rollback:"note-icon-rollback",square:"note-icon-square",strikethrough:"note-icon-strikethrough",subscript:"note-icon-subscript",superscript:"note-icon-superscript",table:"note-icon-table",textHeight:"note-icon-text-height",trash:"note-icon-trash",underline:"note-icon-underline",undo:"note-icon-undo",unorderedlist:"note-icon-unorderedlist",video:"note-icon-video"}}});function ee(o){"@babel/helpers - typeof";return ee=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ee(o)}function Qa(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function Ja(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,ts(n.key),n)}}function es(o,t,e){return t&&Ja(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function ts(o){var t=os(o,"string");return ee(t)=="symbol"?t:t+""}function os(o,t){if(ee(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(ee(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var ns=function(){function o(t,e,n,i){Qa(this,o),this.markup=t,this.children=e,this.options=n,this.callback=i}return es(o,[{key:"render",value:function(e){var n=u()(this.markup);if(this.options&&this.options.contents&&n.html(this.options.contents),this.options&&this.options.className&&n.addClass(this.options.className),this.options&&this.options.data&&u().each(this.options.data,function(r,a){n.attr("data-"+r,a)}),this.options&&this.options.click&&n.on("click",this.options.click),this.children){var i=n.find(".note-children-container");this.children.forEach(function(r){r.render(i.length?i:n)})}return this.callback&&this.callback(n,this.options),this.options&&this.options.callback&&this.options.callback(n),e&&e.append(n),n}}])}();const $={create:function(t,e){return function(){var n=ee(arguments[1])==="object"?arguments[1]:arguments[0],i=Array.isArray(arguments[0])?arguments[0]:[];return n&&n.children&&(i=n.children),new ns(t,i,n,e)}}};function Be(o){"@babel/helpers - typeof";return Be=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Be(o)}function is(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function rs(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,ss(n.key),n)}}function as(o,t,e){return t&&rs(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function ss(o){var t=ls(o,"string");return Be(t)=="symbol"?t:t+""}function ls(o,t){if(Be(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(Be(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var cs=function(){function o(t,e){if(is(this,o),this.$node=t,this.options=u().extend({},{title:"",target:e.container,trigger:"hover focus",placement:"bottom"},e),this.$tooltip=u()(['<div class="note-tooltip">','<div class="note-tooltip-arrow"></div>','<div class="note-tooltip-content"></div>',"</div>"].join("")),this.options.trigger!=="manual"){var n=this.show.bind(this),i=this.hide.bind(this),r=this.toggle.bind(this);this.options.trigger.split(" ").forEach(function(a){a==="hover"?(t.off("mouseenter mouseleave"),t.on("mouseenter",n).on("mouseleave",i)):a==="click"?t.on("click",r):a==="focus"&&t.on("focus",n).on("blur",i)})}}return as(o,[{key:"show",value:function(){var e=this.$node,n=e.offset(),i=u()(this.options.target).offset();n.top-=i.top,n.left-=i.left;var r=this.$tooltip,a=this.options.title||e.attr("title")||e.data("title"),s=this.options.placement||e.data("placement");r.addClass(s),r.find(".note-tooltip-content").text(a),r.appendTo(this.options.target);var c=e.outerWidth(),f=e.outerHeight(),d=r.outerWidth(),h=r.outerHeight();s==="bottom"?r.css({top:n.top+f,left:n.left+(c/2-d/2)}):s==="top"?r.css({top:n.top-h,left:n.left+(c/2-d/2)}):s==="left"?r.css({top:n.top+(f/2-h/2),left:n.left-d}):s==="right"&&r.css({top:n.top+(f/2-h/2),left:n.left+c}),r.addClass("in")}},{key:"hide",value:function(){var e=this;this.$tooltip.removeClass("in"),setTimeout(function(){e.$tooltip.remove()},200)}},{key:"toggle",value:function(){this.$tooltip.hasClass("in")?this.hide():this.show()}}])}();const Xt=cs;function He(o){"@babel/helpers - typeof";return He=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},He(o)}function us(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function fs(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,hs(n.key),n)}}function ds(o,t,e){return t&&fs(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function hs(o){var t=ps(o,"string");return He(t)=="symbol"?t:t+""}function ps(o,t){if(He(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(He(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var vs=function(){function o(t,e){us(this,o),this.$button=t,this.options=u().extend({},{target:e.container},e),this.setEvent()}return ds(o,[{key:"setEvent",value:function(){var e=this;this.$button.on("click",function(n){e.toggle(),n.stopImmediatePropagation()})}},{key:"clear",value:function(){var e=u()(".note-btn-group.open");e.find(".note-btn.active").removeClass("active"),e.removeClass("open")}},{key:"show",value:function(){this.$button.addClass("active"),this.$button.parent().addClass("open");var e=this.$button.next(),n=e.offset(),i=e.outerWidth(),r=u()(window).width(),a=parseFloat(u()(this.options.target).css("margin-right"));n.left+i>r-a?e.css("margin-left",r-a-(n.left+i)):e.css("margin-left","")}},{key:"hide",value:function(){this.$button.removeClass("active"),this.$button.parent().removeClass("open")}},{key:"toggle",value:function(){var e=this.$button.parent().hasClass("open");this.clear(),e?this.hide():this.show()}}])}();u()(document).on("click.note-dropdown-menu",function(o){u()(o.target).closest(".note-btn-group").length||(u()(".note-btn-group.open .note-btn.active").removeClass("active"),u()(".note-btn-group.open").removeClass("open"))}),u()(document).on("click.note-dropdown-menu",function(o){u()(o.target).closest(".note-dropdown-menu").parent().removeClass("open"),u()(o.target).closest(".note-dropdown-menu").parent().find(".note-btn.active").removeClass("active")});const ms=vs;function ze(o){"@babel/helpers - typeof";return ze=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ze(o)}function gs(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function bs(o,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(o,ks(n.key),n)}}function ys(o,t,e){return t&&bs(o.prototype,t),Object.defineProperty(o,"prototype",{writable:!1}),o}function ks(o){var t=ws(o,"string");return ze(t)=="symbol"?t:t+""}function ws(o,t){if(ze(o)!="object"||!o)return o;var e=o[Symbol.toPrimitive];if(e!==void 0){var n=e.call(o,t);if(ze(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var Cs=function(){function o(t){gs(this,o),this.$modal=t,this.$backdrop=u()('<div class="note-modal-backdrop"></div>')}return ys(o,[{key:"show",value:function(){var e=this;this.$backdrop.appendTo(document.body).show(),this.$modal.addClass("open").show(),this.$modal.trigger("note.modal.show"),this.$modal.off("click",".close").on("click",".close",this.hide.bind(this)),this.$modal.on("keydown",function(n){n.which===27&&(n.preventDefault(),e.hide())})}},{key:"hide",value:function(){this.$modal.removeClass("open").hide(),this.$backdrop.hide(),this.$modal.trigger("note.modal.hide"),this.$modal.off("keydown")}}])}();const Ss=Cs;var vt=$.create('<div class="note-editor note-frame"></div>'),mt=$.create('<div class="note-toolbar" role="toolbar"></div>'),Ze=$.create('<div class="note-editing-area"></div>'),Ye=$.create('<textarea class="note-codable" aria-multiline="true"></textarea>'),gt=$.create('<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"></div>'),bt=$.create(['<output class="note-status-output" role="status" aria-live="polite"></output>','<div class="note-statusbar" role="status">','<div class="note-resizebar" aria-label="resize">','<div class="note-icon-bar"></div>','<div class="note-icon-bar"></div>','<div class="note-icon-bar"></div>',"</div>","</div>"].join("")),Qt=$.create('<div class="note-editor note-airframe"></div>'),Jt=$.create(['<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"></div>','<output class="note-status-output" role="status" aria-live="polite"></output>'].join("")),q=$.create('<div class="note-btn-group"></div>'),Y=$.create('<button type="button" class="note-btn" tabindex="-1"></button>',function(o,t){t&&t.tooltip&&(o.attr({"aria-label":t.tooltip}),o.data("_lite_tooltip",new Xt(o,{title:t.tooltip,container:t.container})).on("click",function(e){u()(e.currentTarget).data("_lite_tooltip").hide()})),t.contents&&o.html(t.contents),t&&t.data&&t.data.toggle==="dropdown"&&o.data("_lite_dropdown",new ms(o,{container:t.container})),t&&t.codeviewKeepButton&&o.addClass("note-codeview-keep")}),Me=$.create('<div class="note-dropdown-menu" role="list"></div>',function(o,t){var e=Array.isArray(t.items)?t.items.map(function(n){var i=typeof n=="string"?n:n.value||"",r=t.template?t.template(n):n,a=u()('<a class="note-dropdown-item" href="#" data-value="'+i+'" role="listitem" aria-label="'+i+'"></a>');return a.html(r).data("item",n),a}):t.items;o.html(e).attr({"aria-label":t.title}),o.on("click","> .note-dropdown-item",function(n){var i=u()(this),r=i.data("item"),a=i.data("value");r.click?r.click(i):t.itemClick&&t.itemClick(n,r,a)}),t&&t.codeviewKeepButton&&o.addClass("note-codeview-keep")}),eo=$.create('<div class="note-dropdown-menu note-check" role="list"></div>',function(o,t){var e=Array.isArray(t.items)?t.items.map(function(n){var i=typeof n=="string"?n:n.value||"",r=t.template?t.template(n):n,a=u()('<a class="note-dropdown-item" href="#" data-value="'+i+'" role="listitem" aria-label="'+n+'"></a>');return a.html([G(t.checkClassName)," ",r]).data("item",n),a}):t.items;o.html(e).attr({"aria-label":t.title}),o.on("click","> .note-dropdown-item",function(n){var i=u()(this),r=i.data("item"),a=i.data("value");r.click?r.click(i):t.itemClick&&t.itemClick(n,r,a)}),t&&t.codeviewKeepButton&&o.addClass("note-codeview-keep")}),Ps=function(t,e){return t+" "+G(e.icons.caret,"span")},xs=function(t,e){return q([Y({className:"dropdown-toggle",contents:t.title+" "+G("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),Me({className:t.className,items:t.items,template:t.template,itemClick:t.itemClick})],{callback:e}).render()},Ts=function(t,e){return q([Y({className:"dropdown-toggle",contents:t.title+" "+G("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),eo({className:t.className,checkClassName:t.checkClassName,items:t.items,template:t.template,itemClick:t.itemClick})],{callback:e}).render()},Es=function(t){return q([Y({className:"dropdown-toggle",contents:t.title+" "+G("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),Me([q({className:"note-align",children:t.items[0]}),q({className:"note-list",children:t.items[1]})])]).render()},Is=function(t,e,n){var i=18,r=u()(t.target.parentNode),a=r.next(),s=r.find(".note-dimension-picker-mousecatcher"),c=r.find(".note-dimension-picker-highlighted"),f=r.find(".note-dimension-picker-unhighlighted"),d;if(t.offsetX===void 0){var h=u()(t.target).offset();d={x:t.pageX-h.left,y:t.pageY-h.top}}else d={x:t.offsetX,y:t.offsetY};var p={c:Math.ceil(d.x/i)||1,r:Math.ceil(d.y/i)||1};c.css({width:p.c+"em",height:p.r+"em"}),s.data("value",p.c+"x"+p.r),p.c>3&&p.c<e&&f.css({width:p.c+1+"em"}),p.r>3&&p.r<n&&f.css({height:p.r+1+"em"}),a.html(p.c+" x "+p.r)},_s=function(t){return q([Y({className:"dropdown-toggle",contents:t.title+" "+G("note-icon-caret"),tooltip:t.tooltip,data:{toggle:"dropdown"}}),Me({className:"note-table",items:['<div class="note-dimension-picker">','<div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"></div>','<div class="note-dimension-picker-highlighted"></div>','<div class="note-dimension-picker-unhighlighted"></div>',"</div>",'<div class="note-dimension-display">1 x 1</div>'].join("")})],{callback:function(n){var i=n.find(".note-dimension-picker-mousecatcher");i.css({width:t.col+"em",height:t.row+"em"}).on("mouseup",t.itemClick).on("mousemove",function(r){Is(r,t.col,t.row)})}}).render()},to=$.create('<div class="note-color-palette"></div>',function(o,t){for(var e=[],n=0,i=t.colors.length;n<i;n++){for(var r=t.eventName,a=t.colors[n],s=t.colorsName[n],c=[],f=0,d=a.length;f<d;f++){var h=a[f],p=s[f];c.push(['<button type="button" class="note-btn note-color-btn"','style="background-color:',h,'" ','data-event="',r,'" ','data-value="',h,'" ','data-title="',p,'" ','aria-label="',p,'" ','data-toggle="button" tabindex="-1"></button>'].join(""))}e.push('<div class="note-color-row">'+c.join("")+"</div>")}o.html(e.join("")),o.find(".note-color-btn").each(function(){u()(this).data("_lite_tooltip",new Xt(u()(this),{container:t.container}))})}),As=function(t,e){return q({className:"note-color",children:[Y({className:"note-current-color-button",contents:t.title,tooltip:t.lang.color.recent,click:t.currentClick,callback:function(i){var r=i.find(".note-recent-color");e!=="foreColor"&&(r.css("background-color","#FFFF00"),i.attr("data-backColor","#FFFF00"))}}),Y({className:"dropdown-toggle",contents:G("note-icon-caret"),tooltip:t.lang.color.more,data:{toggle:"dropdown"}}),Me({items:["<div>",'<div class="note-btn-group btn-background-color">','<div class="note-palette-title">'+t.lang.color.background+"</div>","<div>",'<button type="button" class="note-color-reset note-btn note-btn-block" data-event="backColor" data-value="transparent">',t.lang.color.transparent,"</button>","</div>",'<div class="note-holder" data-event="backColor"></div>','<div class="btn-sm">','<input type="color" id="html5bcp" class="note-btn btn-default" value="#21104A" style="width:100%;" data-value="cp">','<button type="button" class="note-color-reset btn" data-event="backColor" data-value="cpbackColor">',t.lang.color.cpSelect,"</button>","</div>","</div>",'<div class="note-btn-group btn-foreground-color">','<div class="note-palette-title">'+t.lang.color.foreground+"</div>","<div>",'<button type="button" class="note-color-reset note-btn note-btn-block" data-event="removeFormat" data-value="foreColor">',t.lang.color.resetToDefault,"</button>","</div>",'<div class="note-holder" data-event="foreColor"></div>','<div class="btn-sm">','<input type="color" id="html5fcp" class="note-btn btn-default" value="#21104A" style="width:100%;" data-value="cp">','<button type="button" class="note-color-reset btn" data-event="foreColor" data-value="cpforeColor">',t.lang.color.cpSelect,"</button>","</div>","</div>","</div>"].join(""),callback:function(i){i.find(".note-holder").each(function(){var r=u()(this);r.append(to({colors:t.colors,eventName:r.data("event")}).render())}),e==="fore"?(i.find(".btn-background-color").hide(),i.css({"min-width":"210px"})):e==="back"&&(i.find(".btn-foreground-color").hide(),i.css({"min-width":"210px"}))},click:function(i){var r=u()(i.target),a=r.data("event"),s=r.data("value"),c=document.getElementById("html5fcp").value,f=document.getElementById("html5bcp").value;if(s==="cp"?i.stopPropagation():s==="cpbackColor"?s=f:s==="cpforeColor"&&(s=c),a&&s){var d=a==="backColor"?"background-color":"color",h=r.closest(".note-color").find(".note-recent-color"),p=r.closest(".note-color").find(".note-current-color-button");h.css(d,s),p.attr("data-"+a,s),e==="fore"?t.itemClick("foreColor",s):e==="back"?t.itemClick("backColor",s):t.itemClick(a,s)}}})]}).render()},Xe=$.create('<div class="note-modal" aria-hidden="false" tabindex="-1" role="dialog"></div>',function(o,t){t.fade&&o.addClass("fade"),o.attr({"aria-label":t.title}),o.html(['<div class="note-modal-content">',t.title?'<div class="note-modal-header"><button type="button" class="close" aria-label="Close" aria-hidden="true"><i class="note-icon-close"></i></button><h4 class="note-modal-title">'+t.title+"</h4></div>":"",'<div class="note-modal-body">'+t.body+"</div>",t.footer?'<div class="note-modal-footer">'+t.footer+"</div>":"","</div>"].join("")),o.data("modal",new Ss(o,t))}),Ls=function(t){var e='<div class="note-form-group"><label for="note-dialog-video-url-'+t.id+'" class="note-form-label">'+t.lang.video.url+' <small class="text-muted">'+t.lang.video.providers+'</small></label><input id="note-dialog-video-url-'+t.id+'" class="note-video-url note-input" type="text"/></div>',n=['<button type="button" href="#" class="note-btn note-btn-primary note-video-btn disabled" disabled>',t.lang.video.insert,"</button>"].join("");return Xe({title:t.lang.video.insert,fade:t.fade,body:e,footer:n}).render()},$s=function(t){var e='<div class="note-form-group note-group-select-from-files"><label for="note-dialog-image-file-'+t.id+'" class="note-form-label">'+t.lang.image.selectFromFiles+'</label><input id="note-dialog-image-file-'+t.id+'" class="note-note-image-input note-input" type="file" name="files" accept="image/*" multiple="multiple"/>'+t.imageLimitation+'</div><div class="note-form-group"><label for="note-dialog-image-url-'+t.id+'" class="note-form-label">'+t.lang.image.url+'</label><input id="note-dialog-image-url-'+t.id+'" class="note-image-url note-input" type="text"/></div>',n=['<button href="#" type="button" class="note-btn note-btn-primary note-btn-large note-image-btn disabled" disabled>',t.lang.image.insert,"</button>"].join("");return Xe({title:t.lang.image.insert,fade:t.fade,body:e,footer:n}).render()},Ds=function(t){var e='<div class="note-form-group"><label for="note-dialog-link-txt-'+t.id+'" class="note-form-label">'+t.lang.link.textToDisplay+'</label><input id="note-dialog-link-txt-'+t.id+'" class="note-link-text note-input" type="text"/></div><div class="note-form-group"><label for="note-dialog-link-url-'+t.id+'" class="note-form-label">'+t.lang.link.url+'</label><input id="note-dialog-link-url-'+t.id+'" class="note-link-url note-input" type="text" value="http://"/></div>'+(t.disableLinkTarget?"":'<div class="checkbox"><label for="note-dialog-link-nw-'+t.id+'"><input id="note-dialog-link-nw-'+t.id+'" type="checkbox" checked> '+t.lang.link.openInNewWindow+"</label></div>"),n=['<button href="#" type="button" class="note-btn note-btn-primary note-link-btn disabled" disabled>',t.lang.link.insert,"</button>"].join("");return Xe({className:"link-dialog",title:t.lang.link.insert,fade:t.fade,body:e,footer:n}).render()},Ns=$.create(['<div class="note-popover bottom">','<div class="note-popover-arrow"></div>','<div class="popover-content note-children-container"></div>',"</div>"].join(""),function(o,t){var e=typeof t.direction<"u"?t.direction:"bottom";o.addClass(e).hide(),t.hideArrow&&o.find(".note-popover-arrow").hide()}),Rs=$.create('<div class="checkbox"></div>',function(o,t){o.html(["<label"+(t.id?' for="note-'+t.id+'"':"")+">",'<input role="checkbox" type="checkbox"'+(t.id?' id="note-'+t.id+'"':""),t.checked?" checked":"",' aria-checked="'+(t.checked?"true":"false")+'"/>',t.text?t.text:"","</label>"].join(""))}),G=function(t,e){return t.match(/^</)?t:(e=e||"i","<"+e+' class="'+t+'"></'+e+">")},Fs=function(t){return{editor:vt,toolbar:mt,editingArea:Ze,codable:Ye,editable:gt,statusbar:bt,airEditor:Qt,airEditable:Jt,buttonGroup:q,button:Y,dropdown:Me,dropdownCheck:eo,dropdownButton:xs,dropdownButtonContents:Ps,dropdownCheckButton:Ts,paragraphDropdownButton:Es,tableDropdownButton:_s,colorDropdownButton:As,palette:to,dialog:Xe,videoDialog:Ls,imageDialog:$s,linkDialog:Ds,popover:Ns,checkbox:Rs,icon:G,options:t,toggleBtn:function(n,i){n.toggleClass("disabled",!i),n.attr("disabled",!i)},toggleBtnActive:function(n,i){n.toggleClass("active",i)},check:function(n,i){n.find(".checked").removeClass("checked"),n.find('[data-value="'+i+'"]').addClass("checked")},onDialogShown:function(n,i){n.one("note.modal.show",i)},onDialogHidden:function(n,i){n.one("note.modal.hide",i)},showDialog:function(n){n.data("modal").show()},hideDialog:function(n){n.data("modal").hide()},getPopoverContent:function(n){return n.find(".note-popover-content")},getDialogBody:function(n){return n.find(".note-modal-body")},createLayout:function(n){var i=(t.airMode?Qt([Ze([Ye(),Jt()])]):t.toolbarPosition==="bottom"?vt([Ze([Ye(),gt()]),mt(),bt()]):vt([mt(),Ze([Ye(),gt()]),bt()])).render();return i.insertAfter(n),{note:n,editor:i,toolbar:i.find(".note-toolbar"),editingArea:i.find(".note-editing-area"),editable:i.find(".note-editable"),codable:i.find(".note-codable"),statusbar:i.find(".note-statusbar")}},removeLayout:function(n,i){n.html(i.editable.html()),i.editor.remove(),n.off("summernote"),n.show()}}};return u().summernote=u().extend(u().summernote,{ui_template:Fs,interface:"lite"}),co})())}(Ct)),Ct.exports}var lo=Ks();const Ws=Os(lo),Gs=Us({__proto__:null,default:Ws},[lo]);export{Gs as s};
