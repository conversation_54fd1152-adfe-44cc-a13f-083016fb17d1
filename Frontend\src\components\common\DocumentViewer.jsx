import React, { useState, useEffect, useCallback } from 'react';
import { FaFileWord, FaFileExcel, FaFileAlt, FaFilePdf, FaFile, FaDownload, FaExternalLinkAlt, FaExclamationTriangle, FaExpand } from 'react-icons/fa';
import UniversalPDFViewer from './UniversalPDFViewer';
import OfficeDocumentViewer from './OfficeDocumentViewer';
import FullscreenViewer from './FullscreenViewer';
import '../../styles/DocumentViewer.css';

const DocumentViewer = ({
  fileUrl,
  fileName = '',
  title = 'Document',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  const [documentType, setDocumentType] = useState('unknown');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isFullscreenOpen, setIsFullscreenOpen] = useState(false);



  // Get file extension
  const getFileExtension = useCallback((filename) => {
    if (!filename) return '';
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex === -1) return '';
    return filename.substring(lastDotIndex).toLowerCase();
  }, []);

  // Enhanced document type detection with content analysis
  const getDocumentType = useCallback(async (filename, previewUrl = null) => {
    const extension = getFileExtension(filename);
    // Check if this is a JSON preview file
    if (previewUrl && previewUrl.endsWith('.json')) {
      try {
        // Fetch and analyze the JSON preview content
        const response = await fetch(previewUrl);
        if (response.ok) {
          const jsonData = await response.json();

          if (jsonData.sheets || jsonData.data?.sheets ||
            (jsonData.data?.content && typeof jsonData.data.content === 'object')) {
            //console.log(`[DocumentViewer] Detected Excel from JSON structure`);
            return 'excel';
          }

          if (jsonData.html || jsonData.data?.html || jsonData.text || jsonData.data?.text) {
            //console.log(`[DocumentViewer] Detected Word from JSON structure`);
            return 'word';
          }

          // Check metadata for document type
          if (jsonData.metadata?.documentType) {
            //console.log(`[DocumentViewer] Detected ${jsonData.metadata.documentType} from metadata`);
            return jsonData.metadata.documentType;
          }
        }
      } catch (error) {
        console.warn(`[DocumentViewer] Could not analyze JSON preview:`, error);
      }

      // Fallback to filename pattern analysis for JSON previews
      const previewFileName = previewUrl.split('/').pop().toLowerCase();

      if (previewFileName.includes('xls') || previewFileName.includes('excel') ||
        previewFileName.includes('spreadsheet') || previewFileName.includes('sheet')) {
        return 'excel';
      }

      if (previewFileName.includes('doc') || previewFileName.includes('word')) {
        return 'word';
      }
    }

    // Content-based detection from filename
    const lowerFilename = filename.toLowerCase();

    // Excel detection
    if (lowerFilename.includes('excel') || lowerFilename.includes('spreadsheet') ||
      lowerFilename.includes('worksheet') || lowerFilename.includes('xls') ||
      lowerFilename.includes('budget') || lowerFilename.includes('data') ||
      lowerFilename.includes('report') || lowerFilename.includes('analysis')) {
      return 'excel';
    }

    // Extension-based detection
    if (['.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv'].includes(extension)) {
      return 'excel';
    }

    if (['.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm'].includes(extension)) {
      return 'word';
    }

    if (extension === '.pdf') {
      return 'pdf';
    }

    if (['.txt', '.rtf'].includes(extension)) {
      return 'text';
    }

    if (['.odt', '.ods', '.odp', '.odg', '.odf'].includes(extension)) {
      return 'opendocument';
    }

    if (['.pages', '.numbers', '.key'].includes(extension)) {
      return 'iwork';
    }

    if (['.epub', '.mobi', '.azw', '.azw3'].includes(extension)) {
      return 'ebook';
    }

    return 'unknown';
  }, [getFileExtension]);

  // Initialize document type
  useEffect(() => {
    const initializeDocumentType = async () => {
      if (!fileName && !fileUrl) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const detectedType = await getDocumentType(fileName, fileUrl);
        setDocumentType(detectedType);
        // console.log(`[DocumentViewer] Final document type: ${detectedType}`);
      } catch (error) {
        console.error('[DocumentViewer] Document type detection failed:', error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    initializeDocumentType();
  }, [fileName, fileUrl, getDocumentType]);

  // Get icon for document type
  const getDocumentIcon = useCallback((type) => {
    switch (type) {
      case 'word':
        return <FaFileWord className="document-viewer__icon document-viewer__icon--word" />;
      case 'excel':
        return <FaFileExcel className="document-viewer__icon document-viewer__icon--excel" />;
      case 'pdf':
        return <FaFilePdf className="document-viewer__icon document-viewer__icon--pdf" />;
      case 'text':
      case 'opendocument':
      case 'iwork':
      case 'ebook':
        return <FaFileAlt className="document-viewer__icon document-viewer__icon--text" />;
      default:
        return <FaFile className="document-viewer__icon document-viewer__icon--default" />;
    }
  }, []);

  // Get document type name
  const getDocumentTypeName = useCallback((type) => {
    switch (type) {
      case 'word':
        return 'Microsoft Word Document';
      case 'excel':
        return 'Microsoft Excel Spreadsheet';
      case 'pdf':
        return 'PDF Document';
      case 'text':
        return 'Text Document';
      case 'opendocument':
        return 'OpenDocument Format';
      case 'iwork':
        return 'Apple iWork Document';
      case 'ebook':
        return 'E-book';
      default:
        return 'Document';
    }
  }, []);

  // Handle native app opening for mobile
  const handleOpenInNativeApp = useCallback(() => {
    if (!fileUrl) return;

    // Create a download link that will trigger the native app
    const link = document.createElement('a');
    link.href = fileUrl;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';

    // Add download attribute for better mobile support
    if (fileName) {
      link.download = fileName;
    }

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [fileUrl, fileName]);

  // Render based on document type
  const renderViewer = () => {
    if (isLoading) {
      return (
        <div className="document-viewer__loading">
          <div className="document-viewer__spinner"></div>
          <p>Loading document...</p>
        </div>
      );
    }

    if (hasError) {
      return (
        <div className="document-viewer__error">
          <FaExclamationTriangle className="document-viewer__warning-icon" />
          <h3>Document Preview Not Available</h3>
          <p>Unable to load the document preview</p>
        </div>
      );
    }

    switch (documentType) {
      case 'pdf':
        return (
          <>
            <div className="document-viewer__toolbar">
              <button
                className="document-viewer__toolbar-btn"
                onClick={() => setIsFullscreenOpen(true)}
                title="Open in Fullscreen"
              >
                <FaExpand /> Fullscreen
              </button>
            </div>
            <UniversalPDFViewer
              fileUrl={fileUrl}
              fileName={fileName}
              height={height}
              showDownload={showDownload}
              onDownload={onDownload}
            />
            {isFullscreenOpen && (
              <FullscreenViewer
                isOpen={isFullscreenOpen}
                onClose={() => setIsFullscreenOpen(false)}
                contentType="pdf"
                content={fileUrl}
              />
            )}
          </>
        );
      case 'excel':
      case 'word':
        return (
          <OfficeDocumentViewer
            fileUrl={fileUrl}
            fileName={fileName}
            documentType={documentType}
            height={height}
            showDownload={showDownload}
            onDownload={onDownload}
          />
        );
      default:
        return (
          <div className="document-viewer__unsupported">
            <FaExclamationTriangle className="document-viewer__warning-icon" />
            <h3>Preview Not Available</h3>
            <p>This file type cannot be previewed</p>
          </div>
        );
    }
  };

  return (
    <>
      <div className={`document-viewer ${className}`} style={{ height }}>
        {renderViewer()}
      </div>

      {/* Fullscreen Viewer */}
      {documentType === 'pdf' && (
        <FullscreenViewer
          isOpen={isFullscreenOpen}
          onClose={() => setIsFullscreenOpen(false)}
          contentType="pdf"
          content={fileUrl}
        />
      )}
    </>
  );
};

export default DocumentViewer;
