import{ac as de,a as oe,u as ue,b as me,r as d,c1 as ge,b9 as b,j as e,b_ as V,au as L,aM as he,aN as G,c2 as pe,bf as k,c0 as J}from"./index-oGr_1a_A.js";import{S as q}from"./SellerLayout-Du2JNoeS.js";import{S as I,i as K,g as ye,T as H,a as W,U as fe,v as _e}from"./TimezoneErrorBoundary-CRcqZhiK.js";import{D as Se}from"./DocumentViewer-B7AtyaWY.js";import{a as C,b as j}from"./timezoneUtils-Dky5bF8c.js";import"./index-auB_fJid.js";const X={maxSize:5*1024*1024,allowedTypes:["image/jpeg","image/jpg","image/png","image/gif"],allowedExtensions:[".jpg",".jpeg",".png",".gif"]},De=()=>{var $;const{id:p}=de(),_=oe(),w=ue(),{singleContent:l,isLoading:S}=me(s=>s.content),[x,P]=d.useState(""),[t,u]=d.useState({title:"",category:"",coachName:"",description:"",fileUrl:"",aboutCoach:"",strategicContent:"",sport:"Other",contentType:"Video",previewUrl:"",thumbnailUrl:"",duration:"",videoLength:"",fileSize:"",tags:[],difficulty:"Intermediate",language:"English",prerequisites:[],learningObjectives:[],equipment:[],saleType:"Fixed",price:0,allowCustomRequests:!1,customRequestPrice:"",status:"Published",visibility:"Public",auctionDetails:{basePrice:"",auctionStartDate:"",auctionEndDate:"",minimumBidIncrement:"",allowOfferBeforeAuctionStart:!1}}),[m,Y]=d.useState(null),[T,U]=d.useState(!1),[Q,v]=d.useState(!1),[Z,A]=d.useState(""),[ee,N]=d.useState(""),[M,O]=d.useState(""),[te,z]=d.useState(!0),[r,f]=d.useState({}),[ae,ie]=d.useState(!1),[o,se]=d.useState(!1),[D,R]=d.useState(!1),[le,E]=d.useState(null);d.useEffect(()=>{p&&!D&&(R(!0),E(null),_(ge(p)).unwrap().then(()=>{}).catch(s=>{console.error("Failed to fetch strategy data:",s),E(s.message||"Failed to load strategy data"),b("Failed to load strategy data. Please try again.")}))},[_,p,D]),d.useEffect(()=>{var s,a,i,n,c;l&&u({title:l.title||"",category:l.category||"",coachName:l.coachName||"",description:l.description||"",fileUrl:l.fileUrl||"",aboutCoach:l.aboutCoach||"",strategicContent:l.strategicContent||"",sport:l.sport||"Basketball",contentType:l.contentType||"",thumbnailUrl:l.thumbnailUrl||"",tags:l.tags||[],difficulty:l.difficulty||"",saleType:l.saleType||"",price:l.price||"",allowCustomRequests:l.allowCustomRequests||!1,auctionDetails:{basePrice:((s=l.auctionDetails)==null?void 0:s.basePrice)||"",auctionStartDate:(a=l.auctionDetails)!=null&&a.auctionStartDate?C(new Date(l.auctionDetails.auctionStartDate)):"",auctionEndDate:(i=l.auctionDetails)!=null&&i.auctionEndDate?C(new Date(l.auctionDetails.auctionEndDate)):"",minimumBidIncrement:((n=l.auctionDetails)==null?void 0:n.minimumBidIncrement)||"",allowOfferBeforeAuctionStart:((c=l.auctionDetails)==null?void 0:c.allowOfferBeforeAuctionStart)||!1},previewUrl:l.previewUrl||"",duration:l.duration||"",videoLength:l.videoLength||"",fileSize:l.fileSize||"",prerequisites:l.prerequisites||[],learningObjectives:l.learningObjectives||[],equipment:l.equipment||[],customRequestPrice:l.customRequestPrice||"",status:l.status||"Published",visibility:l.visibility||"Public"})},[l]);const h=s=>{const{name:a,value:i,type:n,checked:c}=s.target;u(g=>({...g,[a]:n==="checkbox"?c:i})),F(a,n==="checkbox"?c:i)},B=(s,a)=>{u(i=>({...i,[s]:a})),F(s,a)},y=s=>{const{name:a,value:i}=s.target;F(a,i)},F=(s,a)=>{const i={...r};switch(s){case"title":a.trim()?delete i.title:i.title="Strategy title is required";break;case"category":a?delete i.category:i.category="Please select a category";break;case"coachName":a.trim()?delete i.coachName:i.coachName="Coach/Seller/Academy name is required";break;case"description":{a.replace(/<[^>]*>/g,"").trim()?delete i.description:i.description="Strategy description is required";break}case"aboutCoach":{a.replace(/<[^>]*>/g,"").trim()?delete i.aboutCoach:i.aboutCoach="About the coach information is required";break}case"strategicContent":{a.replace(/<[^>]*>/g,"").trim()?delete i.strategicContent:i.strategicContent="Strategic content description is required";break}case"contentType":a?delete i.contentType:i.contentType="Please select a content type";break;case"difficulty":a?delete i.difficulty:i.difficulty="Please select a difficulty level";break;case"saleType":a?delete i.saleType:i.saleType="Please select a sale type";break;case"price":!a||a<=0?i.price="Please enter a valid price greater than $0":delete i.price;break}f(i)},re=async s=>{const a=s.target.files[0];if(!a)return;const i=_e(a,t.contentType);if(!i.isValid){b(i.message),s.target.value="";return}v(!0),A("content file"),N(a.name);try{const n=new FormData;n.append("file",a);const c=await _(J(n)).unwrap();u(g=>({...g,fileUrl:c.data.fileUrl,fileSize:c.data.fileSize})),Y({name:c.data.fileName,url:c.data.fileUrl,type:c.data.fileType,size:c.data.fileSize}),k("File uploaded successfully!")}catch(n){console.error("File upload failed:",n),b("Failed to upload file. Please try again.")}finally{v(!1),A(""),N("")}},ne=async s=>{const a=s.target.files[0];if(a){O(""),z(!1);try{if(!X.allowedTypes.includes(a.type))throw new Error("Only JPG, JPEG, PNG, and GIF formats are supported for thumbnails");if(a.size>X.maxSize)throw new Error("Thumbnail file size must be less than 5MB");v(!0),A("thumbnail"),N(a.name);const i=new FormData;i.append("file",a),i.append("type","thumbnail");const n=await _(J(i)).unwrap();if(!n.data||!n.data.fileUrl)throw new Error("Invalid response from server");const c=n.data.fileUrl;u(g=>({...g,thumbnailUrl:c})),z(!0),k("Thumbnail uploaded successfully!")}catch(i){console.error("Thumbnail upload failed:",i),O(i.message||"Failed to upload thumbnail. Please try again."),u(n=>({...n,thumbnailUrl:""}))}finally{v(!1),A(""),N("")}}},ce=async s=>{s.preventDefault(),se(!0),U(!0);const a={};if(t.title.trim()||(a.title="Strategy title is required"),t.category||(a.category="Please select a category"),t.coachName.trim()||(a.coachName="Coach/Seller/Academy name is required"),t.description.replace(/<[^>]*>/g,"").trim()||(a.description="Strategy description is required"),t.contentType||(a.contentType="Please select a content type"),!t.fileUrl&&!m&&(a.fileUpload="Please upload a video or document file"),t.aboutCoach.replace(/<[^>]*>/g,"").trim()||(a.aboutCoach="About the coach information is required"),t.strategicContent.replace(/<[^>]*>/g,"").trim()||(a.strategicContent="Strategic content description is required"),t.thumbnailUrl||(a.thumbnailUpload="Please upload a thumbnail image"),t.difficulty||(a.difficulty="Please select a difficulty level"),t.saleType||(a.saleType="Please select a sale type"),t.saleType==="Fixed"&&(!t.price||t.price<=0)&&(a.price="Please enter a valid price greater than $0"),t.saleType==="Auction"&&((!t.auctionDetails.basePrice||t.auctionDetails.basePrice<=0)&&(a.auctionBasePrice="Please enter a valid starting bid price greater than $0"),(!t.auctionDetails.minimumBidIncrement||t.auctionDetails.minimumBidIncrement<=0)&&(a.auctionMinIncrement="Please enter a valid minimum bid increment greater than $0"),t.auctionDetails.auctionStartDate?new Date(t.auctionDetails.auctionStartDate)<=new Date&&(a.auctionStartDate="Auction start date must be in the future"):a.auctionStartDate="Please select an auction start date",t.auctionDetails.auctionEndDate||(a.auctionEndDate="Please select an auction end date"),t.auctionDetails.auctionStartDate&&t.auctionDetails.auctionEndDate)){const i=new Date(t.auctionDetails.auctionStartDate);new Date(t.auctionDetails.auctionEndDate)<=i&&(a.auctionDateRange="Auction end date must be after start date")}if(Object.keys(a).length>0){f(a),ie(!0),U(!1),setTimeout(()=>{const i=document.querySelector(".AddStrategy__validation-error");i&&i.scrollIntoView({behavior:"smooth",block:"center"})},100);return}try{const i={...t,sport:t.category||"Other",coachName:t.coachName||"Coach",thumbnailUrl:t.thumbnailUrl};i.saleType==="Auction"&&(i.auctionDetails.auctionStartDate&&(i.auctionDetails.auctionStartDate=j(new Date(i.auctionDetails.auctionStartDate))),i.auctionDetails.auctionEndDate&&(i.auctionDetails.auctionEndDate=j(new Date(i.auctionDetails.auctionEndDate)))),await _(pe({id:p,contentData:i})).unwrap(),k("🎉 Strategy updated successfully!"),w("/seller/my-sports-strategies")}catch(i){console.error("Content update failed:",i);let n="Failed to update strategy. Please try again.";i.message?n=i.message:i.errors&&i.errors.length>0?n=i.errors[0].msg||n:typeof i=="string"&&(n=i),b(`❌ ${n}`)}finally{U(!1)}};return S&&!l||!l&&!D?e.jsx(q,{children:e.jsx("div",{className:"AddStrategy",children:e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading strategy details..."})]})})}):!l&&D&&!S?e.jsx(q,{children:e.jsx("div",{className:"AddStrategy",children:e.jsxs("div",{className:"error-container",children:[e.jsx("h3",{children:"Strategy not found"}),e.jsx("p",{children:le||"The strategy you're trying to edit doesn't exist or has been removed."}),e.jsxs("div",{className:"error-actions",children:[e.jsx("button",{className:"btn btn-primary",onClick:()=>{R(!1),E(null)},children:"Try Again"}),e.jsx("button",{className:"btn btn-outline",onClick:()=>w("/seller/my-sports-strategies"),children:"Back to Strategies"})]})]})})}):e.jsx(q,{children:e.jsxs("div",{className:"AddStrategy",children:[e.jsx("div",{className:"AddStrategy__header",children:e.jsx("p",{className:"AddStrategy__subtitle",children:"Update your strategy details"})}),e.jsxs("form",{className:"AddStrategy__form",onSubmit:ce,children:[e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Strategy Title"}),e.jsx("input",{type:"text",name:"title",className:"AddStrategy__input",placeholder:"Add title for Strategy",value:t.title,onChange:h,onBlur:y}),(r.title||o&&!t.title.trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.title||"Strategy title is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Category"}),e.jsxs("select",{name:"category",className:"AddStrategy__select",value:t.category,onChange:h,onBlur:y,children:[e.jsx("option",{value:"",children:"Select Category"}),e.jsx("option",{value:"Basketball",children:"Basketball"}),e.jsx("option",{value:"Football",children:"Football"}),e.jsx("option",{value:"Soccer",children:"Soccer"}),e.jsx("option",{value:"Baseball",children:"Baseball"})]}),(r.category||o&&!t.category)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.category||"Please select a category"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Coach/Seller/Academy Name"}),e.jsx("input",{type:"text",name:"coachName",className:"AddStrategy__input",placeholder:"Enter coach, seller, or academy name",value:t.coachName,onChange:h,onBlur:y}),(r.coachName||ae&&!t.coachName.trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.coachName||"Coach/Seller/Academy name is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Description for Strategy"}),e.jsx(I,{value:t.description,onChange:s=>B("description",s),placeholder:"Enter a detailed description of your strategy...",height:200,className:"AddStrategy__summernote",contentKey:`desc-${p}`}),(r.description||o&&!t.description.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.description||"Strategy description is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Content Type"}),e.jsxs("select",{name:"contentType",className:"AddStrategy__select",value:t.contentType,onChange:h,onBlur:y,children:[e.jsx("option",{value:"",children:"Select Content Type"}),e.jsx("option",{value:"Video",children:"Video"}),e.jsx("option",{value:"Document",children:"Document"})]}),(r.contentType||o&&!t.contentType)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.contentType||"Please select a content type"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsxs("label",{className:"AddStrategy__label",children:["Upload ",t.contentType||"Video/Document"]}),t.contentType&&e.jsx("p",{className:"AddStrategy__format-note",children:t.contentType==="Video"?e.jsxs(e.Fragment,{children:["Maximum size: ",e.jsx("span",{children:"500MB"})," • Supported formats: ",e.jsx("span",{children:"MP4, MOV, AVI, WEBM"})]}):t.contentType==="Document"?e.jsxs(e.Fragment,{children:["Maximum size: ",e.jsx("span",{children:"50MB"})," • Supported formats: ",e.jsx("span",{children:"PDF, DOC, DOCX"})]}):"Please select a content type to see upload requirements"}),e.jsxs("label",{htmlFor:"file-upload",className:"AddStrategy__upload",children:[e.jsx("input",{type:"file",id:"file-upload",className:"AddStrategy__file-input",onChange:re,accept:ye(t.contentType),disabled:K(t.contentType),style:{display:"none"}}),e.jsxs("div",{className:`AddStrategy__upload-content ${K(t.contentType)?"AddStrategy__upload-content--disabled":""}`,children:[e.jsx(V,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:m?m.name:t.fileUrl?"Current file uploaded":"Click to upload file"})]}),(m||t.fileUrl)&&e.jsxs("div",{className:"AddStrategy__file-info",children:[e.jsx("p",{className:"AddStrategy__file-name",children:m?m.name:"Current file uploaded"}),m&&e.jsxs("p",{className:"AddStrategy__file-size",children:[(m.size/1024/1024).toFixed(2)," MB"]})]})]}),(r.fileUpload||o&&!t.fileUrl&&!m)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.fileUpload||"Please upload a video or document file"})}),t.fileUrl&&e.jsxs("div",{className:"AddStrategy__file-preview",children:[e.jsx("h4",{className:"AddStrategy__preview-title",children:"Current File Preview"}),t.contentType==="Video"&&e.jsx("div",{className:"AddStrategy__video-preview",children:e.jsxs("video",{className:"AddStrategy__video-element",controls:!0,controlsList:"nodownload nofullscreen noremoteplayback",disablePictureInPicture:!0,style:{width:"100%",maxHeight:"300px"},onError:s=>{console.error("Video preview error:",s)},children:[e.jsx("source",{src:L(t.fileUrl),type:"video/mp4"}),"Your browser does not support the video tag."]})}),t.contentType==="Document"&&e.jsx("div",{className:"AddStrategy__document-preview",children:e.jsx(Se,{fileUrl:L(t.fileUrl),fileName:(m==null?void 0:m.name)||(($=t.fileUrl)==null?void 0:$.split("/").pop())||"document",title:"Document Preview",className:"AddStrategy__document-element",height:"300px",showDownload:!1})})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"About The Coach"}),e.jsx(I,{value:t.aboutCoach,onChange:s=>B("aboutCoach",s),placeholder:"Share your background, experience, and expertise...",height:200,className:"AddStrategy__summernote",contentKey:`coach-${p}`}),(r.aboutCoach||o&&!t.aboutCoach.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.aboutCoach||"About the coach information is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Includes Strategic Content"}),e.jsx(I,{value:t.strategicContent,onChange:s=>B("strategicContent",s),placeholder:"Describe what strategic content is included...",height:200,className:"AddStrategy__summernote",contentKey:`strategic-${p}`}),(r.strategicContent||o&&!t.strategicContent.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.strategicContent||"Strategic content description is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Thumbnail Image"}),e.jsxs("p",{className:"AddStrategy__format-note",children:["Maximum size: ",e.jsx("span",{children:"5MB"})," • Supported formats: ",e.jsx("span",{children:"JPG, JPEG, PNG, GIF"})]}),e.jsxs("label",{htmlFor:"thumbnail-upload",className:"AddStrategy__upload",children:[e.jsx("input",{type:"file",id:"thumbnail-upload",className:"AddStrategy__file-input",accept:"image/jpeg,image/jpg,image/png,image/gif",onChange:ne,style:{display:"none"}}),e.jsxs("div",{className:"AddStrategy__upload-content",children:[e.jsx(V,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:t.thumbnailUrl?"Thumbnail uploaded":"Click to upload thumbnail"})]}),M&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:M})}),(r.thumbnailUpload||o&&!t.thumbnailUrl)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.thumbnailUpload||"Please upload a thumbnail image"})}),t.thumbnailUrl&&te&&e.jsx("div",{className:"AddStrategy__thumbnail-preview",children:e.jsx("img",{src:he(t.thumbnailUrl),alt:"Thumbnail preview",onError:s=>{s.target.src=G(),b("Failed to load thumbnail preview")},style:{maxWidth:"100%",height:"auto",borderRadius:"var(--border-radius)"}})}),!t.thumbnailUrl&&e.jsx("div",{className:"AddStrategy__thumbnail-preview",children:e.jsx("img",{src:G(200,120,"No thumbnail"),alt:"No thumbnail",style:{maxWidth:"100%",height:"auto",borderRadius:"var(--border-radius)",opacity:.7}})})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Difficulty Level"}),e.jsxs("select",{name:"difficulty",className:"AddStrategy__select",value:t.difficulty,onChange:h,onBlur:y,children:[e.jsx("option",{value:"",children:"Select Difficulty"}),e.jsx("option",{value:"Beginner",children:"Beginner"}),e.jsx("option",{value:"Intermediate",children:"Intermediate"}),e.jsx("option",{value:"Advanced",children:"Advanced"}),e.jsx("option",{value:"Professional",children:"Professional"})]}),(r.difficulty||o&&!t.difficulty)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.difficulty||"Please select a difficulty level"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Language"}),e.jsxs("select",{name:"language",className:"AddStrategy__select",value:t.language,onChange:h,children:[e.jsx("option",{value:"",children:"Select Language"}),e.jsx("option",{value:"English",children:"English"}),e.jsx("option",{value:"Spanish",children:"Spanish"}),e.jsx("option",{value:"French",children:"French"}),e.jsx("option",{value:"German",children:"German"}),e.jsx("option",{value:"Chinese",children:"Chinese"}),e.jsx("option",{value:"Japanese",children:"Japanese"}),e.jsx("option",{value:"Other",children:"Other"})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Tags"}),e.jsxs("div",{className:"AddStrategy__array-field",children:[e.jsxs("div",{className:"AddStrategy__array-input",children:[e.jsx("input",{type:"text",className:"AddStrategy__input",placeholder:"Add a tag (e.g., basketball, technique, training)...",value:x,onChange:s=>P(s.target.value),onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),x.trim()&&(u(a=>({...a,tags:[...a.tags,x.trim()]})),P("")))}}),e.jsx("button",{type:"button",className:"btn-primary",onClick:()=>{x.trim()&&(u(s=>({...s,tags:[...s.tags,x.trim()]})),P(""))},children:"Add"})]}),t.tags.length>0&&e.jsx("div",{className:"AddStrategy__array-items",children:t.tags.map((s,a)=>e.jsxs("div",{className:"AddStrategy__array-item",children:[s,e.jsx("button",{type:"button",className:"AddStrategy__remove-btn",onClick:()=>{u(i=>({...i,tags:i.tags.filter((n,c)=>c!==a)}))},children:"X"})]},a))})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Sale Type"}),e.jsxs("select",{name:"saleType",className:"AddStrategy__select",value:t.saleType,onChange:h,onBlur:y,children:[e.jsx("option",{value:"",children:"Select Sale Type"}),e.jsx("option",{value:"Fixed",children:"Fixed Price"}),e.jsx("option",{value:"Auction",children:"Auction"})]}),(r.saleType||o&&!t.saleType)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.saleType||"Please select a sale type"})})]}),t.saleType==="Fixed"&&e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Price ($)"}),e.jsx("input",{type:"number",name:"price",className:"AddStrategy__input",placeholder:"Enter price",value:t.price,onChange:h,onBlur:y,min:"0",step:"0.01"}),(r.price||o&&(!t.price||t.price<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.price||"Please enter a valid price greater than $0"})})]}),t.allowCustomRequests&&e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Custom Request Price ($)"}),e.jsx("input",{type:"number",name:"customRequestPrice",className:"AddStrategy__input",placeholder:"Enter custom request price",value:t.customRequestPrice,onChange:h,min:"0",step:"0.01"})]}),t.saleType==="Auction"&&e.jsxs("div",{className:"AddStrategy__auction-section",children:[e.jsx("h3",{className:"AddStrategy__section-title",children:"Auction Settings"}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Starting Bid Price ($)"}),e.jsx("input",{type:"number",name:"auctionDetails.basePrice",className:"AddStrategy__input",placeholder:"Enter starting bid price",value:t.auctionDetails.basePrice,onChange:s=>u(a=>({...a,auctionDetails:{...a.auctionDetails,basePrice:s.target.value}})),min:"0",step:"0.01"}),(r.auctionBasePrice||o&&(!t.auctionDetails.basePrice||t.auctionDetails.basePrice<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionBasePrice||"Please enter a valid starting bid price greater than $0"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Minimum Bid Increment ($)"}),e.jsx("input",{type:"number",name:"auctionDetails.minimumBidIncrement",className:"AddStrategy__input",placeholder:"Enter minimum bid increment",value:t.auctionDetails.minimumBidIncrement,onChange:s=>u(a=>({...a,auctionDetails:{...a.auctionDetails,minimumBidIncrement:s.target.value}})),min:"0.01",step:"0.01"}),(r.auctionMinIncrement||o&&(!t.auctionDetails.minimumBidIncrement||t.auctionDetails.minimumBidIncrement<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionMinIncrement||"Please enter a valid minimum bid increment greater than $0"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Auction Start Date"}),e.jsxs(H,{children:[e.jsx("input",{type:"datetime-local",name:"auctionDetails.auctionStartDate",className:"AddStrategy__input",value:t.auctionDetails.auctionStartDate,min:C(new Date),onChange:s=>{const a=s.target.value;u(i=>({...i,auctionDetails:{...i.auctionDetails,auctionStartDate:a}})),a&&(j(new Date(a))<=new Date?f(c=>({...c,auctionStartDate:"Auction start date must be in the future"})):f(c=>{const g={...c};return delete g.auctionStartDate,g}))}}),e.jsx(W,{})]}),(r.auctionStartDate||o&&!t.auctionDetails.auctionStartDate)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionStartDate||"Please select an auction start date"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Auction End Date"}),e.jsxs(H,{children:[e.jsx("input",{type:"datetime-local",name:"auctionDetails.auctionEndDate",className:"AddStrategy__input",value:t.auctionDetails.auctionEndDate,min:C(t.auctionDetails.auctionStartDate?new Date(t.auctionDetails.auctionStartDate):new Date),onChange:s=>{const a=s.target.value;if(u(i=>({...i,auctionDetails:{...i.auctionDetails,auctionEndDate:a}})),a&&t.auctionDetails.auctionStartDate){const i=j(new Date(t.auctionDetails.auctionStartDate));j(new Date(a))<=i?f(c=>({...c,auctionDateRange:"Auction end date must be after start date"})):f(c=>{const g={...c};return delete g.auctionDateRange,g})}}}),e.jsx(W,{})]}),(r.auctionEndDate||r.auctionDateRange||o&&!t.auctionDetails.auctionEndDate)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionEndDate||r.auctionDateRange||"Please select an auction end date"})})]}),e.jsx("div",{className:"AddStrategy__field",children:e.jsxs("label",{className:"AddStrategy__checkbox-label",children:[e.jsx("input",{type:"checkbox",name:"auctionDetails.allowOfferBeforeAuctionStart",checked:t.auctionDetails.allowOfferBeforeAuctionStart,onChange:s=>u(a=>({...a,auctionDetails:{...a.auctionDetails,allowOfferBeforeAuctionStart:s.target.checked}})),className:"AddStrategy__checkbox"}),"Allow offers before auction starts"]})}),e.jsx("div",{className:"AddStrategy__field",children:e.jsx("div",{className:"AddStrategy__auction-note",children:e.jsxs("p",{children:[e.jsx("strong",{children:"Note:"})," Once the auction starts, the strategy content cannot be edited."]})})})]}),e.jsxs("div",{className:"AddStrategy__actions",children:[e.jsx("button",{type:"submit",className:"btn-primary",disabled:T||S,children:T?"Updating...":"Update Strategy"}),e.jsx("button",{type:"button",className:" btn-outline",onClick:()=>w(`/seller/strategy-details/${p}`),disabled:T||S,children:"Cancel"})]})]}),e.jsx(fe,{progress:S?50:0,isVisible:Q,fileName:ee,uploadType:Z})]})})};export{De as default};
