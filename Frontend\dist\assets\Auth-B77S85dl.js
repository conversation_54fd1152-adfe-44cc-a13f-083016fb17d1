const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-oGr_1a_A.js","assets/index-BicLFhOs.css"])))=>i.map(i=>d[i]);
import{u as w,a as N,b as C,r as m,j as e,L as P,c as f,l as k,t as i,g as D,d as G,_ as I}from"./index-oGr_1a_A.js";import{H as L,G as _,f as v}from"./index-BtoIuJph.js";const $=()=>{const l=w(),c=N(),{isLoading:d}=C(s=>s.auth),[r,p]=m.useState({phone:"",countryCode:"+91"}),[u,g]=m.useState({}),x=s=>{const{name:n,value:a,type:o,checked:t}=s.target;p({...r,[n]:o==="checkbox"?t:a}),u[n]&&g({...u,[n]:null})},y=s=>{p({...r,countryCode:s.target.value})},j=()=>{const s={};return r.phone.trim()?/^\d{10}$/.test(r.phone)||(s.phone="Phone number must be 10 digits"):s.phone="Phone number is required",s},b=async s=>{var a;s.preventDefault();const n=j();if(Object.keys(n).length>0){g(n);return}c(f());try{const o=`${r.countryCode}${r.phone}`,t=await c(k({mobile:o})).unwrap();i.otp.success("OTP sent successfully!"),l("/otp-verification",{state:{userId:t.userId,phoneNumber:`${r.countryCode} ${r.phone}`,cooldownSeconds:t.cooldownSeconds||60,isLogin:!0,developmentOtp:t.developmentOtp}})}catch(o){console.error("Login error:",o);let t="Login failed. Please try again.";if(typeof o=="string"?t=o:o!=null&&o.message&&(t=o.message),t.includes("wait")&&t.includes("seconds")){const h=((a=t.match(/\d+/))==null?void 0:a[0])||60;i.otp.cooldown(parseInt(h))}else t.includes("User not found")?i.error("No account found with this mobile number. Please sign up first."):i.error(t)}},S=async()=>{try{if(c(f()),!v.isInitialized()){i.error("Firebase is not initialized. Please check your configuration.");return}const s=await v.signInWithGoogle();try{const n=await c(D(s.idToken)).unwrap();if(i.auth.loginSuccess(),n.user.role==="buyer")l("/content");else if(n.user.role==="seller"){const a=G(n.user);l(a)}else n.user.role==="admin"?l("/admin/dashboard"):l("/")}catch(n){const a=typeof n=="string"?n:(n==null?void 0:n.message)||"";if(a.includes("not found")||a.includes("does not exist"))try{const{googleSignUp:o}=await I(async()=>{const{googleSignUp:t}=await import("./index-oGr_1a_A.js").then(h=>h.cA);return{googleSignUp:t}},__vite__mapDeps([0,1]));await c(o({idToken:s.idToken,role:"buyer"})).unwrap(),i.auth.registrationSuccess(),l("/content")}catch(o){console.error("Google sign-up error:",o);let t="Failed to complete registration. Please try again.";typeof o=="string"?t=o:o!=null&&o.message&&(t=o.message),i.error(t)}else throw n}}catch(s){console.error("Google sign-in error:",s);let n="Failed to sign in with Google. Please try again.";typeof s=="string"?n=s:s!=null&&s.message&&(n=s.message),i.error(n)}};return e.jsx("div",{className:"auth-page auth-container",children:e.jsxs("div",{className:"auth-form-container",children:[e.jsx("h1",{className:"auth-title",children:"Login in to your account"}),e.jsxs("form",{onSubmit:b,className:"auth-form",children:[e.jsxs("div",{className:"auth-form-input form-input-container",children:[e.jsxs("div",{className:"phone-input-wrapper",children:[e.jsx("div",{children:e.jsxs("div",{className:"country-code-select",children:[e.jsx(L,{style:{color:"var(--dark-gray)",fontSize:"var(--heading5)"}}),e.jsxs("select",{value:r.countryCode,onChange:y,className:"selectstylesnone",children:[e.jsx("option",{value:"+91",children:"+91"}),e.jsx("option",{value:"+1",children:"+1"}),e.jsx("option",{value:"+44",children:"+44"}),e.jsx("option",{value:"+61",children:"+61"}),e.jsx("option",{value:"+86",children:"+86"}),e.jsx("option",{value:"+49",children:"+49"}),e.jsx("option",{value:"+33",children:"+33"}),e.jsx("option",{value:"+81",children:"+81"}),e.jsx("option",{value:"+7",children:"+7"}),e.jsx("option",{value:"+55",children:"+55"})]})]})}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:r.phone,onChange:s=>{const n=s.target.value.replace(/\D/g,"");x({target:{name:"phone",value:n}})},placeholder:"00000 00000",className:`form-input phone-input ${u.phone?"input-error":""}`,required:!0,pattern:"[0-9]*"})]}),u.phone&&e.jsx("p",{className:"error-message",children:u.phone})]}),e.jsx("button",{type:"submit",className:"signin-button btn-primary",disabled:d,children:d?"Sending OTP...":"Send Code Now"}),e.jsx("div",{className:"auth-divider",children:e.jsx("span",{children:"or"})}),e.jsx(_,{onClick:S,isLoading:d,text:"Sign in with Google",variant:"secondary"}),e.jsxs("p",{className:"signup-link mt-10",children:["Don't have an account? ",e.jsx(P,{to:"/signup",children:"Sign Up"})]})]})]})})};export{$ as default};
