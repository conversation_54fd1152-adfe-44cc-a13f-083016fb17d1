import{a as J,r as h,j as e,bc as V,aT as K,aU as X,bf as B,b9 as W,u as Q,b as z,cw as H,aQ as Z,cx as ee,cv as se,A as ae,y as te,bR as ie,bK as G,cy as ne}from"./index-oGr_1a_A.js";const re=({formData:j,onInputChange:k,onExperienceChange:b,onAddExperience:F,onRemoveExperience:T,onNext:S,fieldErrors:n})=>{const P=J(),[y,A]=h.useState(j.profileImage||null),[o,v]=h.useState(""),[d,p]=h.useState(!1),w=["jpg","jpeg","png"],O=10*1024*1024,m=u=>{if(u.size>O)return"File size must be less than 10MB";const l=u.name.split(".").pop().toLowerCase();return w.includes(l)?["image/jpeg","image/jpg","image/png","image/gif"].includes(u.type)?null:"Invalid file type. Please select a valid image file.":`Unsupported file format. Please use: ${w.join(", ").toUpperCase()}`},g=async u=>{const l=u.target.files[0];if(l){v("");const I=m(l);if(I){v(I),u.target.value="";return}try{p(!0);const f=await P(K(l)).unwrap();if(f.success){k("profileImage",f.data.fileUrl),A(f.data.fileUrl);const N=JSON.parse(localStorage.getItem("xosportshub_user"));await P(X({profileImage:f.data.fileUrl,firstName:N.firstName,lastName:N.lastName})).unwrap(),B("Profile image uploaded successfully")}}catch(f){const N=f.message||"Failed to upload profile image";v(N),W(N)}finally{p(!1)}}};return e.jsxs("div",{className:"seller-onboarding-step1-container max-container",children:[e.jsxs("div",{className:"progress-bar",children:[e.jsx("div",{className:"step active",children:"1"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step",children:"2"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step",children:"3"})]}),e.jsxs("div",{className:"form-grid",children:[e.jsxs("div",{className:"description-section",children:[e.jsx("div",{className:"section-title required-field",children:"Description"}),e.jsxs("div",{className:"description-box",children:[e.jsx("textarea",{className:`description-textarea ${n!=null&&n.description?"error":""}`,placeholder:"Write Description..",rows:3,value:j.description,onChange:u=>k("description",u.target.value)}),(n==null?void 0:n.description)&&e.jsx("div",{className:"field-error",children:n.description})]})]}),e.jsxs("div",{className:"profile-experience-grid",children:[e.jsxs("div",{className:"profile-pic-section",children:[e.jsx("div",{className:"section-title required-field",children:"Profile Pic"}),e.jsxs("div",{className:"avatar-upload",children:[e.jsx("div",{className:"avatar-placeholder",children:y?e.jsx("img",{src:`http://localhost:5000${y}`,alt:"Profile",className:"avatar-image"}):e.jsxs("svg",{width:"64",height:"64",viewBox:"0 0 64 64",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("circle",{cx:"32",cy:"32",r:"32",fill:"var(--light-gray)"}),e.jsx("ellipse",{cx:"32",cy:"27",rx:"12",ry:"12",fill:"#fff"}),e.jsx("ellipse",{cx:"32",cy:"50",rx:"16",ry:"10",fill:"#fff"})]})}),e.jsx("input",{type:"file",id:"profileImageInput",accept:"image/jpeg,image/jpg,image/png,image/gif",onChange:g,style:{display:"none"}}),e.jsx("div",{className:"upload-buttons",children:e.jsx("button",{type:"button",className:"btn btn-outline upload-btn",onClick:()=>document.getElementById("profileImageInput").click(),disabled:d,children:d?"Uploading...":"Choose Photo"})}),e.jsx("div",{className:"upload-info",children:e.jsxs("small",{className:"upload-format-info",children:["Supported formats:"," ",w.join(", ").toUpperCase()," (Max: 10MB)"]})}),o&&e.jsx("div",{className:"field-error image-error",children:o})]})]}),e.jsxs("div",{className:"experience-section",children:[e.jsx("div",{className:"section-title required-field",children:"Experience"}),e.jsxs("div",{className:"experience-container",children:[j.experiences.map((u,l)=>{var I,f,N,U,M,R,q,E;return e.jsxs("div",{className:"experience-row",children:[e.jsxs("div",{className:"experience-row-content",children:[e.jsx("input",{type:"text",className:`input ${n!=null&&n.experiences&&l===0?"error":""}`,placeholder:"Enter Experience",value:u.schoolName,onChange:C=>b(l,"schoolName",C.target.value)}),e.jsx("input",{type:"text",className:"input",placeholder:"Enter Position",value:u.position,onChange:C=>b(l,"position",C.target.value)}),e.jsxs("div",{className:"year-fields",children:[e.jsxs("div",{children:[e.jsx("input",{type:"text",className:`input year-input ${(f=(I=n==null?void 0:n.experienceYears)==null?void 0:I[l])!=null&&f.fromYear?"error":""}`,placeholder:"From Year",value:u.fromYear,onChange:C=>b(l,"fromYear",C.target.value)}),((U=(N=n==null?void 0:n.experienceYears)==null?void 0:N[l])==null?void 0:U.fromYear)&&e.jsx("div",{className:"field-error",children:n.experienceYears[l].fromYear})]}),e.jsxs("div",{children:[e.jsx("input",{type:"text",className:`input year-input ${(R=(M=n==null?void 0:n.experienceYears)==null?void 0:M[l])!=null&&R.toYear?"error":""}`,placeholder:"To Year",value:u.toYear,onChange:C=>b(l,"toYear",C.target.value)}),((E=(q=n==null?void 0:n.experienceYears)==null?void 0:q[l])==null?void 0:E.toYear)&&e.jsx("div",{className:"field-error",children:n.experienceYears[l].toYear})]})]})]}),j.experiences.length>1&&T&&e.jsx("button",{type:"button",className:"delete-experience-btn",onClick:()=>T(l),title:"Remove this experience",children:e.jsx(V,{className:"delete-icon"})})]},l)}),(n==null?void 0:n.experiences)&&e.jsx("div",{className:"field-error",children:n.experiences}),e.jsx("div",{className:"add-more-link",onClick:F,children:"+ Add More"})]})]})]})]}),e.jsx("div",{className:"next-btn-row",children:e.jsx("button",{type:"button",className:"btn btn-primary next-btn",onClick:S,children:"Next"})})]})},D="seller_onboarding_data",$="seller_onboarding_step",de=()=>{const j=J(),k=Q(),{isLoading:b,isSuccess:F,isError:T,error:S,onboardingData:n}=z(s=>s.user),{user:P}=z(s=>s.auth),[y,A]=h.useState(()=>{const s=localStorage.getItem($);return s?parseInt(s):1}),[o,v]=h.useState(()=>{const s=localStorage.getItem(D);if(s)try{return JSON.parse(s)}catch(a){console.error("Error parsing saved onboarding data:",a)}return{description:"",selectedImageFile:null,experiences:[{schoolName:"",position:"",fromYear:"",toYear:""}],minTrainingCost:"",socialLinks:{facebook:"",instagram:"",twitter:""},sports:[],expertise:[],certifications:[],stripeConnectAccountId:"",stripeConnectOnboarding:!1}}),[d,p]=h.useState({description:"",experiences:"",minTrainingCost:"",experienceYears:{},profileImage:"",stripeConnect:""}),[w,O]=h.useState(""),[m,g]=h.useState({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""});h.useEffect(()=>{document.body.style.pointerEvents="none";const s=document.querySelector(".seller-onboarding-wrapper");return s&&(s.style.pointerEvents="all"),document.querySelectorAll(".role-dropdown").forEach(t=>{t.style.pointerEvents="all"}),()=>{document.body.style.pointerEvents="all",document.querySelectorAll(".role-dropdown").forEach(c=>{c.style.pointerEvents=""})}},[]),h.useEffect(()=>{if(F&&(g({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""}),O(""),B("Onboarding completed successfully! Welcome to XO Sports Hub!",{autoClose:4e3}),n&&j(H(n)),j(Z()).then(()=>{j(ee()),setTimeout(()=>{k("/seller/dashboard")},1500)})),T&&S){g({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""});let s="An error occurred during onboarding. Please try again.";S.message?s=S.message:S.errors&&Array.isArray(S.errors)&&(s=S.errors.map(t=>t.msg).join(", ")),O(s),W(s)}},[F,T,S,j,k,n]),h.useEffect(()=>{try{const s={...o,selectedImageFile:null};localStorage.setItem(D,JSON.stringify(s))}catch(s){console.error("Error saving onboarding data:",s)}},[o]),h.useEffect(()=>{localStorage.setItem($,y.toString())},[y]),h.useEffect(()=>{F&&(localStorage.removeItem(D),localStorage.removeItem($))},[F]);const u=(s,a)=>{v(t=>({...t,[s]:a})),d[s]&&p(t=>({...t,[s]:""}))},l=()=>{const s=new Date().getFullYear(),a=1950,t=s,c={};let r=!1;return o.experiences.forEach((i,x)=>{const L=parseInt(i.fromYear),_=parseInt(i.toYear),Y={};String(i.fromYear||"").trim()?(isNaN(L)||L<a||L>t)&&(Y.fromYear=`From year must be between ${a} and ${t}`,r=!0):(Y.fromYear="From year is required",r=!0),String(i.toYear||"").trim()?isNaN(_)||_<a||_>t?(Y.toYear=`To year must be between ${a} and ${t}`,r=!0):!isNaN(L)&&_<L&&(Y.toYear="To year must be greater than or equal to from year",r=!0):(Y.toYear="To year is required",r=!0),Object.keys(Y).length>0&&(c[x]=Y)}),r?(p(i=>({...i,experienceYears:c})),!1):(p(i=>({...i,experienceYears:{}})),!0)},I=()=>{const s={};let a=!1;return o.description.trim()||(s.description="Description is required",a=!0),(o.experiences.length===0||!o.experiences[0].schoolName)&&(s.experiences="At least one experience with school name is required",a=!0),l()||(a=!0),a?(p(c=>({...c,...s})),!1):(p({description:"",experiences:"",minTrainingCost:"",experienceYears:{},profileImage:"",stripeConnect:""}),!0)},f=()=>{let s=!1;switch(y){case 1:s=I();break;case 2:s=N();break;default:s=!0}if(s){try{const a={...o,selectedImageFile:null};localStorage.setItem(D,JSON.stringify(a))}catch(a){console.error("Error saving step progress:",a)}A(y+1)}},N=()=>{const s={};let a=!1;return o.minTrainingCost||(s.minTrainingCost="Minimum training cost is required",a=!0),a?(p(t=>({...t,...s})),!1):!0},U=(s,a)=>{v(t=>({...t,socialLinks:{...t.socialLinks,[s]:a}}))},M=(s,a,t)=>{var r,i;const c=[...o.experiences];c[s]={...c[s],[a]:t},v(x=>({...x,experiences:c})),(a==="fromYear"||a==="toYear")&&(i=(r=d.experienceYears)==null?void 0:r[s])!=null&&i[a]&&p(x=>({...x,experienceYears:{...x.experienceYears,[s]:{...x.experienceYears[s],[a]:""}}}))},R=()=>{v(s=>({...s,experiences:[...s.experiences,{schoolName:"",position:"",fromYear:"",toYear:""}]}))},q=s=>{o.experiences.length>1&&(v(a=>({...a,experiences:a.experiences.filter((t,c)=>c!==s)})),p(a=>{const t={...a.experienceYears};delete t[s];const c={};return Object.keys(t).forEach(r=>{const i=parseInt(r);i>s?c[i-1]=t[r]:c[r]=t[r]}),{...a,experienceYears:c}}))},E=async()=>{try{g(a=>({...a,isSubmittingForm:!0,uploadProgress:"Setting up Stripe Connect account..."})),p(a=>({...a,stripeConnect:""}));const s=await ie({email:P.email,firstName:P.firstName,lastName:P.lastName,context:"onboarding"});if(!s.success)throw new Error(s.message||"Failed to create Stripe Connect account");if(s.data.accountId&&s.data.onboardingUrl){v(r=>({...r,stripeConnectAccountId:s.data.accountId}));const a=window.open(s.data.onboardingUrl,"stripe-onboarding","width=800,height=600,scrollbars=yes,resizable=yes"),t=async r=>{if(r.origin===window.location.origin&&r.data&&r.data.type==="STRIPE_ONBOARDING_RESULT")if(window.removeEventListener("message",t),r.data.success)try{const i=await G(s.data.accountId);if(i.success&&i.data.detailsSubmitted)v(x=>({...x,stripeConnectOnboarding:!0})),g(x=>({...x,isSubmittingForm:!1,uploadProgress:""})),B("Stripe Connect account setup completed successfully!");else throw new Error("Onboarding verification failed")}catch{g(x=>({...x,isSubmittingForm:!1,uploadProgress:""})),p(x=>({...x,stripeConnect:"Could not verify onboarding completion. Please try again."}))}else g(i=>({...i,isSubmittingForm:!1,uploadProgress:""})),p(i=>({...i,stripeConnect:"Stripe onboarding was not completed. Please try again."}))};window.addEventListener("message",t);const c=setInterval(async()=>{if(a.closed){clearInterval(c),window.removeEventListener("message",t);try{const r=await G(s.data.accountId);r.success&&r.data.detailsSubmitted?(v(i=>({...i,stripeConnectOnboarding:!0})),g(i=>({...i,isSubmittingForm:!1,uploadProgress:""})),B("Stripe Connect account setup completed successfully!")):(g(i=>({...i,isSubmittingForm:!1,uploadProgress:""})),p(i=>({...i,stripeConnect:"Stripe onboarding was not completed. Please try again."})))}catch{g(i=>({...i,isSubmittingForm:!1,uploadProgress:""})),p(i=>({...i,stripeConnect:"Could not verify onboarding status. Please try again."}))}}},1e3)}else throw new Error("Invalid response from Stripe Connect setup")}catch(s){g(t=>({...t,isSubmittingForm:!1,uploadProgress:""}));const a=s.message||"Failed to setup Stripe Connect account. Please try again.";p(t=>({...t,stripeConnect:a})),W(a)}},C=async()=>{try{g({isUploadingImage:!0,isSubmittingForm:!1,uploadProgress:"Uploading profile image..."});let s="";if(o.selectedImageFile){const t=new FormData;t.append("file",o.selectedImageFile);const c=await j(K(t));c.payload&&c.payload.url&&(s=c.payload.url)}g({isUploadingImage:!1,isSubmittingForm:!0,uploadProgress:"Submitting onboarding information..."});const a={...o,profileImage:s||""};delete a.selectedImageFile,await j(ne(a)),localStorage.removeItem(D),localStorage.removeItem($)}catch(s){console.error("Error during onboarding submission:",s),O("Failed to complete onboarding. Please try again."),g({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""})}};return e.jsx("div",{className:"seller-onboarding-wrapper max-container",children:y===1?e.jsx(re,{formData:o,onInputChange:u,onExperienceChange:M,onAddExperience:R,onRemoveExperience:q,onNext:f,fieldErrors:d}):y===2?e.jsxs("div",{className:"seller-onboarding-step2-container",children:[e.jsxs("div",{className:"progress-bar",children:[e.jsx("div",{className:"step complete",children:"1"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step active",children:"2"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step",children:"3"})]}),e.jsxs("div",{className:"section-block",children:[e.jsx("div",{className:"section-title required-field",children:"Minimum Training Cost"}),e.jsx("input",{type:"number",className:`min-cost-input ${d!=null&&d.minTrainingCost?"error":""}`,placeholder:"Enter minimum training cost",value:o.minTrainingCost,onChange:s=>u("minTrainingCost",s.target.value)}),(d==null?void 0:d.minTrainingCost)&&e.jsx("div",{className:"field-error",children:d.minTrainingCost})]}),e.jsxs("div",{className:"section-block",children:[e.jsx("div",{className:"section-title optional-field",children:"Social Media Links"}),e.jsxs("div",{className:"social-inputs-grid",children:[e.jsxs("div",{className:"social-input-row",children:[e.jsx("div",{className:"social-icon facebook",children:e.jsx(se,{color:"#1877F2"})}),e.jsx("input",{type:"text",className:"social-input",placeholder:"Enter Facebook profile URL",value:o.socialLinks.facebook,onChange:s=>U("facebook",s.target.value)})]}),e.jsxs("div",{className:"social-input-row",children:[e.jsx("div",{className:"social-icon instagram",children:e.jsx(ae,{color:"#E4405F"})}),e.jsx("input",{type:"text",className:"social-input",placeholder:"Enter Instagram profile URL",value:o.socialLinks.instagram,onChange:s=>U("instagram",s.target.value)})]}),e.jsxs("div",{className:"social-input-row",children:[e.jsx("div",{className:"social-icon twitter",children:e.jsx(te,{color:"#000000"})}),e.jsx("input",{type:"text",className:"social-input",placeholder:"Enter Twitter profile URL",value:o.socialLinks.twitter,onChange:s=>U("twitter",s.target.value)})]})]})]}),w&&e.jsxs("div",{className:"server-error-message",children:[e.jsx("div",{className:"error-icon",children:"⚠️"}),e.jsx("div",{className:"error-text",children:w})]}),e.jsxs("div",{className:"next-btn-row",children:[e.jsx("button",{className:"btn btn-outline",onClick:()=>A(1),disabled:m.isUploadingImage||m.isSubmittingForm||b,children:"Back"}),e.jsx("button",{className:"btn btn-primary next-btn",onClick:f,disabled:m.isUploadingImage||m.isSubmittingForm||b,children:"Next"})]})]}):e.jsxs("div",{className:"seller-onboarding-step3-container",children:[e.jsxs("div",{className:"progress-bar",children:[e.jsx("div",{className:"step complete",children:"1"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step complete",children:"2"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step active",children:"3"})]}),e.jsxs("div",{className:"section-block",children:[e.jsx("div",{className:"section-title",children:"Payment Setup"}),e.jsx("div",{className:"section-subtitle",children:"Complete your payment setup to receive earnings from your content sales. We use Stripe Standard accounts for full control and flexibility."}),e.jsx("div",{className:"stripe-connect-container",children:o.stripeConnectOnboarding?e.jsxs("div",{className:"stripe-connect-success",children:[e.jsx("div",{className:"success-icon",children:"✅"}),e.jsx("h4",{children:"Payment Setup Complete!"}),e.jsx("p",{children:"Your Stripe Express account is connected and ready to receive payments."}),e.jsx("div",{className:"stripe-account-info",children:e.jsxs("small",{children:["Account ID: ",o.stripeConnectAccountId]})})]}):e.jsxs("div",{className:"stripe-connect-setup",children:[e.jsxs("div",{className:"stripe-connect-info",children:[e.jsx("h4",{children:"🏦 Fast & Secure Payment Processing"}),e.jsxs("ul",{children:[e.jsx("li",{children:"✅ Stripe Express account with streamlined setup"}),e.jsx("li",{children:"✅ Quick onboarding process"}),e.jsx("li",{children:"✅ Automatic tax handling and reporting"}),e.jsx("li",{children:"✅ Built-in fraud protection"})]}),e.jsx("div",{className:"action-required",children:"⚡ Action Required: Set up your payment account to start accepting payments"})]}),e.jsxs("button",{className:"btn btn-stripe-connect",onClick:()=>E(),disabled:m.isUploadingImage||m.isSubmittingForm||b,children:[e.jsx("span",{className:"lock-icon",children:"🔒"})," Setup Stripe Express Account"]}),d.stripeConnect&&e.jsxs("div",{className:"field-error",children:[d.stripeConnect,(d.stripeConnect.includes("not completed")||d.stripeConnect.includes("was closed")||d.stripeConnect.includes("Could not verify"))&&e.jsx("button",{className:"btn btn-outline",onClick:()=>{p(s=>({...s,stripeConnect:""})),E()},disabled:m.isUploadingImage||m.isSubmittingForm||b,style:{marginLeft:"10px",padding:"5px 15px",fontSize:"14px"},children:"Try Again"})]})]})})]}),w&&e.jsxs("div",{className:"server-error-message",children:[e.jsx("div",{className:"error-icon",children:"⚠️"}),e.jsx("div",{className:"error-text",children:w})]}),e.jsxs("div",{className:"next-btn-row",children:[e.jsx("button",{className:"btn btn-outline",onClick:()=>A(2),disabled:m.isUploadingImage||m.isSubmittingForm||b,children:"Back"}),e.jsx("button",{className:"btn btn-primary next-btn",onClick:C,disabled:!o.stripeConnectOnboarding||m.isUploadingImage||m.isSubmittingForm||b,children:m.isUploadingImage||m.isSubmittingForm||b?m.uploadProgress||"Processing...":"Complete Onboarding"})]})]})})};export{de as default};
