import{ac as X,u as G,a as J,b as K,r as n,c1 as U,a9 as c,j as e,bV as Q,bc as Z,au as d,c4 as ee,ae as F,c5 as C,c6 as te,bX as se}from"./index-oGr_1a_A.js";import{S}from"./SellerLayout-Du2JNoeS.js";import{D as ae}from"./DocumentViewer-B7AtyaWY.js";import{F as le}from"./FullscreenPreview-vQ1ld1so.js";import{o as T}from"./ourmissionimage-Cdyij9G6.js";import{C as ie}from"./ConfirmationModal-D5BYMx4l.js";import{P as v}from"./PreviewContent-Dc02eTnl.js";import{a as re}from"./dateValidation-YzamylQ0.js";import"./index-auB_fJid.js";import"./timezoneUtils-Dky5bF8c.js";const xe=()=>{var w,P;const{id:r}=X(),h=G(),_=J(),{singleContent:a,isLoading:E,error:g}=K(s=>s.content),[L,y]=n.useState(!1),[V,j]=n.useState(!1),[k,u]=n.useState(!1),[x,f]=n.useState(!1),[D,ne]=n.useState(!1),[I,$]=n.useState(null),[M,N]=n.useState(!1);n.useEffect(()=>{r&&(!a||a._id!==r)&&_(U(r))},[_,r,a]),n.useEffect(()=>{const s=()=>{const l=sessionStorage.getItem("previousPath");r&&document.visibilityState==="visible"&&l&&l.includes("/edit")&&(_(U(r)),sessionStorage.removeItem("previousPath"))};return document.addEventListener("visibilitychange",s),()=>{document.removeEventListener("visibilitychange",s)}},[_,r]),n.useEffect(()=>{g&&c.error(g.message||"Failed to load strategy details")},[g]);const O=()=>{sessionStorage.setItem("previousPath",`/seller/strategy-details/${r}/edit`),h(`/seller/strategy-details/${r}/edit`)},q=()=>{y(!0)},A=async()=>{j(!0);try{await _(se(r)).unwrap(),c.success("Strategy deleted successfully"),h("/seller/my-sports-strategies")}catch(s){console.error("Delete failed:",s),c.error(s.message||"Failed to delete strategy")}finally{j(!1),y(!1)}},B=()=>{if(a!=null&&a.fileUrl){const s=a.fileUrl.split(".").pop().toLowerCase();["mp4","avi","mov","wmv","flv","webm","mkv"].includes(s)?(f(!x),x||u(!0)):z()}else c.error("No file available")},z=()=>{c.error("Download functionality has been disabled for security purposes")},b=()=>{a!=null&&a.fileUrl?N(!0):c.error("No file available for preview")},p=()=>{if(!(a!=null&&a.fileUrl))return!1;const s=a.fileUrl.split(".").pop().toLowerCase();return["mp4","avi","mov","wmv","flv","webm","mkv"].includes(s)},R=()=>a!=null&&a.fileUrl?a.contentType==="PDF"||a.contentType==="Document"||a.fileUrl.toLowerCase().endsWith(".pdf"):!1,Y=s=>re(s),H=s=>{var i;let l;return(s==null?void 0:s.saleType)==="Auction"&&((i=s==null?void 0:s.auctionDetails)!=null&&i.basePrice)?l=s.auctionDetails.basePrice:l=s==null?void 0:s.price,typeof l=="number"?`$${l.toFixed(2)}`:"$0.00"},W=s=>{if(!s)return"N/A";const l=Math.floor(s/60),i=s%60;return l>0?`${l}:${i.toString().padStart(2,"0")}:00`:`${i}:00`},o=()=>{var i;if(!a||a.saleType!=="Auction")return!1;const s=new Date,l=(i=a.auctionDetails)!=null&&i.auctionStartDate?new Date(a.auctionDetails.auctionStartDate):null;return l&&s>=l};if(E)return e.jsx(S,{children:e.jsx("div",{className:"StrategyDetails",children:e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading strategy details..."})]})})});if(!a)return e.jsx(S,{children:e.jsx("div",{className:"StrategyDetails",children:e.jsxs("div",{className:"error-container",children:[e.jsx("h3",{children:"Strategy not found"}),e.jsx("p",{children:"The strategy you're looking for doesn't exist or has been removed."}),e.jsx("button",{className:"btn btn-primary",onClick:()=>h("/seller/my-sports-strategies"),children:"Back to Strategies"})]})})});const t=a;return e.jsx(S,{children:e.jsxs("div",{className:"StrategyDetails",children:[e.jsxs("div",{className:"StrategyDetails__info-header",children:[e.jsx("div",{className:"StrategyDetails__info-content",children:e.jsx("h2",{className:"StrategyDetails__info-title",children:"Video/Document Info"})}),e.jsxs("div",{className:"StrategyDetails__info-actions",children:[e.jsxs("button",{className:`StrategyDetails__edit-btn ${o()?"disabled":""}`,onClick:o()?void 0:O,disabled:o(),title:o()?"You can't edit the strategy once the auction has started.":"Edit Strategy",style:{cursor:o()?"not-allowed":"pointer",opacity:o()?.6:1},children:[e.jsx(Q,{className:"StrategyDetails__action-icon"}),"Edit"]}),e.jsxs("button",{className:"StrategyDetails__delete-btn",onClick:q,children:[e.jsx(Z,{className:"StrategyDetails__action-icon"}),"Delete"]})]})]}),e.jsxs("div",{className:"StrategyDetails__content",children:[e.jsx("h3",{className:"StrategyDetails__strategy-title",children:t.title}),e.jsx("div",{className:"StrategyDetails__media-container",children:x&&p()?e.jsxs("div",{className:"StrategyDetails__video-wrapper",children:[e.jsxs("video",{className:"StrategyDetails__video",controls:!0,autoPlay:k,controlsList:"nodownload nofullscreen noremoteplayback",disablePictureInPicture:!0,onPlay:()=>u(!0),onPause:()=>u(!1),onError:s=>{console.error("Video error:",s);const l=s.target.src,i=d(D?t.fileUrl:t.previewUrl||t.fileUrl),m=ee(D?t.fileUrl:t.previewUrl||t.fileUrl);l===i&&m&&m!==i?(console.log("Trying fallback URL:",m),$(m),s.target.src=m,s.target.load()):c.error("Failed to load video. Please try again.")},onLoadStart:()=>console.log("Video loading started"),onCanPlay:()=>console.log("Video can play"),poster:d(t.thumbnailUrl||t.previewUrl)||T,children:[e.jsx("source",{src:I||d(D?t.fileUrl:t.previewUrl||t.fileUrl),type:"video/mp4"}),"Your browser does not support the video tag."]}),e.jsxs("div",{className:"StrategyDetails__video-controls",children:[e.jsxs("button",{className:"StrategyDetails__preview-btn",onClick:b,title:"Open video in fullscreen preview",children:[e.jsx(F,{})," Preview Video"]}),e.jsx("button",{className:"StrategyDetails__control-btn",onClick:()=>f(!1),title:"Hide Video",children:"Close Video"})]})]}):t.contentType==="PDF"||t.contentType==="Document"?e.jsxs("div",{className:"StrategyDetails__document-container",children:[e.jsx("div",{className:"StrategyDetails__document-viewer",children:e.jsx(ae,{fileUrl:d(t.fileUrl),fileName:((w=t.fileUrl)==null?void 0:w.split("/").pop())||"document",title:t.title,className:"StrategyDetails__document-element",height:"500px",showDownload:!1})}),e.jsx("div",{className:"StrategyDetails__document-controls",children:e.jsxs("button",{className:"StrategyDetails__preview-btn",onClick:b,title:"Open PDF in fullscreen preview",children:[e.jsx(F,{})," Preview PDF"]})})]}):e.jsxs("div",{className:"StrategyDetails__image-container",children:[e.jsx("img",{src:d(t.thumbnailUrl||t.previewUrl)||T,alt:t.title,className:"StrategyDetails__image"}),e.jsx("div",{className:"StrategyDetails__play-overlay",onClick:B,title:p()?t.previewUrl&&t.previewUrl!==t.fileUrl?"Play Preview (10 seconds)":"Play Video":t.fileUrl?"Download File":"No file available",children:t.fileUrl?e.jsx(e.Fragment,{children:p()?e.jsxs(e.Fragment,{children:[e.jsx(C,{className:"StrategyDetails__play-icon"}),e.jsx("span",{className:"StrategyDetails__overlay-text",children:t.previewUrl&&t.previewUrl!==t.fileUrl?"Play Preview":"Play Video"})]}):e.jsxs(e.Fragment,{children:[e.jsx(te,{className:"StrategyDetails__play-icon"}),e.jsx("span",{className:"StrategyDetails__overlay-text",children:"Download"})]})}):e.jsx(C,{className:"StrategyDetails__play-icon"})})]})}),e.jsxs("div",{className:"StrategyDetails__description-section",children:[e.jsx("h3",{className:"StrategyDetails__section-title",children:"Description"}),e.jsx(v,{html:t.description,className:"StrategyDetails__description",ariaLabel:"Strategy description preview"})]}),e.jsxs("div",{className:"StrategyDetails__coach-section",children:[e.jsx("h3",{className:"StrategyDetails__section-title",children:"The Coach"}),e.jsx("div",{className:"StrategyDetails__coach-info",children:e.jsxs("div",{className:"StrategyDetails__coach-details",children:[e.jsx("h4",{className:"StrategyDetails__coach-name",children:t.coachName||"Coach Name"}),e.jsxs("p",{className:"StrategyDetails__coach-title",children:[t.sport," Specialist"]}),e.jsx(v,{html:t.aboutCoach,className:"StrategyDetails__coach-description",ariaLabel:"Coach description preview"})]})})]}),e.jsxs("div",{className:"StrategyDetails__stats-and-steps",children:[e.jsxs("div",{className:"StrategyDetails__stats-section",children:[e.jsx("h3",{className:"StrategyDetails__section-title",children:"Strategic Content Info"}),e.jsxs("div",{className:"StrategyDetails__stats-grid",children:[e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Category"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.category})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Sport"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.sport})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Duration"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:W(t.duration)})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Price"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:H(t)})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Content Type"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.contentType})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Difficulty"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.difficulty})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Status"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:t.status})]}),e.jsxs("div",{className:"StrategyDetails__stat-content",children:[e.jsx("span",{className:"StrategyDetails__stat-label",children:"Created"}),e.jsx("span",{className:"StrategyDetails__stat-value",children:Y(t.createdAt)})]})]})]}),e.jsx("div",{className:"vertical-line"}),e.jsxs("div",{className:"StrategyDetails__steps-section",children:[e.jsx("h3",{className:"StrategyDetails__section-title",children:"This strategic content Includes"}),e.jsxs("div",{className:"StrategyDetails__steps-list",children:[e.jsx("div",{className:"StrategyDetails__step",children:e.jsx(v,{html:t.strategicContent,className:"StrategyDetails__step-text",ariaLabel:"Strategic content preview"})}),t.tags&&t.tags.length>0&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Tags:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.tags.join(", ")})]}),t.prerequisites&&t.prerequisites.length>0&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Prerequisites:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.prerequisites.join(", ")})]}),t.learningObjectives&&t.learningObjectives.length>0&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Learning Objectives:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.learningObjectives.join(", ")})]}),t.equipment&&t.equipment.length>0&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Equipment Needed:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.equipment.join(", ")})]}),e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Language:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.language})]}),e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"Sale Type:"}),e.jsx("span",{className:"StrategyDetails__step-text",children:t.saleType})]}),t.fileSize&&e.jsxs("div",{className:"StrategyDetails__step",children:[e.jsx("span",{className:"StrategyDetails__step-label",children:"File Size:"}),e.jsxs("span",{className:"StrategyDetails__step-text",children:[(t.fileSize/1024/1024).toFixed(2)," MB"]})]})]})]})]})]}),e.jsx(ie,{isOpen:L,onClose:()=>y(!1),onConfirm:A,title:"Delete Strategy",message:`Are you sure you want to delete "${t==null?void 0:t.title}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",type:"danger",isLoading:V}),e.jsx(le,{isOpen:M,onClose:()=>N(!1),fileUrl:d(t==null?void 0:t.fileUrl),fileName:((P=t==null?void 0:t.fileUrl)==null?void 0:P.split("/").pop())||"file",fileType:p()?"video":R()?"pdf":"unknown",contentType:t==null?void 0:t.contentType,title:t==null?void 0:t.title,showDownload:!1})]})})};export{xe as default};
