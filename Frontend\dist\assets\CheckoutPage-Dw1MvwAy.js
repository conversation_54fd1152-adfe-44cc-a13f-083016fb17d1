import{a as ne,r as c,b as M,aE as oe,aF as de,aG as me,j as e,aH as ue,aI as he,a9 as g,aJ as pe,ac as ye,u as fe,ak as xe,I as te,a3 as se,J as je}from"./index-oGr_1a_A.js";import{s as Ne}from"./stripe-DT3_Ek51.js";import{L as ve}from"./LoadingSkeleton-DmDWH340.js";import{a as re}from"./dateValidation-YzamylQ0.js";/* empty css                     *//* empty css                        */import"./timezoneUtils-Dky5bF8c.js";const ge=({order:i,onSuccess:m,onError:C,onCancel:p})=>{const f=ne(),x=c.useRef(null),j=c.useRef(null),o=c.useRef(null),{paymentIntent:a,isLoading:_}=M(t=>t.payment),{cards:d}=M(t=>t.cards),[P,S]=c.useState(!1),[k,F]=c.useState(!1),[Y,u]=c.useState(null),[w,$]=c.useState(!1),[T,v]=c.useState(!1),[s,y]=c.useState("new"),[b,E]=c.useState(""),[N,R]=c.useState({name:"",email:""});c.useEffect(()=>{(async()=>{try{const r=await Ne;if(!r)throw new Error("Stripe failed to load");x.current=r,$(!0)}catch{console.error("Failed to load payment form. Please refresh the page.")}})()},[]),c.useEffect(()=>(w&&(async()=>{if((s==="new"||!d||d.length===0)&&x.current){if(v(!0),o.current){try{o.current.destroy()}catch{console.log("Card element already destroyed")}o.current=null}try{const l=x.current.elements().create("card",{style:{base:{color:"#424770",fontFamily:"inherit",fontSmoothing:"antialiased",fontSize:"16px","::placeholder":{color:"#aab7c4"}},invalid:{color:"#dc2626",iconColor:"#dc2626"},complete:{color:"#16a34a",iconColor:"#16a34a"}},hidePostalCode:!0});o.current=l,setTimeout(()=>{if(j.current&&o.current)try{l.mount(j.current),l.on("change",h=>{F(h.complete),u(h.error?h.error.message:null)}),l.on("ready",()=>{v(!1)}),l.on("focus",()=>{u(null)})}catch(h){console.error("Error mounting card element:",h),u("Failed to load payment form. Please refresh the page."),v(!1)}},200)}catch(r){console.error("Error creating card element:",r),u("Failed to initialize payment form. Please refresh the page."),v(!1)}}else v(!1)})(),()=>{if(o.current){try{o.current.destroy()}catch{console.log("Cleanup - card element already destroyed")}o.current=null}}),[s,d,w]),c.useEffect(()=>{f(oe())},[f]),c.useEffect(()=>{if(d&&d.length>0){const t=d.find(r=>r.isDefault);t&&(y("saved"),E(t._id))}},[d]),c.useEffect(()=>{i&&i._id&&f(de())},[f,i==null?void 0:i._id]),c.useEffect(()=>{i&&i._id&&!a&&f(me({orderId:i._id})).unwrap().then(()=>{}).catch(t=>{console.error("Error creating payment intent:",t)})},[f,i==null?void 0:i._id,a]);const I=t=>{R({...N,[t.target.name]:t.target.value})},L=async t=>{if(t.preventDefault(),!x.current||!a){u("Payment system not ready. Please try again.");return}if(s==="new"){if(!o.current){u("Payment system not ready. Please try again.");return}if(!k){u("Please complete your card information");return}}else if(s==="saved"&&!b){u("Please select a payment method");return}if(!N.name.trim()){g.error("Please enter the cardholder name");return}S(!0),u(null);try{let r;if(s==="saved"){const l=d.find(U=>U._id===b);if(!l)throw new Error("Selected payment method not found");const{error:h,paymentIntent:O}=await x.current.confirmCardPayment(a.clientSecret,{payment_method:l.stripePaymentMethodId});if(h)throw new Error(h.message);r=O}else{const{error:l,paymentIntent:h}=await x.current.confirmCardPayment(a.clientSecret,{payment_method:{card:o.current,billing_details:{name:N.name,email:N.email}}});if(l)throw new Error(l.message);r=h}if(r.status==="succeeded"){const l=await f(pe({orderId:i._id,paymentIntentId:r.id})).unwrap();g.success("Payment successful!"),S(!1),m&&m(l)}}catch(r){u(r.message||"An error occurred while processing your payment"),S(!1),C&&C(r)}},A=()=>{p&&p()};return i?e.jsx("div",{className:"stripe-payment-container",children:e.jsxs("div",{className:"stripe-payment-form",children:[e.jsxs("div",{className:"payment-header",children:[e.jsx("h3",{children:"Payment Information"}),e.jsx("p",{children:"Complete your purchase securely with Stripe"})]}),e.jsxs("form",{onSubmit:L,className:"payment-form",children:[d&&d.length>0&&e.jsxs("div",{className:"payment-method-selection",children:[e.jsx("h4",{children:"Choose Payment Method"}),e.jsxs("div",{className:"saved-cards-section",children:[e.jsxs("div",{className:"payment-option",children:[e.jsx("input",{type:"radio",id:"saved-card",name:"paymentMethod",value:"saved",checked:s==="saved",onChange:t=>y(t.target.value)}),e.jsx("label",{htmlFor:"saved-card",children:"Use saved card"})]}),s==="saved"&&e.jsx("div",{className:"saved-cards-list",children:d.map(t=>{var r;return e.jsxs("div",{className:"saved-card-item",children:[e.jsx("input",{type:"radio",id:`card-${t._id}`,name:"selectedCard",value:t._id,checked:b===t._id,onChange:l=>E(l.target.value)}),e.jsx("label",{htmlFor:`card-${t._id}`,className:"card-label",children:e.jsxs("div",{className:"card-info",children:[e.jsx(ue,{className:"card-icon"}),e.jsxs("div",{className:"card-details",children:[e.jsxs("div",{className:"cards-details-style",children:[e.jsxs("span",{className:"card-number",children:["**** **** **** ",t.lastFourDigits]}),e.jsxs("div",{className:"gap-10 flex",children:[e.jsx("span",{className:"card-type",children:(r=t.cardType)==null?void 0:r.toUpperCase()}),e.jsxs("span",{className:"card-expiry",children:[t.expiryMonth,"/",t.expiryYear]})]})]}),t.isDefault&&e.jsx("span",{className:"default-badge",children:"Default"})]})]})})]},t._id)})})]}),e.jsxs("div",{className:"payment-option",children:[e.jsx("input",{type:"radio",id:"new-card",name:"paymentMethod",value:"new",checked:s==="new",onChange:t=>y(t.target.value)}),e.jsxs("label",{htmlFor:"new-card",children:[e.jsx(he,{className:"plus-icon"}),"Use new card"]})]})]}),e.jsxs("div",{className:"billing-details",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"cardholder-name",children:"Cardholder Name *"}),e.jsx("input",{type:"text",id:"cardholder-name",name:"name",value:N.name,onChange:I,placeholder:"Enter cardholder name",className:"form-input",required:!0})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"email",children:"Email (optional)"}),e.jsx("input",{type:"email",id:"email",name:"email",value:N.email,onChange:I,placeholder:"Enter email for receipt",className:"form-input"})]})]}),(s==="new"||!d||d.length===0)&&e.jsxs("div",{className:"form-group",children:[e.jsx("label",{children:"Card Information *"}),e.jsxs("div",{className:"card-element-container",children:[T&&e.jsxs("div",{className:"card-element-loading",children:[e.jsx("span",{className:"spinner"}),e.jsx("span",{children:"Loading payment form..."})]}),e.jsx("div",{ref:j,className:"stripe-card-element"})]})]}),e.jsxs("div",{className:"order-summary-payment",children:[e.jsxs("div",{className:"summary-row",children:[e.jsx("span",{children:"Content Price:"}),e.jsxs("span",{children:["$",(i.amount||0).toFixed(2)]})]}),e.jsxs("div",{className:"summary-row total",children:[e.jsx("span",{children:"Total:"}),e.jsxs("span",{children:["$",(i.amount||0).toFixed(2)]})]}),i.platformFee>0&&e.jsx("div",{className:"fee-explanation",children:e.jsx("small",{children:"Platform fee is deducted from seller earnings"})})]}),e.jsxs("div",{className:"payment-actions",children:[e.jsx("button",{type:"button",onClick:A,className:"btn-secondary cancel-btn",disabled:P,children:"Cancel"}),e.jsx("button",{type:"submit",disabled:!w||P||_||s==="new"&&!k||s==="saved"&&!b,className:`btn-primary pay-btn ${P?"processing":""}`,children:P||_?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"spinner"}),"Processing..."]}):`Pay $${(i.amount||0).toFixed(2)}`})]})]}),e.jsx("div",{className:"security-notice",children:e.jsx("p",{children:"🔒 Your payment information is secure and encrypted"})})]})}):e.jsx("div",{className:"stripe-payment-form",children:e.jsx("div",{className:"payment-error",children:"No order information available. Please try again."})})},_e=()=>{var F,Y,u,w,$,T,v;const{orderId:i}=ye(),m=fe(),C=ne(),{user:p}=M(s=>s.auth),{isLoading:f,error:x}=M(s=>s.order),[j,o]=c.useState("loading"),[a,_]=c.useState(null);c.useEffect(()=>{if(!p){g.error("Please log in to complete your purchase"),m("/login");return}if((p.role==="admin"?p.role:p.activeRole||p.role)!=="buyer"&&p.role!=="admin"){g.error("Only buyers can make purchases"),m("/");return}if(!xe.isValidId(i)){console.error("Invalid order ID:",i),g.error("Invalid order ID. Please try creating a new order."),m("/buyer/dashboard");return}C(te(i)).unwrap().then(y=>{_(y.data),o("payment")}).catch(y=>{console.error("Error fetching order:",y),g.error("Order not found or you do not have permission to view it"),m("/buyer/dashboard")})},[C,i,p,m]);const d=async s=>{var y,b,E,N,R,I,L,A,t,r,l,h,O,U,B,G,z,V,J,q,H,K,Q,W,X,Z,ee;g.success("Payment completed successfully!"),o("success");try{const n=(await C(te(a._id)).unwrap()).data,ie=D=>({visa:"Visa",mastercard:"Mastercard",amex:"American Express",discover:"Discover",diners:"Diners Club",jcb:"JCB",unionpay:"UnionPay",unknown:"Card"})[D==null?void 0:D.toLowerCase()]||"Card Payment",ce=D=>D?`**** **** **** ${D}`:"**** **** **** ****",le={orderId:`#${((y=n._id)==null?void 0:y.slice(-8))||"12345678"}`,date:re(n.createdAt||Date.now()),time:new Date(n.createdAt||Date.now()).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),items:1,totalAmount:`$${n.amount||"0.00"}`,customerDetails:{name:(b=n.buyer)!=null&&b.firstName&&((E=n.buyer)!=null&&E.lastName)?`${n.buyer.firstName} ${n.buyer.lastName}`:((N=n.buyer)==null?void 0:N.name)||"Customer",email:((R=n.buyer)==null?void 0:R.email)||"<EMAIL>",phone:((I=n.buyer)==null?void 0:I.mobile)||((L=n.buyer)==null?void 0:L.phone)||"Not provided"},paymentDetails:{method:ie((A=n.cardDetails)==null?void 0:A.cardType),cardNumber:ce((t=n.cardDetails)==null?void 0:t.lastFourDigits),cardType:((r=n.cardDetails)==null?void 0:r.cardType)||"unknown"},itemInfo:{title:((l=n.content)==null?void 0:l.title)||"Digital Content",category:((h=n.content)==null?void 0:h.category)||((O=n.content)==null?void 0:O.sport)||"Sports Content",image:((U=n.content)==null?void 0:U.thumbnail)||((B=n.content)==null?void 0:B.thumbnailUrl)||"https://via.placeholder.com/80x80/f0f0f0/666666?text=Content"},fullOrder:n,paymentResult:s};setTimeout(()=>{m("/thank-you",{state:{orderData:le}})},2e3)}catch(ae){console.error("Error fetching updated order:",ae);const n={orderId:`#${((G=a._id)==null?void 0:G.slice(-8))||"12345678"}`,date:re(a.createdAt||Date.now()),time:new Date(a.createdAt||Date.now()).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),items:1,totalAmount:`$${a.amount||"0.00"}`,customerDetails:{name:(z=a.buyer)!=null&&z.firstName&&((V=a.buyer)!=null&&V.lastName)?`${a.buyer.firstName} ${a.buyer.lastName}`:((J=a.buyer)==null?void 0:J.name)||"Customer",email:((q=a.buyer)==null?void 0:q.email)||"<EMAIL>",phone:((H=a.buyer)==null?void 0:H.mobile)||((K=a.buyer)==null?void 0:K.phone)||"Not provided"},paymentDetails:{method:"Card Payment",cardNumber:"**** **** **** ****",cardType:"unknown"},itemInfo:{title:((Q=a.content)==null?void 0:Q.title)||"Digital Content",category:((W=a.content)==null?void 0:W.category)||((X=a.content)==null?void 0:X.sport)||"Sports Content",image:((Z=a.content)==null?void 0:Z.thumbnail)||((ee=a.content)==null?void 0:ee.thumbnailUrl)||"https://via.placeholder.com/80x80/f0f0f0/666666?text=Content"},fullOrder:a,paymentResult:s};setTimeout(()=>{m("/thank-you",{state:{orderData:n}})},200)}},P=s=>{console.error("Payment error:",s),g.error(s.message||"Payment failed. Please try again."),o("error")},S=()=>{var s;m(`/buyer/details/${((s=a==null?void 0:a.content)==null?void 0:s._id)||(a==null?void 0:a.content)}`)},k=()=>{o("payment")};return f||j==="loading"?e.jsx(ve,{type:"checkout"}):x||!a?e.jsx(se,{title:"Order Not Found",message:x||"The order you're looking for doesn't exist or you don't have permission to view it.",onRetry:()=>m("/buyer/dashboard"),retryText:"Go to Dashboard"}):a.buyer._id!==p._id&&a.buyer!==p._id?e.jsx(se,{title:"Access Denied",message:"You don't have permission to view this order.",onRetry:()=>m("/buyer/dashboard"),retryText:"Go to Dashboard"}):a.paymentStatus==="Completed"?e.jsx("div",{className:"checkout-page",children:e.jsx("div",{className:"max-container",children:e.jsx("div",{children:e.jsxs("div",{className:"order-already-paid",children:[e.jsx("h2",{children:"Order Already Paid"}),e.jsx("p",{children:"This order has already been completed."}),e.jsx("button",{className:"btn-primary",onClick:()=>m("/buyer/downloads"),children:"View Downloads"})]})})})}):e.jsx("div",{className:"checkout-page",children:e.jsx("div",{className:"max-container",children:e.jsxs("div",{className:"checkout-content",children:[e.jsx("div",{className:"checkout-left",children:e.jsxs("div",{className:"checkout-form-container",children:[e.jsx("h1",{className:"checkout-title",children:"Complete Your Purchase"}),j==="loading"&&e.jsx("div",{className:"payment-loading",children:e.jsx("p",{children:"Loading order details..."})}),j==="payment"&&a?e.jsx(e.Fragment,{children:e.jsx(ge,{order:a,onSuccess:d,onError:P,onCancel:S})}):e.jsx("div",{className:"debug-info"}),j==="success"&&e.jsxs("div",{className:"payment-success",children:[e.jsx("div",{className:"success-icon",children:"✅"}),e.jsx("h3",{children:"Payment Successful!"}),e.jsx("p",{children:"Your payment has been processed successfully. Redirecting..."})]}),j==="error"&&e.jsxs("div",{className:"payment-error",children:[e.jsx("div",{className:"error-icon",children:"❌"}),e.jsx("h3",{children:"Payment Failed"}),e.jsx("p",{children:"There was an issue processing your payment. Please try again."}),e.jsx("button",{className:"btn-primary retry-btn",onClick:k,children:"Retry Payment"})]})]})}),e.jsx("div",{className:"checkout-right",children:e.jsxs("div",{className:"order-summary",children:[e.jsx("h2",{className:"order-title",children:"Order Summary"}),e.jsxs("div",{className:"rightbackgrounddiv",children:[e.jsxs("div",{className:"item-info-section",children:[e.jsx("h3",{className:"item-info-title",children:"Item Details"}),e.jsxs("div",{className:"item-details",children:[e.jsx("div",{className:"item-image",children:e.jsx("img",{src:(F=a.content)!=null&&F.thumbnailUrl?`${je}${a.content.thumbnailUrl}`:"https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG",alt:((Y=a.content)==null?void 0:Y.title)||"Content",className:"item-thumbnail"})}),e.jsxs("div",{className:"item-description",children:[e.jsx("h4",{className:"item-name",children:((u=a.content)==null?void 0:u.title)||"Content Title"}),e.jsxs("p",{className:"item-coach",children:["By ",((w=a.content)==null?void 0:w.coachName)||"Coach"]}),e.jsx("p",{className:"item-type",children:(($=a.content)==null?void 0:$.contentType)||"Digital Content"})]})]})]}),e.jsxs("div",{className:"order-info-section",children:[e.jsx("h3",{className:"order-info-title",children:"Order Information"}),e.jsxs("div",{className:"order-details",children:[e.jsxs("div",{className:"order-row",children:[e.jsx("span",{children:"Order ID:"}),e.jsxs("span",{children:["#",(T=a._id)==null?void 0:T.slice(-8).toUpperCase()]})]}),e.jsxs("div",{className:"order-row",children:[e.jsx("span",{children:"Order Type:"}),e.jsx("span",{children:a.orderType})]}),e.jsxs("div",{className:"order-row",children:[e.jsx("span",{children:"Status:"}),e.jsx("span",{className:`status ${(v=a.status)==null?void 0:v.toLowerCase()}`,children:a.status})]})]})]}),e.jsxs("div",{className:"pricing-section",children:[e.jsxs("div",{className:"price-row",children:[e.jsx("span",{className:"price-label",children:"Content Price"}),e.jsxs("span",{className:"price-value",children:["$",(a.amount||0).toFixed(2)]})]}),e.jsxs("div",{className:"price-row total-row",children:[e.jsx("span",{className:"price-label",children:"Total"}),e.jsxs("span",{className:"price-value",children:["$",(a.amount||0).toFixed(2)]})]}),a.platformFee>0&&e.jsx("div",{className:"fee-explanation",children:e.jsx("small",{children:"You pay the listed price. Platform fee is deducted from seller earnings."})})]})]})]})})]})})})};export{_e as default};
