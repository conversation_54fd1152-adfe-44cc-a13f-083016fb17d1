import{bZ as he,a as ge,u as ye,b as H,r as d,j as e,b_ as J,aM as W,aN as Y,b9 as j,b$ as fe,bf as B,c0 as X}from"./index-oGr_1a_A.js";/* empty css                       *//* empty css                   */import{S as K}from"./SellerLayout-Du2JNoeS.js";import{S as k,i as Z,g as _e,T as Q,a as ee,U as Se,v as xe}from"./TimezoneErrorBoundary-CRcqZhiK.js";import{a as te,b as x}from"./timezoneUtils-Dky5bF8c.js";import{g as be}from"./settingsService-mpdGABki.js";var je=he();const y={TITLE:{maxChars:100},COACH_NAME:{maxChars:100}},Pe=()=>{var G;const C=ge(),S=ye(),{isLoading:A,isError:ae,error:F}=H(s=>s.content),{user:p}=H(s=>s.auth),[t,u]=d.useState({title:"",category:"",coachName:"",description:"",fileUrl:"",aboutCoach:"",strategicContent:"",sport:"Basketball",contentType:"",thumbnailUrl:"",tags:[],difficulty:"",saleType:"",price:"",allowCustomRequests:!1,auctionDetails:{basePrice:"",auctionStartDate:"",auctionEndDate:"",minimumBidIncrement:"",allowOfferBeforeAuctionStart:!1},previewUrl:"",duration:"",videoLength:"",fileSize:"",prerequisites:[],learningObjectives:[],equipment:[],customRequestPrice:"",status:"Published",visibility:"Public"}),[b,T]=d.useState(""),[h,w]=d.useState(null),[ie,N]=d.useState(!1),[se,D]=d.useState(""),[re,v]=d.useState(""),[q,M]=d.useState(null),[ne,O]=d.useState(!1),[R,P]=d.useState(""),[le,E]=d.useState(!0),[r,g]=d.useState({}),[ce,$]=d.useState(!1),[m,z]=d.useState(!1),[oe,de]=d.useState(0);if(d.useEffect(()=>{(async()=>{try{const a=await be();a.data.success&&de(a.data.data.platformCommission)}catch(a){console.error("Error fetching initial data:",a),j("Failed to load required data")}})()},[]),d.useEffect(()=>{p&&(()=>{var i;if(!((i=p==null?void 0:p.paymentInfo)==null?void 0:i.stripeConnectId)){j("Complete your payout setup to create content.",{autoClose:!1,closeOnClick:!1,closeButton:!0});const l=window.confirm(`⚠️ Payment Setup Required

You need to complete your payment setup before creating content. This ensures you can receive payments from your sales.

Would you like to go to Payment Settings now?`);return S(l?"/seller/payment-settings":"/seller/my-sports-strategies"),!1}return!0})()},[p,S]),p&&!((G=p==null?void 0:p.paymentInfo)!=null&&G.stripeConnectId))return e.jsx(K,{children:e.jsx("div",{className:"payment-setup-required",children:e.jsxs("div",{className:"payment-setup-card",children:[e.jsx("div",{className:"payment-setup-icon",children:"🏦"}),e.jsx("h2",{children:"Payment Setup Required"}),e.jsx("p",{children:"You need to complete your payment setup before creating content. This ensures you can receive payments from your content sales."}),e.jsxs("div",{className:"payment-setup-benefits",children:[e.jsxs("div",{className:"benefit-item",children:[e.jsx("span",{className:"benefit-icon",children:"✅"}),e.jsx("span",{children:"Secure payments powered by Stripe"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("span",{className:"benefit-icon",children:"✅"}),e.jsx("span",{children:"Automatic payouts to your bank account"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("span",{className:"benefit-icon",children:"✅"}),e.jsxs("span",{children:["You keep ",100-oe,"% of each sale"]})]})]}),e.jsxs("div",{className:"payment-setup-actions",children:[e.jsx("button",{className:"btn btn-primary",onClick:()=>S("/seller/payment-settings"),children:"Complete Payment Setup"}),e.jsx("button",{className:"btn btn-outline",onClick:()=>S("/seller/my-sports-strategies"),children:"Go Back"})]})]})})});const f=s=>{const{name:a,value:i,type:l,checked:c}=s.target,n=l==="checkbox"?c:i;u(a==="auctionDetails.auctionStartDate"||a==="auctionDetails.auctionEndDate"?o=>({...o,auctionDetails:{...o.auctionDetails,[a.split(".")[1]]:i?x(new Date(i)).toISOString().slice(0,16):""}}):o=>({...o,[a]:n})),l!=="checkbox"&&U(a,n)},_=s=>{const{name:a,value:i}=s.target;U(a,i)},I=(s,a)=>{u(i=>({...i,[s]:a})),U(s,a)},U=(s,a)=>{const i={...r};switch(s){case"title":{a.trim()?a.length>y.TITLE.maxChars?i.title=`Title cannot exceed ${y.TITLE.maxChars} characters`:delete i.title:i.title="Strategy title is required";break}case"category":{a?delete i.category:i.category="Please select a category";break}case"coachName":{a.trim()?a.length>y.COACH_NAME.maxChars?i.coachName=`Coach name cannot exceed ${y.COACH_NAME.maxChars} characters`:delete i.coachName:i.coachName="Coach/Seller/Academy name is required";break}case"description":{a.replace(/<[^>]*>/g,"").trim()?delete i.description:i.description="Strategy description is required";break}case"aboutCoach":{a.replace(/<[^>]*>/g,"").trim()?delete i.aboutCoach:i.aboutCoach="About the coach information is required";break}case"strategicContent":{a.replace(/<[^>]*>/g,"").trim()?delete i.strategicContent:i.strategicContent="Strategic content description is required";break}case"contentType":{a?delete i.contentType:i.contentType="Please select a content type";break}case"difficulty":{a?delete i.difficulty:i.difficulty="Please select a difficulty level";break}case"saleType":{a?delete i.saleType:i.saleType="Please select a sale type";break}case"price":{!a||a<=0?i.price="Please enter a valid price greater than $0":delete i.price;break}}g(i)},ue=async s=>{const a=s.target.files[0];if(a){const i=xe(a,t.contentType);if(!i.isValid){j(i.message),s.target.value="";return}w(a),N(!0),D("content file"),v(a.name);const l=new FormData;l.append("file",a),l.append("type","content");try{const c=await C(X(l)).unwrap();u(o=>({...o,fileUrl:c.data.fileUrl,fileSize:c.data.fileSize||a.size}));const n={...r};delete n.fileUpload,g(n),B("Content file uploaded successfully!")}catch(c){console.error("File upload failed:",c),w(null),j("Failed to upload content file. Please try again.")}finally{N(!1),D(""),v("")}}},me=async s=>{s.preventDefault(),z(!0);const a={};if(t.title.trim()?t.title.length>y.TITLE.maxChars&&(a.title=`Title cannot exceed ${y.TITLE.maxChars} characters`):a.title="Strategy title is required",t.category||(a.category="Please select a category"),t.coachName.trim()?t.coachName.length>y.COACH_NAME.maxChars&&(a.coachName=`Coach name cannot exceed ${y.COACH_NAME.maxChars} characters`):a.coachName="Coach/Seller/Academy name is required",t.description.replace(/<[^>]*>/g,"").trim()||(a.description="Strategy description is required"),!t.fileUrl&&!h&&(a.fileUpload="Please upload a video or document file"),t.aboutCoach.replace(/<[^>]*>/g,"").trim()||(a.aboutCoach="About the coach information is required"),t.strategicContent.replace(/<[^>]*>/g,"").trim()||(a.strategicContent="Strategic content description is required"),t.contentType||(a.contentType="Please select a content type"),t.thumbnailUrl||(a.thumbnailUpload="Please upload a thumbnail image"),t.difficulty||(a.difficulty="Please select a difficulty level"),t.saleType||(a.saleType="Please select a sale type"),t.saleType==="Fixed")(!t.price||t.price<=0)&&(a.price="Please enter a valid price greater than $0");else if(t.saleType==="Auction"&&((!t.auctionDetails.basePrice||t.auctionDetails.basePrice<=0)&&(a.auctionBasePrice="Please enter a valid starting bid price greater than $0"),(!t.auctionDetails.minimumBidIncrement||t.auctionDetails.minimumBidIncrement<=0)&&(a.auctionMinIncrement="Please enter a valid minimum bid increment greater than $0"),t.auctionDetails.auctionStartDate?new Date(t.auctionDetails.auctionStartDate)<=new Date&&(a.auctionStartDate="Auction start date must be in the future"):a.auctionStartDate="Please select an auction start date",t.auctionDetails.auctionEndDate||(a.auctionEndDate="Please select an auction end date"),t.auctionDetails.auctionStartDate&&t.auctionDetails.auctionEndDate)){const n=new Date(t.auctionDetails.auctionStartDate);new Date(t.auctionDetails.auctionEndDate)<=n&&(a.auctionDateRange="Auction end date must be after start date")}if(Object.keys(a).length>0){je.flushSync(()=>{g(a),$(!0)}),setTimeout(()=>{const n=document.querySelector(".AddStrategy__validation-error");n&&n.scrollIntoView({behavior:"smooth",block:"center"})},100);return}try{const n={...t,sport:t.category||"Other",coachName:t.coachName||"Coach"};t.saleType==="Fixed"?n.price=parseFloat(t.price):t.saleType==="Auction"&&(n.price=parseFloat(t.auctionDetails.basePrice),n.auctionDetails.auctionStartDate&&(n.auctionDetails.auctionStartDate=x(new Date(n.auctionDetails.auctionStartDate))),n.auctionDetails.auctionEndDate&&(n.auctionDetails.auctionEndDate=x(new Date(n.auctionDetails.auctionEndDate))));const o=await C(fe(n)).unwrap();o.data&&o.data._id&&(M(o.data._id),O(!0)),B("🎉 Strategy created successfully! Preview will be generated in the background."),S("/seller/my-sports-strategies"),L()}catch(n){console.error("Content creation failed:",n);let o="Failed to create strategy. Please try again.";n.message?o=n.message:n.errors&&n.errors.length>0?o=n.errors[0].msg||o:typeof n=="string"&&(o=n),j(`❌ ${o}`)}},L=()=>{u({title:"",category:"",coachName:"",description:"",fileUrl:"",aboutCoach:"",strategicContent:"",sport:"Other",contentType:"",previewUrl:"",thumbnailUrl:"",duration:"",videoLength:"",fileSize:"",tags:[],difficulty:"",language:"English",prerequisites:[],learningObjectives:[],equipment:[],saleType:"",price:"",allowCustomRequests:!1,customRequestPrice:"",status:"Published",visibility:"Public",auctionDetails:{basePrice:"",auctionStartDate:"",auctionEndDate:"",minimumBidIncrement:"",allowOfferBeforeAuctionStart:!1}}),w(null),M(null),O(!1),g({}),$(!1),z(!1),P(""),E(!1)},V={maxSize:5*1024*1024,allowedTypes:["image/jpeg","image/jpg","image/png","image/gif"],allowedExtensions:[".jpg",".jpeg",".png",".gif"]},pe=async s=>{const a=s.target.files[0];if(a){P(""),E(!1);try{if(!V.allowedTypes.includes(a.type))throw new Error("Only JPG, JPEG, PNG, and GIF formats are supported for thumbnails");if(a.size>V.maxSize)throw new Error("Thumbnail file size must be less than 5MB");N(!0),D("thumbnail"),v(a.name);const i=new FormData;i.append("file",a),i.append("type","thumbnail");const l=await C(X(i)).unwrap();if(!l.data||!l.data.fileUrl)throw new Error("Invalid response from server");u(c=>({...c,thumbnailUrl:l.data.fileUrl})),E(!0),B("Thumbnail uploaded successfully!")}catch(i){console.error("Thumbnail upload failed:",i),P(i.message||"Failed to upload thumbnail. Please try again."),u(l=>({...l,thumbnailUrl:""}))}finally{N(!1),D(""),v("")}}};return e.jsx(K,{children:e.jsxs("div",{className:"AddStrategy",children:[e.jsxs("form",{className:"AddStrategy__form",onSubmit:me,children:[e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Strategy Title"}),e.jsx("input",{type:"text",name:"title",className:"AddStrategy__input",placeholder:"Add title for New Strategy",value:t.title,onChange:f,onBlur:_}),(r.title||m&&!t.title.trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.title||"Strategy title is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Select Category"}),e.jsxs("select",{name:"category",className:"AddStrategy__select",value:t.category,onChange:f,onBlur:_,children:[e.jsx("option",{value:"",children:"Select Category"}),e.jsx("option",{value:"Basketball",children:"Basketball"}),e.jsx("option",{value:"Football",children:"Football"}),e.jsx("option",{value:"Soccer",children:"Soccer"}),e.jsx("option",{value:"Baseball",children:"Baseball"})]}),(r.category||m&&!t.category)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.category||"Please select a category"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Coach/Seller/Academy Name"}),e.jsx("input",{type:"text",name:"coachName",className:"AddStrategy__input",placeholder:"Enter coach, seller, or academy name",value:t.coachName,onChange:f,onBlur:_}),(r.coachName||ce&&!t.coachName.trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.coachName||"Coach/Seller/Academy name is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Description for Strategy"}),e.jsx(k,{value:t.description,onChange:s=>I("description",s),placeholder:"Enter a detailed description of your strategy...",height:200,className:"AddStrategy__summernote"}),(r.description||m&&!t.description.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.description||"Strategy description is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Content Type"}),e.jsxs("select",{name:"contentType",className:"AddStrategy__select",value:t.contentType,onChange:f,onBlur:_,children:[e.jsx("option",{value:"",children:"Select Content Type"}),e.jsx("option",{value:"Video",children:"Video"}),e.jsx("option",{value:"Document",children:"Document"})]}),(r.contentType||m&&!t.contentType)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.contentType||"Please select a content type"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsxs("label",{className:"AddStrategy__label",children:["Upload ",t.contentType||"Video/Document"]}),t.contentType&&e.jsx("p",{className:"AddStrategy__format-note",children:t.contentType==="Video"?e.jsxs(e.Fragment,{children:["Maximum size: ",e.jsx("span",{children:"500MB"})," • Supported formats: ",e.jsx("span",{children:"MP4, MOV, AVI, WEBM"})]}):t.contentType==="Document"?e.jsxs(e.Fragment,{children:["Maximum size: ",e.jsx("span",{children:"50MB"})," • Supported formats: ",e.jsx("span",{children:"PDF, DOC, DOCX"})]}):"Please select a content type to see upload requirements"}),e.jsxs("label",{htmlFor:"file-upload",className:"AddStrategy__upload",children:[e.jsx("input",{type:"file",id:"file-upload",className:"AddStrategy__file-input",onChange:ue,accept:_e(t.contentType),disabled:Z(t.contentType),style:{display:"none"}}),e.jsxs("div",{className:`AddStrategy__upload-content ${Z(t.contentType)?"AddStrategy__upload-content--disabled":""}`,children:[e.jsx(J,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:h?h.name:t.fileUrl?"Current file uploaded":"Click to upload file"})]}),(h||t.fileUrl)&&e.jsxs("div",{className:"AddStrategy__file-info",children:[e.jsx("p",{className:"AddStrategy__file-name",children:h?h.name:"Current file uploaded"}),h&&e.jsxs("p",{className:"AddStrategy__file-size",children:[(h.size/1024/1024).toFixed(2)," MB"]})]})]}),(r.fileUpload||m&&!t.fileUrl&&!h)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.fileUpload||"Please upload a video or document file"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"About The Coach"}),e.jsx(k,{value:t.aboutCoach,onChange:s=>I("aboutCoach",s),placeholder:"Share your background, experience, and expertise...",height:200,className:"AddStrategy__summernote"}),(r.aboutCoach||m&&!t.aboutCoach.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.aboutCoach||"About the coach information is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Includes Strategic Content"}),e.jsx(k,{value:t.strategicContent,onChange:s=>I("strategicContent",s),placeholder:"Describe what strategic content is included...",height:200,className:"AddStrategy__summernote"}),(r.strategicContent||m&&!t.strategicContent.replace(/<[^>]*>/g,"").trim())&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.strategicContent||"Strategic content description is required"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Thumbnail Image"}),e.jsxs("p",{className:"AddStrategy__format-note",children:["Maximum size: ",e.jsx("span",{children:"5MB"})," • Supported formats: ",e.jsx("span",{children:"JPG, JPEG, PNG, GIF"})]}),e.jsxs("label",{htmlFor:"thumbnail-upload",className:"AddStrategy__upload",children:[e.jsx("input",{type:"file",id:"thumbnail-upload",className:"AddStrategy__file-input",accept:"image/jpeg,image/jpg,image/png,image/gif",onChange:pe,style:{display:"none"}}),e.jsxs("div",{className:"AddStrategy__upload-content",children:[e.jsx(J,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:t.thumbnailUrl?"Thumbnail uploaded":"Click to upload thumbnail"})]}),R&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:R})}),(r.thumbnailUpload||m&&!t.thumbnailUrl)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.thumbnailUpload||"Please upload a thumbnail image"})}),t.thumbnailUrl&&le&&e.jsx("div",{className:"AddStrategy__thumbnail-preview",children:e.jsx("img",{src:W(t.thumbnailUrl),alt:"Thumbnail preview",onError:s=>{console.error("Thumbnail preview failed:",s),console.error("Failed URL:",W(t.thumbnailUrl)),console.error("Original thumbnailUrl:",t.thumbnailUrl),s.target.src=Y(200,120,"Image not found")},style:{maxWidth:"100%",height:"auto",borderRadius:"var(--border-radius)"}})}),!t.thumbnailUrl&&e.jsx("div",{className:"AddStrategy__thumbnail-preview",children:e.jsx("img",{src:Y(200,120,"No thumbnail"),alt:"No thumbnail",style:{maxWidth:"100%",height:"auto",borderRadius:"var(--border-radius)",opacity:.7}})})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Tags"}),e.jsxs("div",{className:"AddStrategy__array-field",children:[e.jsxs("div",{className:"AddStrategy__array-input",children:[e.jsx("input",{type:"text",className:"AddStrategy__input",placeholder:"Add a tag (e.g., basketball, technique, training)...",value:b,onChange:s=>T(s.target.value),onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),b.trim()&&(u(a=>({...a,tags:[...a.tags,b.trim()]})),T("")))}}),e.jsx("button",{type:"button",className:"AddStrategy__add-btn btn-primary",onClick:()=>{b.trim()&&(u(s=>({...s,tags:[...s.tags,b.trim()]})),T(""))},children:"Add"})]}),e.jsx("div",{className:"AddStrategy__array-items",children:t.tags.map((s,a)=>e.jsxs("div",{className:"AddStrategy__array-item",children:[e.jsx("span",{children:s}),e.jsx("button",{type:"button",className:"AddStrategy__remove-btn",onClick:()=>{u(i=>({...i,tags:i.tags.filter((l,c)=>c!==a)}))},children:"X"})]},a))})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Difficulty Level"}),e.jsxs("select",{name:"difficulty",className:"AddStrategy__select",value:t.difficulty,onChange:f,onBlur:_,children:[e.jsx("option",{value:"",children:"Select Difficulty"}),e.jsx("option",{value:"Beginner",children:"Beginner"}),e.jsx("option",{value:"Intermediate",children:"Intermediate"}),e.jsx("option",{value:"Advanced",children:"Advanced"}),e.jsx("option",{value:"Professional",children:"Professional"})]}),(r.difficulty||m&&!t.difficulty)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.difficulty||"Please select a difficulty level"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Sale Type"}),e.jsxs("select",{name:"saleType",className:"AddStrategy__select",value:t.saleType,onChange:f,onBlur:_,children:[e.jsx("option",{value:"",children:"Select Sale Type"}),e.jsx("option",{value:"Fixed",children:"Fixed Price"}),e.jsx("option",{value:"Auction",children:"Auction"})]}),(r.saleType||m&&!t.saleType)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.saleType||"Please select a sale type"})})]}),t.saleType==="Fixed"&&e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Price ($)"}),e.jsx("input",{type:"number",name:"price",className:"AddStrategy__input",placeholder:"Enter price",value:t.price,onChange:f,onBlur:_,min:"0",step:"0.01"}),(r.price||m&&(!t.price||t.price<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.price||"Please enter a valid price greater than $0"})})]}),t.saleType==="Auction"&&e.jsxs("div",{className:"AddStrategy__auction-section",children:[e.jsx("h3",{className:"AddStrategy__section-title",children:"Auction Settings"}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Starting Bid Price ($)"}),e.jsx("input",{type:"number",name:"auctionDetails.basePrice",className:"AddStrategy__input",placeholder:"Enter starting bid price",value:t.auctionDetails.basePrice,onChange:s=>u(a=>({...a,auctionDetails:{...a.auctionDetails,basePrice:s.target.value}})),min:"0",step:"0.01"}),(r.auctionBasePrice||m&&(!t.auctionDetails.basePrice||t.auctionDetails.basePrice<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionBasePrice||"Please enter a valid starting bid price greater than $0"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Minimum Bid Increment ($)"}),e.jsx("input",{type:"number",name:"auctionDetails.minimumBidIncrement",className:"AddStrategy__input",placeholder:"Enter minimum bid increment",value:t.auctionDetails.minimumBidIncrement,onChange:s=>u(a=>({...a,auctionDetails:{...a.auctionDetails,minimumBidIncrement:s.target.value}})),min:"0.01",step:"0.01"}),(r.auctionMinIncrement||m&&(!t.auctionDetails.minimumBidIncrement||t.auctionDetails.minimumBidIncrement<=0))&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionMinIncrement||"Please enter a valid minimum bid increment greater than $0"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Auction Start Date"}),e.jsxs(Q,{children:[e.jsx("input",{type:"datetime-local",name:"auctionDetails.auctionStartDate",className:"AddStrategy__input",value:t.auctionDetails.auctionStartDate,min:te(new Date),onChange:s=>{const a=s.target.value;u(i=>({...i,auctionDetails:{...i.auctionDetails,auctionStartDate:a}})),a&&(x(new Date(a))<=new Date?g(c=>({...c,auctionStartDate:"Auction start date must be in the future"})):g(c=>{const n={...c};return delete n.auctionStartDate,n}))}}),e.jsx(ee,{})]}),(r.auctionStartDate||m&&!t.auctionDetails.auctionStartDate)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionStartDate||"Please select an auction start date"})})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Auction End Date"}),e.jsxs(Q,{children:[e.jsx("input",{type:"datetime-local",name:"auctionDetails.auctionEndDate",className:"AddStrategy__input",value:t.auctionDetails.auctionEndDate,min:te(t.auctionDetails.auctionStartDate?new Date(t.auctionDetails.auctionStartDate):new Date),onChange:s=>{const a=s.target.value;if(u(i=>({...i,auctionDetails:{...i.auctionDetails,auctionEndDate:a}})),a&&t.auctionDetails.auctionStartDate){const i=x(new Date(t.auctionDetails.auctionStartDate));x(new Date(a))<=i?g(c=>({...c,auctionDateRange:"Auction end date must be after start date"})):g(c=>{const n={...c};return delete n.auctionDateRange,n})}}}),e.jsx(ee,{})]}),(r.auctionEndDate||r.auctionDateRange||m&&!t.auctionDetails.auctionEndDate)&&e.jsx("div",{className:"AddStrategy__validation-error",children:e.jsx("p",{className:"AddStrategy__error-message",children:r.auctionEndDate||r.auctionDateRange||"Please select an auction end date"})})]}),e.jsx("div",{className:"AddStrategy__field",children:e.jsxs("label",{className:"AddStrategy__checkbox-label",children:[e.jsx("input",{type:"checkbox",name:"auctionDetails.allowOfferBeforeAuctionStart",checked:t.auctionDetails.allowOfferBeforeAuctionStart,onChange:s=>u(a=>({...a,auctionDetails:{...a.auctionDetails,allowOfferBeforeAuctionStart:s.target.checked}})),className:"AddStrategy__checkbox"}),"Allow offers before auction starts"]})}),e.jsx("div",{className:"AddStrategy__field",children:e.jsx("div",{className:"AddStrategy__auction-note",children:e.jsxs("p",{children:[e.jsx("strong",{children:"Note:"})," Once the auction starts, the strategy content cannot be edited."]})})})]}),ae&&F&&e.jsx("div",{className:"AddStrategy__error",children:e.jsxs("p",{children:["Error: ",F.message||"Something went wrong"]})}),e.jsxs("div",{className:"AddStrategy__actions",children:[e.jsx("button",{type:"submit",className:"btn-primary",disabled:A,children:A?"Creating...":"Add New Strategy"}),e.jsx("button",{type:"button",className:"btn-outline",onClick:L,disabled:A,children:"Reset Form"})]})]}),ne&&q&&e.jsx(PreviewStatusIndicator,{contentId:q,onStatusChange:s=>{console.log("Preview status updated:",s)}}),e.jsx(Se,{progress:A?50:0,isVisible:ie,fileName:re,uploadType:se})]})})};export{Pe as default};
