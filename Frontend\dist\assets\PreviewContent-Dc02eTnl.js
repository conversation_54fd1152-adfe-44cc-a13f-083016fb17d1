import{r as t,j as l}from"./index-oGr_1a_A.js";const H=4,L=u=>{const{html:c="",className:h="",previewLines:o=H,labelSeeMore:x="See more",labelSeeLess:g="See less",ariaLabel:m="Preview content"}=u||{},[e,p]=t.useState(!1),[f,w]=t.useState(!1),[r,S]=t.useState("auto"),[d,a]=t.useState("none"),n=t.useRef(null);return t.useEffect(()=>{if(n.current){p(!1);const s=n.current,v=window.getComputedStyle(s),i=parseFloat(v.lineHeight)*o;S(`${i}px`),w(s.scrollHeight>i+2),a(`${i}px`)}},[c,o]),t.useEffect(()=>{n.current&&a(e?`${n.current.scrollHeight}px`:r)},[e,r]),t.useEffect(()=>{if(e&&n.current){const s=setTimeout(()=>{a("none")},400);return()=>clearTimeout(s)}},[e]),l.jsxs("div",{className:`preview-content-wrapper ${h}`,"aria-label":m,tabIndex:0,children:[l.jsx("div",{className:`preview-content-inner${e?" expanded":""}${e?"":" preview-collapsed"}`,ref:n,"data-maxheight":d,"data-collapsedheight":r,"data-expanded":e,style:{"--preview-max-height":d,WebkitLineClamp:e?"unset":o},"aria-expanded":e,dangerouslySetInnerHTML:{__html:c}}),f&&l.jsx("button",{className:"preview-content-toggle",onClick:()=>p(s=>!s),"aria-expanded":e,"aria-controls":"preview-content-inner",children:e?g:x})]})};export{L as P};
