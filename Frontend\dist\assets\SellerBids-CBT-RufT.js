import{a as P,r as x,j as e,a_ as $,a8 as R,c9 as T,cm as M,a9 as p,af as E,u as _,b as I,cn as D,a3 as L,J as U,aV as V,co as G}from"./index-oGr_1a_A.js";import{S as C}from"./SellerLayout-Du2JNoeS.js";/* empty css                        */import{g as W}from"./settingsService-mpdGABki.js";import{b as z,a as J}from"./dateValidation-YzamylQ0.js";import{T as Y}from"./Table-67eaNy4J.js";import{P as q}from"./Pagination-Chk7MbK3.js";import{f as F}from"./timezoneUtils-Dky5bF8c.js";import"./index-auB_fJid.js";const H=({isOpen:o,onClose:u,bid:a,content:t,onBidAccepted:j})=>{var b,y,w,A,B;P();const[h,n]=x.useState(!1),[d,f]=x.useState(""),[N,v]=x.useState(10);x.useEffect(()=>{o&&(async()=>{try{const c=await W();c.data.success&&v(c.data.data.platformCommission)}catch(c){console.error("Error fetching platform commission:",c)}})()},[o]);const S=async()=>{var l,c;if(!a||!a._id){p.error("Invalid bid data");return}n(!0);try{const s=await E.put(`/bids/${a._id}/status`,{status:"accepted",sellerResponse:d.trim()||"Bid accepted!"});if(s.data.success)p.success("Bid accepted successfully! Auction ended and buyer notified."),j&&j(s.data.data),u();else throw new Error(s.data.message||"Failed to accept bid")}catch(s){console.error("Error accepting bid:",s),p.error(((c=(l=s.response)==null?void 0:l.data)==null?void 0:c.message)||"Failed to accept bid")}finally{n(!1)}},k=async()=>{var l,c;if(!a||!a._id){p.error("Invalid bid data");return}n(!0);try{const s=await E.put(`/bids/${a._id}/status`,{status:"rejected",sellerResponse:d.trim()||"Bid rejected."});if(s.data.success)p.success("Bid rejected successfully."),j&&j(s.data.data),u();else throw new Error(s.data.message||"Failed to reject bid")}catch(s){console.error("Error rejecting bid:",s),p.error(((c=(l=s.response)==null?void 0:l.data)==null?void 0:c.message)||"Failed to reject bid")}finally{n(!1)}};return!o||!a?null:e.jsx("div",{className:"modal-overlay",onClick:u,children:e.jsxs("div",{className:"auction-bid-modal",onClick:l=>l.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h2",{className:"modal-title",children:[e.jsx($,{className:"modal-icon"}),"Manage Auction Bid"]}),e.jsx("button",{className:"modal-close",onClick:u,children:e.jsx(R,{})})]}),e.jsxs("div",{className:"modal-content",children:[e.jsxs("div",{className:"bid-details-section",children:[e.jsx("h3",{children:"Bid Details"}),e.jsxs("div",{className:"bid-info-grid",children:[e.jsxs("div",{className:"bid-info-item",children:[e.jsx("span",{className:"label",children:"Bidder:"}),e.jsxs("span",{className:"value",children:[(b=a.bidder)==null?void 0:b.firstName," ",(y=a.bidder)==null?void 0:y.lastName]})]}),e.jsxs("div",{className:"bid-info-item",children:[e.jsx("span",{className:"label",children:"Bid Amount:"}),e.jsxs("span",{className:"value bid-amount",children:["$",(w=a.amount)==null?void 0:w.toFixed(2)]})]}),e.jsxs("div",{className:"bid-info-item",children:[e.jsx("span",{className:"label",children:"Bid Date:"}),e.jsxs("p",{className:"bid-date",children:["Submitted on"," ",z(a.createdAt)]})]}),e.jsxs("div",{className:"bid-info-item",children:[e.jsx("span",{className:"label",children:"Status:"}),e.jsx("span",{className:`value status-${(A=a.status)==null?void 0:A.toLowerCase()}`,children:a.status})]})]})]}),e.jsxs("div",{className:"content-details-section",children:[e.jsx("h3",{children:"Content Details"}),e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:t==null?void 0:t.title}),e.jsxs("div",{className:"content-meta",children:[t==null?void 0:t.sport," • ",t==null?void 0:t.contentType," • ",t==null?void 0:t.difficulty]})]})]}),e.jsx("div",{className:"warning-section",children:e.jsxs("div",{className:"warning-box",children:[e.jsx(T,{className:"warning-icon"}),e.jsxs("div",{className:"warning-content",children:[e.jsx("h4",{children:"Important Notice"}),e.jsxs("ul",{children:[e.jsxs("li",{children:["Accepting this bid will ",e.jsx("strong",{children:"immediately end the auction"})]}),e.jsx("li",{children:'All other active bids will be marked as "Lost"'}),e.jsxs("li",{children:["The content will be ",e.jsx("strong",{children:"removed from public listing"})]}),e.jsx("li",{children:"The winning bidder will receive an email with checkout instructions"}),e.jsxs("li",{children:["This action ",e.jsx("strong",{children:"cannot be undone"})]})]})]})]})}),e.jsxs("div",{className:"response-section",children:[e.jsx("label",{htmlFor:"sellerResponse",className:"response-label",children:"Response Message (Optional)"}),e.jsx("textarea",{id:"sellerResponse",className:"response-textarea",placeholder:"Add a personal message to the bidder...",value:d,onChange:l=>f(l.target.value),maxLength:500,rows:3}),e.jsxs("div",{className:"character-count",children:[d.length,"/500 characters"]})]}),e.jsxs("div",{className:"earnings-section",children:[e.jsx("h3",{children:"Earnings Breakdown"}),e.jsxs("div",{className:"earnings-grid",children:[e.jsxs("div",{className:"earnings-item",children:[e.jsx("span",{className:"label",children:"Bid Amount:"}),e.jsxs("span",{className:"value",children:["$",(B=a.amount)==null?void 0:B.toFixed(2)]})]}),e.jsxs("div",{className:"earnings-item",children:[e.jsxs("span",{className:"label",children:["Platform Fee (",N,"%):"]}),e.jsxs("span",{className:"value",children:["-$",(a.amount*(N/100)).toFixed(2)]})]}),e.jsxs("div",{className:"earnings-item total",children:[e.jsx("span",{className:"label",children:"Your Earnings:"}),e.jsxs("span",{className:"value",children:["$",(a.amount*(1-N/100)).toFixed(2)]})]})]})]})]}),e.jsxs("div",{className:"modal-actions",children:[e.jsx("button",{className:"btn-secondary",onClick:u,disabled:h,children:"Cancel"}),e.jsx("button",{className:"btn-danger",onClick:k,disabled:h,children:h?"Processing...":"Reject Bid"}),e.jsxs("button",{className:"btn-success",onClick:S,disabled:h,children:[e.jsx(M,{}),h?"Accepting...":"Accept Bid & End Auction"]})]})]})})},ie=()=>{const o=P(),u=_(),{sellerBids:a,isLoading:t,isError:j,error:h,pagination:n}=I(s=>s.bid),[d,f]=x.useState(null),[N,v]=x.useState(!1);x.useEffect(()=>{o(D({page:n.page,limit:n.limit}))},[o,n.page]);const S=s=>{u(`/seller/bid-details/${s.replace("#","")}`)},k=s=>{f(s),v(!0)},b=()=>{v(!1),f(null)},y=()=>{o(D({page:n.page,limit:n.limit}))},w=s=>{switch(s){case"Active":return"status-active";case"Won":return"status-won";case"Lost":return"status-lost";case"Cancelled":return"status-cancelled";default:return"status-default"}},A=s=>J(s),B=[{key:"no",label:"No.",className:"no"},{key:"bidId",label:"Bid Id"},{key:"content",label:"Videos/Documents",render:s=>{var i,r,m,g;return e.jsxs("div",{className:"video-doc",children:[e.jsx("img",{src:`${U}${(i=s.content)==null?void 0:i.thumbnailUrl}`||"https://via.placeholder.com/60x40",alt:((r=s.content)==null?void 0:r.title)||"Content"}),e.jsxs("div",{className:"content-details",children:[e.jsx("span",{className:"content-title",children:((m=s.content)==null?void 0:m.title)||"Unknown Content"}),e.jsx("span",{className:"content-type",children:((g=s.content)==null?void 0:g.contentType)||"Unknown"})]})]})}},{key:"bidder",label:"Bidder",render:s=>{var i,r;return e.jsx("div",{className:"bidder-info",children:e.jsxs("span",{className:"bidder-name",children:[(i=s.bidder)==null?void 0:i.firstName," ",(r=s.bidder)==null?void 0:r.lastName]})})}},{key:"date",label:"Date",render:s=>A(s.createdAt)},{key:"bidAmount",label:"Bid Amount",render:s=>{var i;return`$${((i=s.amount)==null?void 0:i.toFixed(2))||"0.00"}`}},{key:"status",label:"Status",render:s=>e.jsx("span",{className:`status-badge ${w(s.status)}`,children:s.status})},{key:"auctionStatus",label:"Auction",render:s=>{var i,r,m,g;return e.jsxs("div",{className:"auction-details",children:[e.jsxs("p",{children:["Start:"," ",(r=(i=s.content)==null?void 0:i.auctionDetails)!=null&&r.auctionStartDate?F(s.content.auctionDetails.auctionStartDate):"Not set"]}),e.jsxs("p",{children:["End:"," ",(g=(m=s.content)==null?void 0:m.auctionDetails)!=null&&g.auctionEndDate?F(s.content.auctionDetails.auctionEndDate):"Not set"]})]})}},{key:"action",label:"Action",render:s=>e.jsx("div",{className:"action-buttons",children:s.status==="Active"?e.jsx("button",{className:"btn-review",onClick:()=>k(s),children:e.jsx($,{})}):e.jsx("button",{className:"btn-view",onClick:()=>S(s._id),children:e.jsx(V,{})})})}],l=s=>s.map((i,r)=>{var m;return{...i,no:r+1,bidId:`#${((m=i._id)==null?void 0:m.slice(-6))||"N/A"}`}}),c=s=>{o(G(s))};return t?e.jsx(C,{children:e.jsx("div",{className:"video-status-container",children:e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading bids..."})]})})}):j?e.jsx(C,{children:e.jsx(L,{error:h})}):e.jsxs(C,{children:[e.jsx("div",{className:"seller-bids-container",children:e.jsxs("div",{children:[e.jsx(Y,{columns:B,data:l(a),emptyMessage:"No bids found"}),n.totalPages>1&&e.jsx(q,{currentPage:n.page,totalPages:n.totalPages,onPageChange:c})]})}),e.jsx(H,{isOpen:N,onClose:b,bid:d,content:d==null?void 0:d.content,onBidAccepted:y})]})};export{ie as default};
