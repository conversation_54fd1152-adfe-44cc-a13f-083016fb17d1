import{a as L,b as o,b4 as M,b5 as k,b6 as R,b7 as z,r as t,aE as H,b8 as b,b9 as n,j as e,aI as T,ba as V,bb as Y,bc as q,aH as B,bd as x,be as $,bf as j,bg as G,bh as J,bi as K,bj as g}from"./index-oGr_1a_A.js";import{S as N}from"./SellerLayout-Du2JNoeS.js";import{s as Q}from"./stripe-DT3_Ek51.js";const ee=()=>{const r=L(),m=o(M),l=o(k),y=o(R),u=o(z),[f,h]=t.useState({nameOnCard:"",cardNumber:"",expiryDate:"",cvv:""}),[c,S]=t.useState(!1),[p,w]=t.useState(null),[U,D]=t.useState(null),[i,E]=t.useState(null),C=t.useRef(null);t.useEffect(()=>(l==="add"&&(async()=>{try{const s=await Q;if(!s)throw new Error("Stripe failed to load");w(s);const d=s.elements();D(d);const v=d.create("card",{style:{base:{color:"#424770",fontFamily:'"Helvetica Neue", Helvetica, sans-serif',fontSmoothing:"antialiased",fontSize:"16px","::placeholder":{color:"#aab7c4"}},invalid:{color:"#9e2146",iconColor:"#9e2146"}},hidePostalCode:!0});E(v)}catch(s){console.error("Error initializing Stripe:",s),n("Failed to load payment form. Please refresh the page.")}})(),()=>{i&&i.unmount()}),[l]),t.useEffect(()=>{i&&C.current&&l==="add"&&i.mount(C.current)},[i,l]),t.useEffect(()=>(r(H()),()=>{r(b())}),[r]),t.useEffect(()=>{u&&(n(u.message||"Failed to load cards"),r(b()))},[u,r]);const _=()=>{l==="add"&&h({nameOnCard:"",cardNumber:"",expiryDate:"",cvv:""}),r(x(l==="list"?"add":"list"))},F=async a=>{if(window.confirm("Are you sure you want to remove this card?"))try{await r(G(a)).unwrap(),j("Card removed successfully")}catch(s){n(s.message||"Failed to remove card")}},P=async a=>{try{await r($(a)).unwrap(),j("Default card updated")}catch(s){n(s.message||"Failed to update default card")}},O=a=>{h(s=>({...s,nameOnCard:a}))},A=async a=>{if(a.preventDefault(),!f.nameOnCard.trim()){n("Please enter cardholder name");return}if(!p||!i){n("Payment form not ready. Please try again.");return}S(!0);const s=J("Adding your card...");try{const{error:d,paymentMethod:v}=await p.createPaymentMethod({type:"card",card:i,billing_details:{name:f.nameOnCard.trim()}});if(d)throw new Error(d.message);await r(K({paymentMethodId:v.id,isDefault:m.length===0})).unwrap(),g(s,"Card added successfully!","success"),h({nameOnCard:"",cardNumber:"",expiryDate:"",cvv:""}),r(x("list"))}catch(d){console.error("Error adding card:",d),g(s,d.message||"Failed to add card","error")}finally{S(!1)}},I=a=>{const s={visa:"https://js.stripe.com/v3/fingerprinted/img/visa-********************************.svg",mastercard:"https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg",amex:"https://js.stripe.com/v3/fingerprinted/img/amex-a49b82f46c5cd6a96a6ef9a6d86c802a.svg",discover:"https://js.stripe.com/v3/fingerprinted/img/discover-ac52cd46f89fa40a29a0bfded4bdb5cf.svg",diners:"https://js.stripe.com/v3/fingerprinted/img/diners-fbcbd024c13e67d8c3b7b24ace822024.svg",jcb:"https://js.stripe.com/v3/fingerprinted/img/jcb-271fd06e6e0a86955c6d560bdb81fb77.svg",unionpay:"https://js.stripe.com/v3/fingerprinted/img/unionpay-8a10aefc7295216c338ba4e1224627a1.svg"};return s[a]||s.mastercard};return y?e.jsx(N,{children:e.jsx("div",{className:"SellerCards",children:e.jsx("div",{className:"SellerCards__loading",children:"Loading cards..."})})}):e.jsx(N,{children:e.jsx("div",{className:"SellerCards",children:l==="list"?e.jsxs("div",{className:"SellerCards__list-view",children:[e.jsxs("div",{className:"SellerCards__header",children:[e.jsx("h3",{className:"SellerCards__subtitle",children:"Saved Cards"}),e.jsxs("button",{className:"btn-outline",onClick:_,children:[e.jsx(T,{})," Add New Card"]})]}),e.jsx("div",{className:"SellerCards__cards-list",children:m.length>0?m.map(a=>e.jsx("div",{className:"SellerCards__card-item",children:e.jsxs("div",{className:"SellerCards__card-content",children:[e.jsxs("div",{className:"SellerCards__card-info",children:[e.jsx("div",{className:"SellerCards__card-logo",children:e.jsx("img",{src:I(a.cardType),alt:a.cardType})}),e.jsxs("div",{className:"SellerCards__card-details",children:[e.jsxs("div",{className:"SellerCards__card-number",children:["•••• •••• •••• ",a.lastFourDigits]}),e.jsx("div",{className:"SellerCards__card-name",children:a.cardholderName}),e.jsxs("div",{className:"SellerCards__card-expiry",children:["Expires"," ",a.expiryMonth.toString().padStart(2,"0"),"/",a.expiryYear.toString().slice(-2)]})]})]}),e.jsxs("div",{className:"SellerCards__card-actions",children:[e.jsx("button",{className:`SellerCards__default-btn ${a.isDefault?"active":""}`,onClick:()=>!a.isDefault&&P(a._id),disabled:a.isDefault,title:a.isDefault?"Default card":"Set as default",children:a.isDefault?e.jsx(V,{}):e.jsx(Y,{})}),e.jsx("button",{className:"SellerCards__delete-btn",onClick:()=>F(a._id),"aria-label":"Delete card",children:e.jsx(q,{className:"delete-icon"})})]})]})},a._id)):e.jsxs("div",{className:"SellerCards__empty-state",children:[e.jsx(B,{className:"SellerCards__empty-icon"}),e.jsx("p",{children:"You have no saved payment methods yet."}),e.jsx("button",{className:"SellerCards__add-first-btn btn-primary",onClick:_,children:"Add Your First Card"})]})})]}):e.jsxs("div",{className:"SellerCards__add-view",children:[e.jsx("div",{className:"SellerCards__header",children:e.jsx("h3",{className:"SellerCards__subtitle",children:"Add New Card"})}),e.jsx("div",{className:"SellerCards__form",children:e.jsxs("form",{onSubmit:A,children:[e.jsx("div",{className:"SellerCards__form-row",children:e.jsx("div",{className:"SellerCards__input-field SellerCards__input-field--full",children:e.jsxs("div",{className:"SellerCards__input-container",children:[e.jsx("div",{className:"SellerCards__input-icon"}),e.jsx("input",{type:"text",id:"nameOnCard",name:"nameOnCard",value:f.nameOnCard,onChange:a=>O(a.target.value),placeholder:"Name on card",required:!0,className:"SellerCards__input",disabled:c})]})})}),e.jsx("div",{className:"SellerCards__form-row",children:e.jsx("div",{className:"SellerCards__input-field SellerCards__input-field--full",children:e.jsxs("div",{className:"SellerCards__input-container",children:[e.jsx("div",{className:"SellerCards__input-icon"}),e.jsx("div",{ref:C,className:"SellerCards__stripe-element"})]})})}),e.jsxs("div",{className:"SellerCards__form-actions",children:[e.jsx("button",{type:"submit",className:"btn-primary",disabled:c||!p,children:c?"Adding Card...":"Add Card"}),e.jsx("button",{className:"SellerCards__cancel-btn",onClick:_,disabled:c,children:"Cancel"})]})]})})]})})})};export{ee as default};
